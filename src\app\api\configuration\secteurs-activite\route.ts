// src/app/api/configuration/secteurs-activite/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { SecteurActiviteSchema } from "@/lib/schemas/secteur-activite.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET: Lister tous les secteurs d'activité
export async function GET(request: Request) {
  console.log("API SecteursActivite GET appelée");
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const secteurs = await prisma.secteurActivite.findMany({
        orderBy: { nom: "asc" },
        include: {
          utilisateurCreation: { select: { nomUtilisateur: true }},
          utilisateurModification: { select: { nomUtilisateur: true }},
        }
      });
      return NextResponse.json(secteurs);
    } catch (error) {
      console.error("Erreur GET /api/configuration/secteurs-activite:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// POST: Créer un nouveau secteur d'activité
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: creatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = SecteurActiviteSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { nom, code, description } = validation.data;

      // Vérifier l'unicité du nom
      const existingNom = await prisma.secteurActivite.findUnique({ where: { nom } });
      if (existingNom) {
        return NextResponse.json({ message: "Un secteur d'activité avec ce nom existe déjà." }, { status: 409 });
      }

      // Vérifier l'unicité du code s'il est fourni
      if (code && code.trim() !== "") {
        const existingCode = await prisma.secteurActivite.findUnique({ where: { code } });
        if (existingCode) {
          return NextResponse.json({ message: "Un secteur d'activité avec ce code existe déjà." }, { status: 409 });
        }
      }

      const data: any = {
        nom,
        code: (code && code.trim() !== "") ? code : null, // Mettre null si vide
        description,
      };
      if (creatorId !== undefined) {
        data.utilisateurCreationId = creatorId;
      }

      const newSecteur = await prisma.secteurActivite.create({
        data,
      });
      return NextResponse.json(newSecteur, { status: 201 });
    } catch (error) {
      console.error("Erreur POST /api/configuration/secteurs-activite:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}