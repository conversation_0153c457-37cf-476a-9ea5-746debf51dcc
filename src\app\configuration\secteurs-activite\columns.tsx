// src/app/configuration/secteurs-activite/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { SecteurActivite, Utilisateur } from "@prisma/client";
import { ArrowUpDown, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";

export type SecteurActiviteColumn = SecteurActivite & {
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
    utilisateurModification?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface SecteurActiviteColumnsProps {
    onEdit: (secteur: SecteurActiviteColumn) => void;
    onDelete: (secteur: SecteurActiviteColumn) => void;
}

export const getSecteurActiviteColumns = ({ onEdit, onDelete }: SecteurActiviteColumnsProps): ColumnDef<SecteurActiviteColumn>[] => [
  {
    accessorKey: "nom",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Nom <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "code",
    header: "Code",
    cell: ({ row }) => row.getValue("code") || <span className="text-muted-foreground">-</span>,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => <span className="text-sm text-muted-foreground truncate block max-w-md">{row.getValue("description") || "-"}</span>,
  },
  {
    accessorKey: "dateCreation",
    header: "Créé le",
    cell: ({ row }) => new Date(row.getValue("dateCreation")).toLocaleDateString("fr-FR"),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const secteur = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button variant="ghost" size="icon" onClick={() => onEdit(secteur)} title="Modifier">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(secteur)} className="text-red-600 hover:text-red-700" title="Supprimer">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];