// src/lib/schemas/partenaire.schema.ts
import { z } from "zod";
import { TypePartenaire } from "@prisma/client"; // Importer l'enum depuis Prisma

// Convertir l'enum Prisma en un tableau de chaînes pour Zod
const typePartenaireValues = Object.values(TypePartenaire) as [string, ...string[]];

export const PartenaireSchema = z.object({
  nom: z.string().min(2, "Le nom du partenaire doit contenir au moins 2 caractères.").max(150),
  typePartenaire: z.enum(typePartenaireValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un type de partenaire valide." }),
  }),
  description: z.string().optional().or(z.literal('')),
  convention: z.string().optional().or(z.literal('')),

  // Champs pour Contact (similaire aux Bailleurs)
  contactNomRepresentant: z.string().optional().or(z.literal('')),
  contactAdresse: z.string().optional().or(z.literal('')), // Ajout spécifique pour Partenaire
  contactEmail: z.string().email({ message: "Email de contact invalide." }).optional().or(z.literal('')),
  contactTelephone: z.string().optional().or(z.literal('')),

  // Nouveaux champs pour la saisie améliorée
  numero_agrement: z.string().optional().or(z.literal('')),
  date_creation_entite: z.string().optional().or(z.literal('')),

  // autresInformationsStr sera généré automatiquement
  autresInformationsStr: z.string().optional().or(z.literal(''))
    .refine((val) => {
        if (!val || val.trim() === "") return true;
        try {
            JSON.parse(val);
            return true;
        } catch (e) {
            return false;
        }
    }, { message: "Le champ 'Autres Informations' doit être un JSON valide s'il est renseigné." }),
}).superRefine((data, ctx) => {
  // Génère autresInformationsStr à partir des deux champs si l'un ou l'autre est rempli
  if ((data.numero_agrement && data.numero_agrement.trim() !== "") || (data.date_creation_entite && data.date_creation_entite.trim() !== "")) {
    const autres = {
      ...(data.numero_agrement && data.numero_agrement.trim() !== "" ? { numero_agrement: data.numero_agrement } : {}),
      ...(data.date_creation_entite && data.date_creation_entite.trim() !== "" ? { date_creation_entite: data.date_creation_entite } : {}),
    };
    data.autresInformationsStr = JSON.stringify(autres);
  }
});

export type PartenaireFormValues = z.infer<typeof PartenaireSchema>;