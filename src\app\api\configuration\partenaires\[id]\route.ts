// src/app/api/configuration/partenaires/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypePartenaire } from "@prisma/client";
import { PartenaireSchema } from "@/lib/schemas/partenaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

interface RouteParams { params: { id: string } }

// GET: Récupérer un partenaire spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  try {
    const partenaire = await prisma.partenaire.findUnique({ where: { id }});
    if (!partenaire) return NextResponse.json({ message: "Partenaire non trouvé" }, { status: 404 });
    return NextResponse.json(partenaire);
  } catch (error) {
    console.error(`Erreur GET /api/configuration/partenaires/${id}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// PUT: Mettre à jour un partenaire
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: modifierId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = PartenaireSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        nom,
        typePartenaire,
        description,
        convention,
        contactNomRepresentant,
        contactAdresse,
        contactEmail,
        contactTelephone,
        autresInformationsStr,
      } = validation.data;

      const contactJson = {
        nomRepresentant: contactNomRepresentant,
        adresse: contactAdresse,
        email: contactEmail,
        telephone: contactTelephone,
      };
      let autresInformationsJson = null;
      if (autresInformationsStr && autresInformationsStr.trim() !== "") {
        try {
          autresInformationsJson = JSON.parse(autresInformationsStr);
        } catch (e) {
          return NextResponse.json({ message: "Format JSON invalide pour 'Autres Informations'." }, { status: 400 });
        }
      }

      const existingPartenaireWithName = await prisma.partenaire.findFirst({
        where: { nom, id: { not: id } },
      });
      if (existingPartenaireWithName) {
        return NextResponse.json({ message: "Un autre partenaire avec ce nom existe déjà." }, { status: 409 });
      }

      const updatedPartenaire = await prisma.partenaire.update({
        where: { id },
        data: {
          nom,
          typePartenaire: typePartenaire as TypePartenaire,
          description,
          convention,
          contact: contactJson,
          autresInformations: autresInformationsJson,
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedPartenaire);
    } catch (error: any) {
      console.error(`Erreur PUT /api/configuration/partenaires/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Partenaire non trouvé" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// DELETE: Supprimer un partenaire
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const deleterId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: deleterId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      // Vérifier si le partenaire est lié à des allocations ou garanties
      const allocationsCount = await prisma.allocationLignePartenaire.count({ where: { partenaireId: id } });
      if (allocationsCount > 0) {
        return NextResponse.json({ message: `Impossible de supprimer ce partenaire car il est lié à ${allocationsCount} allocation(s).` }, { status: 400 });
      }
      // Vous pourriez aussi vérifier les garanties directes si nécessaire, bien qu'elles passent par une allocation.

      await prisma.partenaire.delete({ where: { id } });
      return NextResponse.json({ message: "Partenaire supprimé avec succès" }, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur DELETE /api/configuration/partenaires/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Partenaire non trouvé" }, { status: 404 });
      if (error.code === 'P2003') return NextResponse.json({ message: "Impossible de supprimer ce partenaire car il est référencé ailleurs." }, { status: 400 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}