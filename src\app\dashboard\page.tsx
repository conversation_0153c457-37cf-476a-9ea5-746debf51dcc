// src/app/dashboard/page.tsx
"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation"; // Correction de l'import si nécessaire
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DollarSign, Landmark, Users, FileText, AlertTriangle, CheckCircle, Clock } from "lucide-react"; // Icônes pour les KPIs

// Interface pour les KPIs (pourrait être plus détaillée plus tard)
interface KpiData {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ElementType; // Type pour un composant React (icône)
  bgColorClass?: string; // Classes Tailwind pour la couleur de fond de l'icône
  textColorClass?: string; // Classes Tailwind pour la couleur du texte de l'icône
}

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true); // Pour le chargement initial de la page/session

  // Données statiques pour les KPIs pour l'instant
  // Plus tard, ces données viendront d'une API
  const kpiMetrics: KpiData[] = [
    {
      title: "Lignes de Garantie Actives",
      value: "1.2 Milliards XOF", // Exemple
      description: "Montant total des lignes actives",
      icon: Landmark,
      bgColorClass: "bg-blue-100 dark:bg-blue-900",
      textColorClass: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "Montant Alloué aux Partenaires",
      value: "850 Millions XOF",
      description: "Total alloué sur les lignes actives",
      icon: DollarSign,
      bgColorClass: "bg-green-100 dark:bg-green-900",
      textColorClass: "text-green-600 dark:text-green-400",
    },
    {
      title: "Garanties Octroyées",
      value: "620 Millions XOF",
      description: "Encours total des garanties",
      icon: FileText,
      bgColorClass: "bg-indigo-100 dark:bg-indigo-900",
      textColorClass: "text-indigo-600 dark:text-indigo-400",
    },
    {
      title: "Taux d'Utilisation Global",
      value: "72.94%", // (620 / 850)
      description: "Montant garanti / Montant alloué",
      icon: Users, // Pourrait être un icône de jauge/pourcentage
      bgColorClass: "bg-sky-100 dark:bg-sky-900",
      textColorClass: "text-sky-600 dark:text-sky-400",
    },
  ];

  const alertNotifications = [
    { id: 1, message: "Ligne de garantie 'AFD2022' arrive à expiration dans 15 jours.", type: "warning", icon: AlertTriangle, link: "#" },
    { id: 2, message: "Demande de mainlevée pour GAR-2023-0045 en attente d'approbation.", type: "info", icon: Clock, link: "#" },
    { id: 3, message: "Paiement de commission du Partenaire 'BANQUE X' en retard de 5 jours.", type: "error", icon: AlertTriangle, link: "#" },
    { id: 4, message: "Nouvelle allocation 'LIGNE_BIDC_P01' validée pour le partenaire 'SFD Y'.", type: "success", icon: CheckCircle, link: "#" },
  ];


  useEffect(() => {
    if (status === "loading") {
      setIsLoading(true);
      return;
    }
    if (!session) {
      router.push("/auth/connexion?callbackUrl=/dashboard");
      // setIsLoading(false); // La redirection va démonter le composant
      return;
    }
    // Si la session est chargée et l'utilisateur est authentifié
    setIsLoading(false);
  }, [session, status, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p>Chargement du tableau de bord...</p>
        {/* Vous pouvez ajouter un spinner ici */}
      </div>
    );
  }

  // Si la session n'est toujours pas là après le chargement (devrait être géré par le redirect)
  if (!session) {
    return null; // Ou un message indiquant la redirection
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-6 lg:p-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
          Tableau de Bord GesGar
        </h1>
        <div className="flex items-center space-x-2">
          {/* Placeholder pour un sélecteur de période ou un bouton d'action */}
          {/* <Button>Exporter</Button> */}
        </div>
      </div>

      {/* Section des KPIs */}
      <section>
        <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Aperçu Global</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {kpiMetrics.map((kpi, index) => (
            <Card key={index} className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {kpi.title}
                </CardTitle>
                <div className={`p-2 rounded-full ${kpi.bgColorClass}`}>
                    <kpi.icon className={`h-5 w-5 ${kpi.textColorClass}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{kpi.value}</div>
                {kpi.description && (
                  <p className="text-xs text-muted-foreground pt-1">
                    {kpi.description}
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Section Alertes et Notifications (Placeholder) */}
      <section className="pt-6">
         <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Alertes et Notifications</h2>
         <div className="space-y-3">
            {alertNotifications.length > 0 ? alertNotifications.map(notif => (
                <Card key={notif.id} className={`shadow-sm border-l-4 ${
                    notif.type === 'error' ? 'border-red-500' :
                    notif.type === 'warning' ? 'border-yellow-500' :
                    notif.type === 'info' ? 'border-blue-500' :
                    'border-green-500' // success
                }`}>
                    <CardContent className="p-4 flex items-start space-x-3">
                        <div className={`p-1.5 rounded-full mt-1 ${
                            notif.type === 'error' ? 'bg-red-100 text-red-600' :
                            notif.type === 'warning' ? 'bg-yellow-100 text-yellow-600' :
                            notif.type === 'info' ? 'bg-blue-100 text-blue-600' :
                            'bg-green-100 text-green-600'
                        }`}>
                            <notif.icon className="h-5 w-5" />
                        </div>
                        <div>
                            <p className="text-sm font-medium">{notif.message}</p>
                            {notif.link && notif.link !== "#" && (
                                <a href={notif.link} className="text-xs text-blue-600 hover:underline">Voir détails</a>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )) : (
                <p className="text-muted-foreground">Aucune notification pour le moment.</p>
            )}
         </div>
      </section>

      {/* Section Graphiques (Placeholder) */}
      <section className="pt-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Visualisations</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>Répartition des Garanties par Secteur</CardTitle>
              <CardDescription>Placeholder pour un graphique circulaire ou à barres.</CardDescription>
            </CardHeader>
            <CardContent className="h-64 flex items-center justify-center bg-slate-50 dark:bg-slate-800 rounded-b-md">
              <p className="text-muted-foreground">[Graphique ici]</p>
            </CardContent>
          </Card>
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>Évolution des Encours</CardTitle>
              <CardDescription>Placeholder pour un graphique linéaire.</CardDescription>
            </CardHeader>
            <CardContent className="h-64 flex items-center justify-center bg-slate-50 dark:bg-slate-800 rounded-b-md">
              <p className="text-muted-foreground">[Graphique ici]</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Section Tâches en Attente (Placeholder) */}
      <section className="pt-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Mes Tâches en Attente</h2>
        <Card className="shadow-sm">
            <CardContent className="p-6">
                {/* Exemple de tâche */}
                <div className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div>
                        <p className="font-medium">Valider la demande de garantie GAR-2023-0112</p>
                        <p className="text-xs text-muted-foreground">Soumise par Partenaire Y - Client Z</p>
                    </div>
                    <Button size="sm" variant="outline">Traiter</Button>
                </div>
                <div className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div>
                        <p className="font-medium">Approuver la mainlevée pour GAR-2022-0088</p>
                        <p className="text-xs text-muted-foreground">Remboursement total confirmé</p>
                    </div>
                    <Button size="sm" variant="outline">Traiter</Button>
                </div>
                <p className="text-muted-foreground mt-4 text-center">Aucune autre tâche urgente.</p>
            </CardContent>
        </Card>
      </section>
    </div>
  );
}