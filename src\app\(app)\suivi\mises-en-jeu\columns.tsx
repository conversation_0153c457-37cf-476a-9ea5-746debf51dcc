// src/app/(app)/suivi/mises-en-jeu/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MiseEnJeu, Garantie, Projet, ClientBeneficiaire, AllocationLignePartenaire, Partenaire, LigneGarantie, Utilisateur } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowUpDown, Edit, Eye, CreditCard } from "lucide-react"; // Edit pour "Traiter", Eye pour voir détails garantie, CreditCard pour paiement
import Link from "next/link";
import { StatutMiseEnJeu } from "@/types/enums";

// Type enrichi pour la colonne, incluant les relations nécessaires pour l'affichage
export type MiseEnJeuDemandeColumn = MiseEnJeu & {
  garantie: Pick<Garantie, "id" | "referenceGarantie" | "montantGarantie"> & {
    projet: Pick<Projet, "id"> & {
      clientBeneficiaire: Pick<ClientBeneficiaire, "id" | "nomOuRaisonSociale">;
    };
    allocation: Pick<AllocationLignePartenaire, "id"> & {
      partenaire: Pick<Partenaire, "id" | "nom">;
      ligneGarantie: Pick<LigneGarantie, "id" | "devise">; // Pour la devise des montants
    };
  };
  utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur" | "nom" | "prenom"> | null; // Qui a demandé
  utilisateurModification?: Pick<Utilisateur, "nomUtilisateur" | "nom" | "prenom"> | null; // Qui a traité
};

interface MiseEnJeuDemandeColumnsProps {
    onProcess: (demande: MiseEnJeuDemandeColumn) => void; // Pour ouvrir le formulaire de décision
    onPayment?: (demande: MiseEnJeuDemandeColumn) => void; // Pour ouvrir le formulaire de paiement
}

const formatCurrency = (amount: any, currency: string = "XOF") => {
    if (amount === null || amount === undefined) return "-";
    const num = typeof amount === 'string' ? parseFloat(amount.replace(',', '.')) : amount;
    if (isNaN(num)) return "-";
    return new Intl.NumberFormat("fr-FR", { style: "currency", currency: currency, minimumFractionDigits: 0 }).format(num);
};

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

const getStatutMiseEnJeuBadgeVariant = (statut: string | undefined) => {
    switch (statut) {
        case "Demandee": return "bg-blue-100 text-blue-700 border-blue-300";
        case "EnCoursInstruction": return "bg-yellow-100 text-yellow-700 border-yellow-300";
        case "ApprouveeTotalement": case "ApprouveePartiellement": return "bg-teal-100 text-teal-700 border-teal-300";
        case "EnAttentePaiement": return "bg-sky-100 text-sky-700 border-sky-300";
        case "Payee": return "bg-green-100 text-green-700 border-green-300";
        case "Refusee": return "bg-red-100 text-red-700 border-red-300";
        default: return "bg-slate-100 text-slate-700 border-slate-300";
    }
};

export const getMiseEnJeuDemandeColumns = ({ onProcess, onPayment }: MiseEnJeuDemandeColumnsProps): ColumnDef<MiseEnJeuDemandeColumn>[] => [
  {
    accessorKey: "id",
    header: "ID Demande",
    cell: ({row}) => <span className="font-mono text-xs">{row.getValue("id")}</span>
  },
  {
    accessorKey: "garantie.referenceGarantie",
    header: "Réf. Garantie",
    cell: ({ row }) => (
        <Link
          href={"/garanties/" + row.original.garantie.id.toString() as any}
          className="hover:underline text-blue-600"
          title="Voir détails de la garantie"
        >
            {row.original.garantie.referenceGarantie}
        </Link>
    )
  },
  {
    accessorKey: "garantie.projet.clientBeneficiaire.nomOuRaisonSociale",
    header: "Client Bénéficiaire",
    cell: ({ row }) => row.original.garantie.projet.clientBeneficiaire.nomOuRaisonSociale,
  },
  {
    accessorKey: "garantie.allocation.partenaire.nom",
    header: "Partenaire",
    cell: ({ row }) => row.original.garantie.allocation.partenaire.nom,
  },
  {
    accessorKey: "montantDemande",
    header: "Montant Demandé",
    cell: ({ row }) => formatCurrency(row.getValue("montantDemande"), row.original.garantie.allocation.ligneGarantie.devise),
  },
  { // Montant Approuvé (si déjà décidé)
    accessorKey: "montantApprouve",
    header: "Montant Approuvé",
    cell: ({ row }) => formatCurrency(row.getValue("montantApprouve"), row.original.garantie.allocation.ligneGarantie.devise),
  },
  { // Montant Payé (si déjà payé)
    accessorKey: "montantPaye",
    header: "Montant Payé",
    cell: ({ row }) => formatCurrency(row.getValue("montantPaye"), row.original.garantie.allocation.ligneGarantie.devise),
  },
  {
    accessorKey: "dateDemande",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Date Demande <ArrowUpDown className="ml-1 h-3 w-3"/> </Button> ),
    cell: ({ row }) => formatDate(row.getValue("dateDemande")),
  },
  {
    accessorKey: "statut",
    header: "Statut Demande",
    cell: ({ row }) => <Badge className={getStatutMiseEnJeuBadgeVariant(row.getValue("statut"))}>{row.getValue("statut")}</Badge>,
  },
  {
    accessorKey: "utilisateurCreation.nomUtilisateur",
    header: "Demandeur",
    cell: ({ row }) => row.original.utilisateurCreation?.nomUtilisateur || "N/A",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const demande = row.original;
      
      // Vérifier si la demande peut être traitée (décision)
      const canProcess = [
        "Demandee",
        "EnCoursInstruction",
        "ApprouveePartiellement",
        "ApprouveeTotalement",
        "EnAttentePaiement",
      ].includes(demande.statut);

      // Vérifier si la demande peut être payée
      const canPay = onPayment && [
        "ApprouveeTotalement",
        "ApprouveePartiellement",
        "EnAttentePaiement",
      ].includes(demande.statut);

      if (canProcess || canPay) {
        return (
          <div className="flex gap-2">
            {canProcess && (
              <Button variant="outline" size="sm" onClick={() => onProcess(demande)}>
                <Edit className="mr-2 h-4 w-4" /> Traiter / Voir
              </Button>
            )}
            {canPay && (
              <Button variant="outline" size="sm" onClick={() => onPayment?.(demande)} className="bg-green-50 hover:bg-green-100 border-green-200">
                <CreditCard className="mr-2 h-4 w-4" /> Enregistrer Paiement
              </Button>
            )}
          </div>
        );
      }
      
      // Pour les statuts finaux comme Payee ou Refusee
      return <span className="text-xs text-muted-foreground">Traitée</span>;
    },
  },
];