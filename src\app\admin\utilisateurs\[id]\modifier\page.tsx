// src/app/admin/utilisateurs/[id]/modifier/page.tsx
"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation"; // useParams pour récupérer [id]
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UpdateUserSchema, UpdateUserFormValues } from "@/lib/schemas/user.schema";
import { RoleUtilisateur, Utilisateur } from "@prisma/client"; // Importer Utilisateur pour le type

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react";

// Type pour les données utilisateur récupérées (sans mot de passe)
type UserData = Omit<Utilisateur, "motDePasse" | "utilisateurCreationId" | "utilisateurModificationId" | "dateModification">;


const roleDisplayOptions = [
    { value: RoleUtilisateur.Administrateur, label: "Administrateur" },
    { value: RoleUtilisateur.GestionnaireGesGar, label: "Gestionnaire GesGar" },
    { value: RoleUtilisateur.AnalysteFinancier, label: "Analyste Financier" },
    { value: RoleUtilisateur.Partenaire, label: "Partenaire (Externe)" },
    { value: RoleUtilisateur.Bailleur, label: "Bailleur (Externe)" },
    { value: RoleUtilisateur.Auditeur, label: "Auditeur" },
];

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams(); // Pour obtenir l'ID de l'URL
  const userId = params.id as string; // L'ID est une chaîne ici
  const { data: session, status: sessionStatus } = useSession();
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  const form = useForm<UpdateUserFormValues>({
    resolver: zodResolver(UpdateUserSchema),
    defaultValues: { // Sera peuplé par useEffect
      nomUtilisateur: "",
      email: "",
      nom: "",
      prenom: "",
      role: undefined,
      motDePasse: "", // Laisser vide par défaut pour ne pas l'afficher
    },
  });

  useEffect(() => {
    if (sessionStatus === "loading") return;
    if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
      toast.error("Accès refusé");
      router.replace("/");
      return;
    }

    if (userId) {
      setIsLoadingUser(true);
      fetch(`/api/admin/users/${userId}`)
        .then(res => {
          if (!res.ok) throw new Error("Utilisateur non trouvé ou erreur serveur");
          return res.json();
        })
        .then((data: UserData) => {
          form.reset({ // Pré-remplir le formulaire
            nomUtilisateur: data.nomUtilisateur,
            email: data.email,
            nom: data.nom,
            prenom: data.prenom,
            role: data.role,
            motDePasse: "", // Important: ne pas pré-remplir le mot de passe
          });
          setIsLoadingUser(false);
        })
        .catch(error => {
          console.error("Erreur de chargement de l'utilisateur:", error);
          toast.error("Impossible de charger les données de l'utilisateur.");
          router.push("/admin/utilisateurs");
        });
    }
  }, [userId, form, router, toast, session, sessionStatus]);


  const onSubmit = async (values: UpdateUserFormValues) => {
    // Si le champ motDePasse est vide, ne pas l'envoyer dans la requête ou le traiter côté API
    const dataToSubmit: Partial<UpdateUserFormValues> = { ...values };
    if (!values.motDePasse || values.motDePasse.trim() === "") {
      delete dataToSubmit.motDePasse;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(dataToSubmit),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec de la mise à jour de l'utilisateur");
      }

      toast.success(`L'utilisateur ${values.nomUtilisateur} a été modifié avec succès.`);
      router.push("/admin/utilisateurs");
      router.refresh();
    } catch (error: any) {
      console.error("Erreur de soumission:", error);
      toast.error(error.message || "Une erreur s'est produite.");
    }
  };

  if (sessionStatus === "loading" || isLoadingUser || !session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return <div className="p-6">Chargement ou vérification des droits...</div>;
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0 max-w-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Modifier l'Utilisateur</h1>
        <Button variant="outline" asChild>
            <Link href="/admin/utilisateurs">Retour à la liste</Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 bg-white p-6 shadow-md rounded-lg">
          {/* Les champs sont identiques à ceux du formulaire d'ajout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="prenom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prénom</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="nomUtilisateur"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom d'utilisateur</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl><Input type="email" {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="motDePasse"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nouveau mot de passe (optionnel)</FormLabel>
                <FormControl><Input type="password" placeholder="Laisser vide pour ne pas changer" {...field} /></FormControl>
                <FormDescription>
                  Si fourni, au moins 8 caractères.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rôle</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}> {/* 'value' au lieu de 'defaultValue' pour un champ contrôlé */}
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un rôle" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {roleDisplayOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={() => router.push('/admin/utilisateurs')}>
              Annuler
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? "Mise à jour..." : "Mettre à jour l'Utilisateur"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}