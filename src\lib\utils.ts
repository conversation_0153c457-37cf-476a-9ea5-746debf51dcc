import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumberWithThousandsSeparator(number: number | string | undefined | null, locale: string = 'fr-FR', options?: Intl.NumberFormatOptions): string {
  if (number === undefined || number === null || number === '') return "-";
  const num = typeof number === 'string' ? parseFloat(number.replace(',', '.')) : number;
  if (isNaN(num)) return "-";
  return num.toLocaleString(locale, options);
}
