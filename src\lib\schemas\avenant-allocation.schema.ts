// src/lib/schemas/avenant-allocation.schema.ts
import { z } from "zod";
import { TypeAvenant } from "@prisma/client"; // Réutiliser l'enum TypeAvenant

const typeAvenantValues = Object.values(TypeAvenant) as [string, ...string[]];

export const AvenantAllocationSchema = z.object({
  // allocationId sera fourni par le contexte (URL), non par le formulaire direct
  typeAvenant: z.enum(typeAvenantValues, {
    required_error: "Le type d'avenant est requis.",
    invalid_type_error: "Type d'avenant invalide.",
  }),
  montantModificationStr: z.string().optional().or(z.literal('')), // String pour le form, converti en Decimal
  nouvelleDateExpiration: z.date().optional(), // Optionnel, dépend du typeAvenant
  dateAvenant: z.date({
    required_error: "La date de l'avenant est requise.",
    invalid_type_error: "Format de date d'avenant invalide.",
  }),
  raison: z.string().min(5, "La raison doit contenir au moins 5 caractères.").max(1000),
  referenceDocument: z.string().max(100).optional().or(z.literal('')),
})
// Validations conditionnelles
.refine(data => {
    if (data.typeAvenant === TypeAvenant.AUGMENTATION_MONTANT || data.typeAvenant === TypeAvenant.REDUCTION_MONTANT) {
        if (!data.montantModificationStr || data.montantModificationStr.trim() === "") return false;
        const montant = parseFloat(data.montantModificationStr.replace(',', '.'));
        return !isNaN(montant); // Doit être un nombre (positif ou négatif sera géré par l'API)
    }
    return true;
}, {
    message: "Le montant de modification est requis et doit être un nombre pour ce type d'avenant.",
    path: ["montantModificationStr"],
})
.refine(data => {
    if (data.typeAvenant === TypeAvenant.PROLONGATION_DUREE) {
        return data.nouvelleDateExpiration !== undefined && data.nouvelleDateExpiration !== null;
    }
    return true;
}, {
    message: "La nouvelle date d'expiration est requise pour une prolongation de durée.",
    path: ["nouvelleDateExpiration"],
});

export type AvenantAllocationFormValues = Omit<z.infer<typeof AvenantAllocationSchema>, "allocationId">;
// On omet allocationId du type de formulaire car il viendra du contexte.