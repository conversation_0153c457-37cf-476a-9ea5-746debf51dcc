// src/app/auth/layout.tsx
import type { Metadata } from "next";
import "./auth.css";
import { getServerSession } from "next-auth/next";
import { authOptions } from "src/app/api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Authentification - GesGar",
  description: "Connectez-vous ou créez votre compte GesGar",
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 auth-bg-pattern">
      {/* Conteneur principal sans sidebar */}
      <main className="relative overflow-hidden">
        {/* Motif de fond décoratif animé */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Cercles décoratifs avec animation subtile */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/10 dark:bg-blue-400/5 rounded-full blur-3xl animate-pulse"></div>
          <div 
            className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400/10 dark:bg-indigo-400/5 rounded-full blur-3xl animate-pulse" 
            style={{ animationDelay: '1s' }}
          ></div>
          <div 
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-400/5 dark:bg-purple-400/3 rounded-full blur-3xl animate-pulse" 
            style={{ animationDelay: '2s' }}
          ></div>
          
          {/* Grille subtile en arrière-plan */}
          <div className="absolute inset-0 opacity-30 dark:opacity-10">
            <div className="w-full h-full bg-gradient-to-r from-transparent via-slate-200/20 to-transparent dark:via-slate-700/20"></div>
          </div>
        </div>
        
        {/* Contenu de la page avec animation */}
        <div className="relative z-10 auth-fade-in-up">
          {children}
        </div>
      </main>
    </div>
  );
}