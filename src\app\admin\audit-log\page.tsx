// src/app/admin/audit-log/page.tsx
"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { RoleUtilisateur } from "@prisma/client";
import { columns, AuditLogColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DateRangePicker } from "@/components/shared/date-range-picker";
import { DateRange } from "react-day-picker";
import { useToast } from "@/components/ui/use-toast";
import { PaginationControls } from "@/components/shared/pagination-controls"; // À créer

// Si DataTable n'est pas dans shared, ajustez le chemin:
// import { DataTable } from "../utilisateurs/data-table";

interface AuditLogResponse {
  data: AuditLogColumn[];
  totalPages: number;
  currentPage: number;
  totalRecords: number;
}

export default function AuditLogPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [logs, setLogs] = useState<AuditLogColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);

  // États pour les filtres
  // États internes pour les filtres (initialisés une seule fois)
  interface Filters {
    utilisateurId: string;
    action: string;
    entite: string;
    dateRange?: DateRange;
  }

  const [filters, setFilters] = useState<Filters>(() => ({
    utilisateurId: searchParams.get("utilisateurId") || "",
    action: searchParams.get("action") || "",
    entite: searchParams.get("entite") || "",
    dateRange: (() => {
      const debut = searchParams.get("dateDebut");
      const fin = searchParams.get("dateFin");
      if (debut && fin) {
        return { from: new Date(debut), to: new Date(fin) };
      }
      return undefined;
    })()
  }));

  const fetchAuditLogs = useCallback(async (pageToFetch = 1, shouldUpdateUrl = false) => {
    setIsLoading(true);
    
    // Préparer les paramètres sans toucher à l'URL immédiatement
    const params = new URLSearchParams();
    params.append("page", pageToFetch.toString());
    params.append("limit", "15");

    if (filters.utilisateurId) params.append("utilisateurId", filters.utilisateurId);
    if (filters.action) params.append("action", filters.action);
    if (filters.entite) params.append("entite", filters.entite);
    if (filters.dateRange?.from) params.append("dateDebut", filters.dateRange.from.toISOString().split('T')[0]);
    if (filters.dateRange?.to) params.append("dateFin", filters.dateRange.to.toISOString().split('T')[0]);

    // Mise à jour de l'URL seulement si explicitement demandée
    if (shouldUpdateUrl) {
      const newUrl = `${pathname}?${params.toString()}`;
      if (window.location.search !== params.toString()) {
        router.replace(newUrl, { scroll: false });
      }
    }

    try {
      const response = await fetch(`/api/admin/audit-log?${params.toString()}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec de la récupération des logs");
      }
      const data: AuditLogResponse = await response.json();
      setLogs(data.data);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
      setTotalRecords(data.totalRecords);
    } catch (error: any) {
      console.error("Erreur fetchAuditLogs:", error);
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [filters, toast, router, pathname]);


  // Effet principal - s'exécute une seule fois au montage
  useEffect(() => {
    if (sessionStatus === "loading") return;
    
    if (!session || (session.user?.role !== RoleUtilisateur.Administrateur && session.user?.role !== RoleUtilisateur.Auditeur)) {
      toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/");
      return;
    }

    // Récupération initiale avec les paramètres de l'URL
    const pageFromUrl = parseInt(searchParams.get("page") || "1");
    fetchAuditLogs(pageFromUrl, false);
  }, [session, sessionStatus]);

  // Effet pour gérer les changements de filtres
  useEffect(() => {
    if (sessionStatus !== "authenticated") return;
    const timer = setTimeout(() => {
      fetchAuditLogs(1, true); // Mettre à jour l'URL après un délai
    }, 300);
    return () => clearTimeout(timer);
  }, [filters]);


  const handleFilterSubmit = () => {
    // Pas besoin d'appeler fetchAuditLogs ici, l'effet sur filters le fera
    // On pourrait ajouter un indicateur visuel de chargement si nécessaire
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchAuditLogs(newPage, true);
  };

  if (sessionStatus === "loading" || isLoading) {
    return <div className="p-6">Chargement des journaux d'audit...</div>;
  }
  if (!session || (session.user?.role !== RoleUtilisateur.Administrateur && session.user?.role !== RoleUtilisateur.Auditeur)) {
    return <div className="p-6">Accès non autorisé.</div>;
  }

  // TODO: Récupérer la liste des utilisateurs pour le filtre Utilisateur
  // const [allUsers, setAllUsers] = useState<{id: number, nomUtilisateur: string}[]>([]);
  // useEffect(() => { fetch('/api/admin/users?all=true').then(res=>res.json()).then(setAllUsers); }, []);

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <h1 className="text-3xl font-bold mb-6">Journal d'Audit</h1>

      <div className="mb-6 p-4 border rounded-lg bg-card shadow">
        <h2 className="text-lg font-semibold mb-3">Filtres</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
          <Input
            placeholder="ID Utilisateur"
            value={filters.utilisateurId}
            onChange={(e) => setFilters(prev => ({...prev, utilisateurId: e.target.value}))}
            type="number"
          />
          {/* Alternative: Select pour l'utilisateur
          <Select value={utilisateurIdFilter} onValueChange={setUtilisateurIdFilter}>
            <SelectTrigger><SelectValue placeholder="Filtrer par utilisateur..." /></SelectTrigger>
            <SelectContent>
              <SelectItem value="">Tous les utilisateurs</SelectItem>
              {allUsers.map(user => <SelectItem key={user.id} value={user.id.toString()}>{user.nomUtilisateur}</SelectItem>)}
            </SelectContent>
          </Select>
          */}
          <Input
            placeholder="Action (ex: CREATE, UPDATE)"
            value={filters.action}
            onChange={(e) => setFilters(prev => ({...prev, action: e.target.value}))}
          />
          <Input
            placeholder="Entité (ex: Utilisateur, Bailleur)"
            value={filters.entite}
            onChange={(e) => setFilters(prev => ({...prev, entite: e.target.value}))}
          />
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-muted-foreground mb-1">Période</label>
            <DateRangePicker
              date={filters.dateRange}
              onDateChange={(range) => setFilters(prev => ({...prev, dateRange: range}))}
            />
          </div>
          <Button onClick={handleFilterSubmit} className="w-full md:w-auto">Appliquer les Filtres</Button>
        </div>
      </div>

      {/* Assurez-vous que DataTable est bien importé et configuré */}
      {/* Si vous avez créé DataTable dans /admin/utilisateurs, vous pourriez vouloir le déplacer vers un dossier partagé */}
      {/* ex: src/components/shared/data-table.tsx */}
      <DataTable columns={columns} data={logs} />

      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        totalRecords={totalRecords}
      />
    </div>
  );
}