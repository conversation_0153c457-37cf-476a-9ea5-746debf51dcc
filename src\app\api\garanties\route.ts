// src/app/api/garanties/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { GarantieSchema } from "@/lib/schemas/garantie.schema";
import { withAudit, BusinessAction } from '@/lib/audit-wrapper';
import { Decimal } from "@prisma/client/runtime/library";
import { StatutAllocation, RoleUtilisateur } from "@/types/enums";
import { StatutGarantie, TypeGarantie } from "@prisma/client";

// Fonction pour générer une référence de garantie unique
async function generateReferenceGarantie(): Promise<string> {
    const year = new Date().getFullYear();
    // Trouver le dernier compteur pour l'année en cours
    const lastGarantie = await prisma.garantie.findFirst({
        where: { referenceGarantie: { startsWith: `GAR-${year}-` } },
        orderBy: { dateCreation: 'desc' }, // ou id si dateCreation n'est pas assez précis
    });

    let nextId = 1;
    if (lastGarantie && lastGarantie.referenceGarantie) {
        const parts = lastGarantie.referenceGarantie.split('-');
        const lastIdInYear = parseInt(parts[parts.length - 1]);
        if (!isNaN(lastIdInYear)) {
            nextId = lastIdInYear + 1;
        }
    }
    return `GAR-${year}-${nextId.toString().padStart(4, '0')}`;
}


// GET: Lister toutes les garanties (avec filtres de base si besoin)
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  // Ajuster les rôles autorisés selon les besoins
  if (!session || !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  // TODO: Ajouter des filtres (par partenaire, statut, etc.) via searchParams
  // Pour les Partenaires, ne retourner que leurs propres garanties
  const whereClause: any = {};
  if (session.user?.role === RoleUtilisateur.Partenaire) {
    // Sécurité: Bloquer l'accès tant que le filtrage n'est pas implémenté correctement
    return NextResponse.json({ message: "Accès interdit: filtrage par partenaire non implémenté." }, { status: 403 });
    // Si vous implémentez le filtrage, remplacez le bloc ci-dessus par :
    // whereClause.partenaireId = /* ID du partenaire de l'utilisateur */;
  }
  // Exclure les garanties supprimées
  whereClause.statut = { not: StatutGarantie.Supprimee };

  try {
    const garanties = await prisma.garantie.findMany({
      where: whereClause,
      orderBy: { dateCreation: "desc" },
      include: {
        allocation: { include: { partenaire: {select: {id: true, nom: true}}, ligneGarantie: {select: {id: true, nom: true, devise: true}} } },
        projet: { include: { clientBeneficiaire: {select: {id: true, nomOuRaisonSociale: true}}, secteurActivite: {select: {id: true, nom: true}} } },
        // partenaire: { select: { id: true, nom: true } }, // Redondant si inclus via allocation
        // clientBeneficiaire: { select: { id: true, nomOuRaisonSociale: true } }, // Redondant si inclus via projet
        utilisateurCreation: { select: { nomUtilisateur: true }},
      }
    });
    return NextResponse.json(garanties);
  } catch (error) {
    console.error("Erreur GET /api/garanties:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// POST: Créer une nouvelle garantie (Octroi de Garantie)
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  // Typiquement, un GestionnaireGesGar ou un Admin crée la garantie après instruction.
  // Un Partenaire pourrait soumettre une "demande" qui passe par un workflow.
  // Pour l'instant, on autorise Admin et GestionnaireGesGar à créer directement.
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé à créer une garantie" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;

  return withAudit(async () => {
    try {
      const body = await request.json();
      // Conversion robuste des dates string en objets Date avant la validation Zod
      [
        "dateOctroiCredit",
        "dateDemandeGarantie",
        "dateEcheanceInitialeCredit",
        "dateEcheanceGarantie"
      ].forEach((key) => {
        if (body[key]) {
          const d = new Date(body[key]);
          body[key] = isNaN(d.getTime()) ? undefined : d;
        }
      });

      const validation = GarantieSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides pour la garantie", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        allocationId, projetId, typeGarantie, montantCreditStr, tauxCouvertureAppliqueStr,
        dateOctroiCredit, dateDemandeGarantie, dateEcheanceInitialeCredit, dateEcheanceGarantie,
        statut, identifiantCreditPartenaire, conditionsParticulieres, delaiMiseEnJeu
      } = validation.data;

      if (!dateEcheanceInitialeCredit) {
        return NextResponse.json({ message: "dateEcheanceInitialeCredit manquant" }, { status: 400 });
      }
      if (!dateEcheanceGarantie) {
        return NextResponse.json({ message: "dateEcheanceGarantie manquant" }, { status: 400 });
      }

      const allocationIdNum = parseInt(allocationId);
      const projetIdNum = parseInt(projetId);
      const montantCreditDecimal = new Decimal(montantCreditStr.replace(',', '.'));
      // On stocke le taux comme "50.00" pour 50%
      const tauxCouvertureAppliqueDecimal = new Decimal(tauxCouvertureAppliqueStr.replace(',', '.'));

      // --- Début de la logique transactionnelle ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'allocation et ses informations liées
        const allocation = await tx.allocationLignePartenaire.findUnique({
          where: { id: allocationIdNum },
          include: { ligneGarantie: true, partenaire: true }
        });
        if (!allocation) throw new Error("Allocation sélectionnée non valide ou non trouvée.");
        if (allocation.statut !== StatutAllocation.Active) { // Seules les allocations actives peuvent être utilisées
            throw new Error(`L'allocation sélectionnée (ID: ${allocation.id}) n'est pas active. Statut actuel: ${allocation.statut}`);
        }


        // 2. Récupérer le projet et son client
        const projet = await tx.projet.findUnique({
          where: { id: projetIdNum },
          include: { clientBeneficiaire: true }
        });
        if (!projet) throw new Error("Projet sélectionné non valide ou non trouvé.");

        // 3. Calculer le montant de la garantie
        // Si tauxCouvertureAppliqueDecimal stocke 50.00 pour 50%, il faut diviser par 100
        const montantGarantieDecimal = montantCreditDecimal.mul(tauxCouvertureAppliqueDecimal.div(100));

        // 4. Vérifier la disponibilité sur l'allocation
        if (montantGarantieDecimal.greaterThan(allocation.montantDisponible)) {
          throw new Error(`Montant de garantie (${montantGarantieDecimal.toFixed(2)}) dépasse le montant disponible (${allocation.montantDisponible.toFixed(2)}) sur l'allocation.`);
        }

        // 5. Vérifier si le taux de couverture appliqué respecte le max de l'allocation
        if (allocation.tauxCouvertureMax && tauxCouvertureAppliqueDecimal.greaterThan(allocation.tauxCouvertureMax)) {
            throw new Error(`Le taux de couverture appliqué (${tauxCouvertureAppliqueDecimal}%) dépasse le maximum autorisé (${allocation.tauxCouvertureMax}%) pour cette allocation.`);
        }

        // 6. Générer la référence de garantie
        const referenceGarantie = await generateReferenceGarantie(); // Utilisation de la fonction helper

        // Vérif unicité referenceGarantie (sécurité supplémentaire)
        const exists = await tx.garantie.findFirst({ where: { referenceGarantie } });
        if (exists) throw new Error("referenceGarantie déjà utilisée");

        // 7. Créer la garantie
        const newGarantie = await tx.garantie.create({
          data: {
            referenceGarantie,
            ligneGarantieId: allocation.ligneGarantieId, // Pour référence rapide
            allocationId: allocationIdNum,
            partenaireId: allocation.partenaireId, // Pour référence rapide
            projetId: projetIdNum,
            clientBeneficiaireId: projet.clientBeneficiaireId, // Pour référence rapide
            typeGarantie: typeGarantie as TypeGarantie,
            montantCredit: montantCreditDecimal,
            tauxCouvertureApplique: tauxCouvertureAppliqueDecimal, // Stocké comme 50.00
            montantGarantie: montantGarantieDecimal,
            dateOctroiCredit,
            dateDemandeGarantie,
            // dateAccordGarantie et dateEffetGarantie seront définies plus tard dans le workflow
            dateAccordGarantie: new Date(), // TEMPORAIRE: Pour l'instant, on met la date du jour
            dateEffetGarantie: new Date(),  // TEMPORAIRE: Pour l'instant, on met la date du jour
            dateEcheanceInitialeCredit: dateEcheanceInitialeCredit,
            dateEcheanceGarantie: dateEcheanceGarantie,
            statut: statut as StatutGarantie, // Ex: EnInstruction ou Active si pas de workflow
            identifiantCreditPartenaire,
            conditionsParticulieres,
            delaiMiseEnJeu: parseInt(delaiMiseEnJeu),
            utilisateurCreationId: creatorId,
          },
        });

        // 8. Mettre à jour le montant disponible de l'allocation
        const nouveauMontantDisponibleAllocation = Decimal.max(allocation.montantDisponible.sub(montantGarantieDecimal), new Decimal(0));
        await tx.allocationLignePartenaire.update({
          where: { id: allocationIdNum },
          data: {
            montantDisponible: nouveauMontantDisponibleAllocation,
            utilisateurModificationId: creatorId, // L'allocation est modifiée
          },
        });

        return newGarantie;
      });
      // --- Fin de la logique transactionnelle ---

      return NextResponse.json(result, { status: 201 });

    } catch (error: any) {
      console.error("Erreur POST /api/garanties:", error);
      if (error.message.includes("dépasse le montant disponible") || error.message.includes("non valide") || error.message.includes("non trouvée") || error.message.includes("n'est pas active") || error.message.includes("dépasse le maximum autorisé")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      // Gérer l'erreur P2002 pour referenceGarantie unique (très peu probable si generateReferenceGarantie est correcte)
      if (error.code === 'P2002' && error.meta?.target?.includes('referenceGarantie')) {
        return NextResponse.json({ message: "Erreur de génération de référence unique, veuillez réessayer." }, { status: 409 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la création de la garantie." }, { status: 500 });
    }
  }, {
    module: 'GARANTIES',
    operation: BusinessAction.GARANTIE_CREATE,
    metadata: { creatorId }
  });
}