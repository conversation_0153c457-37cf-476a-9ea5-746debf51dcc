-- DropForeignKey
ALTER TABLE "AllocationLignePartenaire" DROP CONSTRAINT "AllocationLignePartenaire_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "AllocationLignePartenaire" DROP CONSTRAINT "AllocationLignePartenaire_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "AuditLog" DROP CONSTRAINT "AuditLog_utilisateurId_fkey";

-- DropForeignKey
ALTER TABLE "AvenantAllocation" DROP CONSTRAINT "AvenantAllocation_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "AvenantAllocation" DROP CONSTRAINT "AvenantAllocation_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "AvenantLigneGarantie" DROP CONSTRAINT "AvenantLigneGarantie_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "AvenantLigneGarantie" DROP CONSTRAINT "AvenantLigneGarantie_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "Bailleur" DROP CONSTRAINT "Bailleur_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "Bailleur" DROP CONSTRAINT "Bailleur_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "ClientBeneficiaire" DROP CONSTRAINT "ClientBeneficiaire_secteurActivitePrincipalId_fkey";

-- DropForeignKey
ALTER TABLE "ClientBeneficiaire" DROP CONSTRAINT "ClientBeneficiaire_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "ClientBeneficiaire" DROP CONSTRAINT "ClientBeneficiaire_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_allocationId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_clientBeneficiaireId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_garantieId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_ligneGarantieId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_mainleveeId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_miseEnJeuId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_paiementId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_partenaireId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_projetId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_transfertGarantieId_fkey";

-- DropForeignKey
ALTER TABLE "Garantie" DROP CONSTRAINT "Garantie_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "Garantie" DROP CONSTRAINT "Garantie_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "GroupeRegleEligibilite" DROP CONSTRAINT "GroupeRegleEligibilite_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "GroupeRegleEligibilite" DROP CONSTRAINT "GroupeRegleEligibilite_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "LigneGarantie" DROP CONSTRAINT "LigneGarantie_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "LigneGarantie" DROP CONSTRAINT "LigneGarantie_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "Mainlevee" DROP CONSTRAINT "Mainlevee_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "Mainlevee" DROP CONSTRAINT "Mainlevee_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "MiseEnJeu" DROP CONSTRAINT "MiseEnJeu_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "MiseEnJeu" DROP CONSTRAINT "MiseEnJeu_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "PaiementInteretCommission" DROP CONSTRAINT "PaiementInteretCommission_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "PaiementInteretCommission" DROP CONSTRAINT "PaiementInteretCommission_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "Partenaire" DROP CONSTRAINT "Partenaire_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "Partenaire" DROP CONSTRAINT "Partenaire_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "Projet" DROP CONSTRAINT "Projet_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "Projet" DROP CONSTRAINT "Projet_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "RegleEligibilite" DROP CONSTRAINT "RegleEligibilite_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "RegleEligibilite" DROP CONSTRAINT "RegleEligibilite_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "SecteurActivite" DROP CONSTRAINT "SecteurActivite_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "SecteurActivite" DROP CONSTRAINT "SecteurActivite_utilisateurModificationId_fkey";

-- DropForeignKey
ALTER TABLE "TransfertGarantie" DROP CONSTRAINT "TransfertGarantie_utilisateurCreationId_fkey";

-- DropForeignKey
ALTER TABLE "TransfertGarantie" DROP CONSTRAINT "TransfertGarantie_utilisateurModificationId_fkey";

-- AlterTable
ALTER TABLE "AllocationLignePartenaire" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AvenantAllocation" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AvenantLigneGarantie" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Bailleur" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ClientBeneficiaire" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Garantie" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "GroupeRegleEligibilite" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "LigneGarantie" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Mainlevee" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "MiseEnJeu" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "PaiementInteretCommission" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Partenaire" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Projet" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "RegleEligibilite" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SecteurActivite" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "TransfertGarantie" ALTER COLUMN "utilisateurCreationId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "Bailleur" ADD CONSTRAINT "Bailleur_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Bailleur" ADD CONSTRAINT "Bailleur_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AvenantLigneGarantie" ADD CONSTRAINT "AvenantLigneGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AvenantLigneGarantie" ADD CONSTRAINT "AvenantLigneGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AvenantAllocation" ADD CONSTRAINT "AvenantAllocation_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AvenantAllocation" ADD CONSTRAINT "AvenantAllocation_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_secteurActivitePrincipalId_fkey" FOREIGN KEY ("secteurActivitePrincipalId") REFERENCES "SecteurActivite"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SecteurActivite" ADD CONSTRAINT "SecteurActivite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SecteurActivite" ADD CONSTRAINT "SecteurActivite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "GroupeRegleEligibilite" ADD CONSTRAINT "GroupeRegleEligibilite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "GroupeRegleEligibilite" ADD CONSTRAINT "GroupeRegleEligibilite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Mainlevee" ADD CONSTRAINT "Mainlevee_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Mainlevee" ADD CONSTRAINT "Mainlevee_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "MiseEnJeu" ADD CONSTRAINT "MiseEnJeu_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "MiseEnJeu" ADD CONSTRAINT "MiseEnJeu_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PaiementInteretCommission" ADD CONSTRAINT "PaiementInteretCommission_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "PaiementInteretCommission" ADD CONSTRAINT "PaiementInteretCommission_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_partenaireId_fkey" FOREIGN KEY ("partenaireId") REFERENCES "Partenaire"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_allocationId_fkey" FOREIGN KEY ("allocationId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_clientBeneficiaireId_fkey" FOREIGN KEY ("clientBeneficiaireId") REFERENCES "ClientBeneficiaire"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_projetId_fkey" FOREIGN KEY ("projetId") REFERENCES "Projet"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_garantieId_fkey" FOREIGN KEY ("garantieId") REFERENCES "Garantie"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_mainleveeId_fkey" FOREIGN KEY ("mainleveeId") REFERENCES "Mainlevee"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_miseEnJeuId_fkey" FOREIGN KEY ("miseEnJeuId") REFERENCES "MiseEnJeu"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_transfertGarantieId_fkey" FOREIGN KEY ("transfertGarantieId") REFERENCES "TransfertGarantie"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_paiementId_fkey" FOREIGN KEY ("paiementId") REFERENCES "PaiementInteretCommission"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_utilisateurId_fkey" FOREIGN KEY ("utilisateurId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;
