"use client";

import { useState, useEffect } from "react";
import { DataTable } from "@/components/shared/data-table";
import { enhancedAuditLogColumns, type EnhancedAuditLog } from "./enhanced-columns";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/shared/date-range-picker";
import { DateRange } from "react-day-picker";
import { Search, Filter, Download, RefreshCw, AlertTriangle, TrendingUp } from "lucide-react";
import { format, subDays } from "date-fns";
import { fr } from "date-fns/locale";

// Types pour les filtres
interface AuditFilters {
  utilisateurId?: string;
  action?: string;
  entite?: string;
  module?: string;
  criticalityLevel?: string;
  dateRange?: DateRange;
  searchTerm?: string;
}

// Statistiques d'audit
interface AuditStats {
  totalLogs: number;
  criticalLogs: number;
  highLogs: number;
  uniqueUsers: number;
  topActions: Array<{ action: string; count: number }>;
  topModules: Array<{ module: string; count: number }>;
}

export default function EnhancedAuditLogPage() {
  const [auditLogs, setAuditLogs] = useState<EnhancedAuditLog[]>([]);
  const [stats, setStats] = useState<AuditStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<AuditFilters>({
    dateRange: {
      from: subDays(new Date(), 7),
      to: new Date()
    }
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Options pour les filtres
  const criticalityOptions = [
    { value: "CRITICAL", label: "Critique", color: "destructive" },
    { value: "HIGH", label: "Élevé", color: "destructive" },
    { value: "MEDIUM", label: "Moyen", color: "secondary" },
    { value: "LOW", label: "Faible", color: "outline" }
  ];

  const moduleOptions = [
    { value: "USER_MANAGEMENT", label: "Gestion Utilisateurs" },
    { value: "SYSTEM_CONFIG", label: "Configuration Système" },
    { value: "FINANCIAL_OPERATIONS", label: "Opérations Financières" },
    { value: "GARANTIES", label: "Garanties" },
    { value: "ALLOCATIONS", label: "Allocations" },
    { value: "DATA_ACCESS", label: "Accès aux Données" },
    { value: "DATA_TRANSFER", label: "Transfert de Données" }
  ];

  const actionOptions = [
    { value: "LOGIN", label: "Connexion" },
    { value: "LOGOUT", label: "Déconnexion" },
    { value: "USER_CREATE", label: "Création Utilisateur" },
    { value: "USER_UPDATE", label: "Modification Utilisateur" },
    { value: "USER_DEACTIVATE", label: "Désactivation Utilisateur" },
    { value: "GARANTIE_CREATE", label: "Création Garantie" },
    { value: "ALLOCATION_CREATE", label: "Création Allocation" },
    { value: "MISE_EN_JEU_REQUEST", label: "Demande Mise en Jeu" },
    { value: "MAINLEVEE_REQUEST", label: "Demande Mainlevée" },
    { value: "PAIEMENT_CREATE", label: "Création Paiement" },
    { value: "SYSTEM_SETTING_UPDATE", label: "Modification Paramètre" },
    { value: "SENSITIVE_DATA_ACCESS", label: "Accès Données Sensibles" }
  ];

  // Charger les données d'audit
  const loadAuditData = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20"
      });

      // Ajouter les filtres aux paramètres
      if (filters.utilisateurId) params.append("utilisateurId", filters.utilisateurId);
      if (filters.action) params.append("action", filters.action);
      if (filters.entite) params.append("entite", filters.entite);
      if (filters.module) params.append("module", filters.module);
      if (filters.criticalityLevel) params.append("criticalityLevel", filters.criticalityLevel);
      if (filters.searchTerm) params.append("search", filters.searchTerm);
      if (filters.dateRange?.from) {
        params.append("dateDebut", format(filters.dateRange.from, "yyyy-MM-dd"));
      }
      if (filters.dateRange?.to) {
        params.append("dateFin", format(filters.dateRange.to, "yyyy-MM-dd"));
      }

      const response = await fetch(`/api/admin/audit-log?${params}`);
      const data = await response.json();

      if (response.ok) {
        setAuditLogs(data.data || []);
        setTotalPages(data.totalPages || 1);
      } else {
        console.error("Erreur lors du chargement des logs d'audit:", data.message);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des logs d'audit:", error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les statistiques
  const loadStats = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.dateRange?.from) {
        params.append("dateDebut", format(filters.dateRange.from, "yyyy-MM-dd"));
      }
      if (filters.dateRange?.to) {
        params.append("dateFin", format(filters.dateRange.to, "yyyy-MM-dd"));
      }

      const response = await fetch(`/api/admin/audit-log/stats?${params}`);
      const data = await response.json();

      if (response.ok) {
        setStats(data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
    }
  };

  // Exporter les données
  const exportData = async () => {
    try {
      const params = new URLSearchParams();
      
      // Ajouter tous les filtres actuels
      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'dateRange') {
          params.append(key, value as string);
        }
      });
      
      if (filters.dateRange?.from) {
        params.append("dateDebut", format(filters.dateRange.from, "yyyy-MM-dd"));
      }
      if (filters.dateRange?.to) {
        params.append("dateFin", format(filters.dateRange.to, "yyyy-MM-dd"));
      }
      
      params.append("export", "true");

      const response = await fetch(`/api/admin/audit-log?${params}`);
      const blob = await response.blob();
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-log-${format(new Date(), "yyyy-MM-dd")}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Erreur lors de l'export:", error);
    }
  };

  // Effets
  useEffect(() => {
    loadAuditData();
    loadStats();
  }, [currentPage, filters]);

  // Réinitialiser les filtres
  const resetFilters = () => {
    setFilters({
      dateRange: {
        from: subDays(new Date(), 7),
        to: new Date()
      }
    });
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Audit Log Amélioré</h1>
          <p className="text-muted-foreground">
            Traçabilité complète des actions utilisateur et système
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadAuditData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
          <Button onClick={exportData} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Logs</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Logs Critiques</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.criticalLogs}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Logs Élevés</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.highLogs}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Utilisateurs Actifs</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.uniqueUsers}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtres */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filtres Avancés
          </CardTitle>
          <CardDescription>
            Filtrez les logs d'audit selon vos critères
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* Recherche textuelle */}
            <div className="space-y-2">
              <Label htmlFor="search">Recherche</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Rechercher..."
                  className="pl-8"
                  value={filters.searchTerm || ""}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                />
              </div>
            </div>

            {/* Niveau de criticité */}
            <div className="space-y-2">
              <Label>Niveau de Criticité</Label>
              <Select
                value={filters.criticalityLevel || ""}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  criticalityLevel: value || undefined 
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les niveaux" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Tous les niveaux</SelectItem>
                  {criticalityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Module */}
            <div className="space-y-2">
              <Label>Module</Label>
              <Select
                value={filters.module || ""}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  module: value || undefined 
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les modules" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Tous les modules</SelectItem>
                  {moduleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Action */}
            <div className="space-y-2">
              <Label>Action</Label>
              <Select
                value={filters.action || ""}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  action: value || undefined 
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Toutes les actions</SelectItem>
                  {actionOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Plage de dates */}
            <div className="space-y-2 md:col-span-2">
              <Label>Période</Label>
              <DateRangePicker
                date={filters.dateRange}
                onDateChange={(dateRange: DateRange | undefined) => setFilters(prev => ({ ...prev, dateRange }))}
              />
            </div>

            {/* Boutons d'action */}
            <div className="flex items-end space-x-2">
              <Button onClick={resetFilters} variant="outline" size="sm">
                Réinitialiser
              </Button>
            </div>
          </div>

          {/* Filtres actifs */}
          <div className="flex flex-wrap gap-2 mt-4">
            {filters.criticalityLevel && (
              <Badge variant="secondary">
                Criticité: {criticalityOptions.find(o => o.value === filters.criticalityLevel)?.label}
              </Badge>
            )}
            {filters.module && (
              <Badge variant="secondary">
                Module: {moduleOptions.find(o => o.value === filters.module)?.label}
              </Badge>
            )}
            {filters.action && (
              <Badge variant="secondary">
                Action: {actionOptions.find(o => o.value === filters.action)?.label}
              </Badge>
            )}
            {filters.searchTerm && (
              <Badge variant="secondary">
                Recherche: "{filters.searchTerm}"
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Table des logs */}
      <Card>
        <CardHeader>
          <CardTitle>Logs d'Audit</CardTitle>
          <CardDescription>
            {auditLogs.length} log(s) trouvé(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={enhancedAuditLogColumns}
            data={auditLogs}
          />
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Précédent
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}