// src/lib/schemas/system-setting.schema.ts
import { z } from "zod";
import { TypeParametre } from "@prisma/client";

const typeParametreValues = Object.values(TypeParametre) as [string, ...string[]];

export const SystemSettingSchema = z.object({
  cle: z.string().min(3, "La clé doit contenir au moins 3 caractères.")
    .regex(/^[A-Z0-9_]+$/, "La clé ne doit contenir que des majuscules, chiffres et underscores.")
    .max(100),
  valeur: z.string().min(1, "La valeur ne peut pas être vide."), // Sera validée plus en détail selon le typeValeur côté serveur
  description: z.string().optional(),
  typeValeur: z.enum(typeParametreValues),
  estModifiable: z.boolean().default(true),
});

export type SystemSettingFormValues = z.infer<typeof SystemSettingSchema>;

// Validation spécifique de la valeur en fonction du type (à utiliser côté serveur)
export const validateSettingValue = (valeur: string, type: TypeParametre): boolean | string => {
    try {
        switch (type) {
            case TypeParametre.NUMBER:
                if (isNaN(Number(valeur))) return "Doit être un nombre valide.";
                break;
            case TypeParametre.BOOLEAN:
                if (valeur.toLowerCase() !== 'true' && valeur.toLowerCase() !== 'false') return "Doit être 'true' ou 'false'.";
                break;
            case TypeParametre.JSON:
                JSON.parse(valeur); // Lèvera une erreur si JSON invalide
                break;
            case TypeParametre.STRING:
            case TypeParametre.TEXT:
                // Aucune validation supplémentaire pour string/text ici, mais pourrait être ajoutée
                break;
            default:
                return "Type de paramètre inconnu.";
        }
        return true;
    } catch (e) {
        if (type === TypeParametre.JSON) return "JSON invalide.";
        return "Erreur de validation de la valeur.";
    }
};