"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs = require("fs");
const path = require("path");
const schemaPath = path.resolve(__dirname, "../prisma/schema.prisma");
const outputPath = path.resolve(__dirname, "../src/types/prisma-enums.ts");
let schema;
try {
  schema = fs.readFileSync(schemaPath, "utf-8");
} catch (error) {
  console.error(`Error reading schema file at ${schemaPath}:`, error.message);
  process.exit(1);
}
const enumRegex = /enum\s+(\w+)\s*{([^}]*)}/g;
let match;
const enums = [];
while ((match = enumRegex.exec(schema)) !== null) {
    const enumName = match[1];
    const values = match[2]
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith("//"))
        .map((line) => line.replace(/\/\/.*$/, "")) // remove inline comments
        .map((line) => line.replace(/"/g, "")) // remove quotes
        .map((line) => line.replace(/,.*/, "")) // remove trailing commas
        .map((line) => line.trim())
        .filter(Boolean);
    const enumString = `export enum ${enumName} {\n${values.map((v) => `  ${v} = "${v}",`).join("\n")}\n}\n`;
    enums.push(enumString);
}
const header = `// Ce fichier est généré automatiquement depuis prisma/schema.prisma. Ne pas éditer à la main !\n`;
try {
  fs.writeFileSync(outputPath, header + enums.join("\n"));
  console.log(`✅ Enums Prisma générés dans ${outputPath} (${enums.length} enums)`);
} catch (error) {
  console.error(`❌ Error writing to ${outputPath}:`, error.message);
  process.exit(1);
}
