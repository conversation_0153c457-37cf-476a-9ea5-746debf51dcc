// src/app/admin/system-settings/page.tsx
"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, TypeParametre, ParametreSysteme } from "@prisma/client";
import { getSystemSettingsColumns, SystemSettingColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose
} from "@/components/ui/dialog";
import { SystemSettingFormValues, SystemSettingSchema, validateSettingValue } from "@/lib/schemas/system-setting.schema";
import { useForm, Control, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";

const typeParametreOptions = Object.values(TypeParametre).map(val => ({ label: val, value: val }));

export default function SystemSettingsPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [settings, setSettings] = useState<SystemSettingColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingSetting, setEditingSetting] = useState<SystemSettingColumn | null>(null);
  const [settingToDelete, setSettingToDelete] = useState<SystemSettingColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);


  const form = useForm<SystemSettingFormValues>({
    resolver: zodResolver(SystemSettingSchema) as any,
    defaultValues: {
      cle: "",
      valeur: "",
      description: "",
      typeValeur: TypeParametre.STRING,
      estModifiable: true,
    },
  });

  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  const fetchSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/admin/system-settings", {
        headers: {
          'x-user-id': session?.user?.id || '',
          'user-agent': navigator.userAgent
        }
      });
      if (!response.ok) throw new Error("Échec de la récupération des paramètres");
      const data = await response.json();
      setSettings(data);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    if (sessionStatus === "loading") return;
    
    // Vérification d'authentification (une seule fois)
    if (!hasCheckedAuth) {
      if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
        toast({ title: "Accès refusé", variant: "destructive" });
        router.replace("/");
        return;
      }
      setHasCheckedAuth(true);
    }

    // Chargement des données
    if (hasCheckedAuth) {
      fetchSettings();
    }
  }, [session, sessionStatus, router, hasCheckedAuth]);

  const handleEdit = (setting: SystemSettingColumn) => {
    setEditingSetting(setting);
    form.reset({
      cle: setting.cle, // Clé non modifiable dans le formulaire, mais affichée
      valeur: setting.valeur,
      description: setting.description || "",
      typeValeur: setting.typeValeur,
      estModifiable: setting.estModifiable,
    });
    setIsFormOpen(true);
  };

  const handleDeleteConfirm = (setting: SystemSettingColumn) => {
    setSettingToDelete(setting);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!settingToDelete) return;
    try {
      const response = await fetch(`/api/admin/system-settings/${settingToDelete.id}`, {
        method: "DELETE",
        headers: {
          'x-user-id': session?.user?.id || '',
          'user-agent': navigator.userAgent
        }
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec de la suppression");
      }
      toast({ title: "Paramètre Supprimé", description: `Le paramètre ${settingToDelete.cle} a été supprimé.` });
      fetchSettings();
    } catch (error: any) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } finally {
      setIsDeleteDialogOpen(false);
      setSettingToDelete(null);
    }
  };

  const onSubmit: SubmitHandler<SystemSettingFormValues> = async (values) => {
    // Validation supplémentaire de la valeur
    const valueValidationResult = validateSettingValue(values.valeur, values.typeValeur as TypeParametre);
    if (typeof valueValidationResult === 'string') {
        form.setError("valeur", { type: "manual", message: valueValidationResult });
        return;
    }

    const url = editingSetting ? `/api/admin/system-settings/${editingSetting.id}` : "/api/admin/system-settings";
    const method = editingSetting ? "PUT" : "POST";
    // Pour PUT, on n'envoie pas la clé car elle ne doit pas être modifiée
    const body = editingSetting ? { ...values, cle: undefined } : values;


    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          'x-user-id': session?.user?.id || '',
          'user-agent': navigator.userAgent
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.errors) { // Erreurs de validation Zod du backend
            Object.keys(errorData.errors).forEach((key) => {
                form.setError(key as keyof SystemSettingFormValues, { type: "server", message: errorData.errors[key].join(', ') });
            });
        }
        throw new Error(errorData.message || (editingSetting ? "Échec de la mise à jour" : "Échec de la création"));
      }
      toast({ title: editingSetting ? "Paramètre Mis à Jour" : "Paramètre Créé" });
      setIsFormOpen(false);
      setEditingSetting(null);
      form.reset();
      fetchSettings();
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  };

  const columns = React.useMemo(() => getSystemSettingsColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }), []);

  if (isLoading || sessionStatus === 'loading') return <div className="p-6">Chargement...</div>;

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Paramètres Système</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => {
            setIsFormOpen(open);
            if (!open) {
                setEditingSetting(null);
                form.reset();
            }
        }}>
          <DialogTrigger asChild>
            <Button onClick={() => { setEditingSetting(null); form.reset(); setIsFormOpen(true); }}>
                <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Paramètre
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingSetting ? "Modifier le Paramètre" : "Ajouter un Paramètre Système"}</DialogTitle>
              {editingSetting && <DialogDescription>Clé : <span className="font-mono font-semibold">{editingSetting.cle}</span> (non modifiable)</DialogDescription>}
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
                {!editingSetting && (
                    <FormField control={form.control as Control<SystemSettingFormValues>} name="cle" render={({ field }) => (
                        <FormItem>
                            <FormLabel>Clé</FormLabel>
                            <FormControl><Input placeholder="EXEMPLE_CLE_PARAM" {...field} /></FormControl>
                            <FormDescription>Unique, en majuscules avec underscores.</FormDescription>
                            <FormMessage />
                        </FormItem>
                    )} />
                )}
                <FormField control={form.control} name="typeValeur" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Type de Valeur</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir un type" /></SelectTrigger></FormControl>
                            <SelectContent>
                                {typeParametreOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="valeur" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Valeur</FormLabel>
                        <FormControl>
                            {form.getValues("typeValeur") === TypeParametre.TEXT || form.getValues("typeValeur") === TypeParametre.JSON ? (
                                <Textarea placeholder="Entrez la valeur..." {...field} rows={form.getValues("typeValeur") === TypeParametre.JSON ? 6: 3} />
                            ) : form.getValues("typeValeur") === TypeParametre.BOOLEAN ? (
                                <Select onValueChange={field.onChange} value={field.value?.toString() || "false"}>
                                    <FormControl><SelectTrigger><SelectValue placeholder="Choisir vrai ou faux" /></SelectTrigger></FormControl>
                                    <SelectContent>
                                        <SelectItem value="true">Vrai (true)</SelectItem>
                                        <SelectItem value="false">Faux (false)</SelectItem>
                                    </SelectContent>
                                </Select>
                            ) : (
                                <Input placeholder="Entrez la valeur..." {...field} type={form.getValues("typeValeur") === TypeParametre.NUMBER ? "number" : "text"} />
                            )}
                        </FormControl>
                        <FormDescription>
                            {form.getValues("typeValeur") === TypeParametre.BOOLEAN && "Entrez 'true' ou 'false'."}
                            {form.getValues("typeValeur") === TypeParametre.JSON && "Entrez un JSON valide."}
                        </FormDescription>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="description" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl><Textarea placeholder="À quoi sert ce paramètre ?" {...field} /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="estModifiable" render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 shadow-sm">
                        <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                        <div className="space-y-1 leading-none">
                            <FormLabel>Modifiable via l'interface utilisateur</FormLabel>
                            <FormDescription>Si décoché, ce paramètre ne pourra plus être modifié ou supprimé via l'UI (sauf par un super-admin en BDD).</FormDescription>
                        </div>
                    </FormItem>
                )} />
                <DialogFooter>
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={form.formState.isSubmitting}>
                        {form.formState.isSubmitting ? "Sauvegarde..." : (editingSetting ? "Mettre à Jour" : "Créer")}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={settings} />

      {settingToDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Supprimer le paramètre ?</AlertDialogTitle>
                    <AlertDialogDescription>
                        Êtes-vous sûr de vouloir supprimer le paramètre <span className="font-semibold font-mono">{settingToDelete.cle}</span> ?
                        Cette action est irréversible.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setSettingToDelete(null)}>Annuler</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">Oui, Supprimer</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}