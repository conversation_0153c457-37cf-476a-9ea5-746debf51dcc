// src/app/api/lignes-garantie/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutLigneGarantie } from "@prisma/client";
import { LigneGarantieSchema } from "@/lib/schemas/ligne-garantie.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

// GET: Lister toutes les lignes de garantie
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  try {
    const lignes = await prisma.ligneGarantie.findMany({
      orderBy: { dateCreation: "desc" },
      include: {
        bailleur: { select: { id: true, nom: true } },
        utilisateurCreation: { select: { nomUtilisateur: true }},
        // Vous pourriez vouloir calculer le montantDisponible ici si ce n'est pas fait par trigger/vue BDD
      }
    });
    return NextResponse.json(lignes);
  } catch (error) { /* ... gestion erreur ... */ }
}

// POST: Créer une nouvelle ligne de garantie
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  if (creatorId === undefined) {
    return NextResponse.json({ message: "Utilisateur de création non valide." }, { status: 400 });
  }

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const body = await request.json();
      console.log("Body reçu par l'API LigneGarantie POST:", body); // LOG DEBUG
      // Convertir les dates string en objets Date avant la validation Zod si elles arrivent en string
      if (body.dateOuverture) body.dateOuverture = new Date(body.dateOuverture);
      if (body.dateExpiration) body.dateExpiration = new Date(body.dateExpiration);

      const validation = LigneGarantieSchema.safeParse(body);
      console.log("Résultat validation Zod API:", validation); // LOG DEBUG
      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        nom, bailleurId, referenceConvention, description, montantInitialStr,
        dateOuverture, dateExpiration, devise, statut,
        autreInfoObjectifLigne, autreInfoConditionsSpecifiques, autreInfoIndicateurPerf
      } = validation.data;

      const bailleurIdNum = parseInt(bailleurId);
      const bailleurExists = await prisma.bailleur.findUnique({ where: { id: bailleurIdNum } });
      if (!bailleurExists) return NextResponse.json({ message: "Bailleur non valide." }, { status: 400 });

      const montantInitialDecimal = new Decimal(montantInitialStr.replace(',', '.'));

      // Assembler autresInformationsJson
      const autresInformationsJson: any = {};
      if (autreInfoObjectifLigne) autresInformationsJson.objectif_ligne = autreInfoObjectifLigne;
      if (autreInfoConditionsSpecifiques) autresInformationsJson.conditions_specifiques = autreInfoConditionsSpecifiques;
      if (autreInfoIndicateurPerf) autresInformationsJson.indicateur_performance_cle = autreInfoIndicateurPerf;

      const newLine = await prisma.ligneGarantie.create({
        data: {
          nom,
          bailleurId: bailleurIdNum,
          referenceConvention,
          description,
          montantInitial: montantInitialDecimal,
          montantDisponible: montantInitialDecimal, // Initialement disponible = initial
          dateOuverture,
          dateExpiration,
          devise: devise.toUpperCase(),
          statut: statut as StatutLigneGarantie,
          autresInformations: Object.keys(autresInformationsJson).length > 0 ? autresInformationsJson : null,
          utilisateurCreationId: creatorId,
        },
      });
      return NextResponse.json(newLine, { status: 201 });
    } catch (error: any) {
      if (error.code === 'P2002' && error.meta?.target?.includes('nom')) {
        return NextResponse.json({ message: "Une ligne de garantie avec ce nom existe déjà." }, { status: 409 });
      }
      console.error("Erreur POST /api/lignes-garantie:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}