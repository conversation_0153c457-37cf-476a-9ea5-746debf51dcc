// src/app/(app)/lignes-garantie/[ligneGarantieId]/page.tsx
"use client";
// Use environment-aware logging
if (process.env.NODE_ENV === 'development') {
    console.log("[LigneDetailPage] File loaded, component definition starting.");
  }
  
  import React, { useEffect, useState, useCallback, useMemo } from "react";
  import { useParams, useRouter } from "next/navigation";
  import { useSession } from "next-auth/react";
  import { Button } from "@/components/ui/button";
  import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
  import { useToast } from "@/components/ui/use-toast";
  import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from "@/components/ui/dialog";
  import { AvenantLigneGarantieFormValues, AvenantLigneGarantieSchema, TypeAvenant } from "@/lib/schemas/avenant-ligne-garantie.schema";
  import { useForm } from "react-hook-form";
  import { zodResolver } from "@hookform/resolvers/zod";
  import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
  import { Input } from "@/components/ui/input";
  import { Textarea } from "@/components/ui/textarea";
  import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
  import { DatePicker } from "@/components/ui/date-picker";
  import { PlusCircle, ArrowLeft } from "lucide-react";
  import { formatNumberWithThousandsSeparator } from "@/lib/utils";
  import Link from "next/link";
  import { DataTable } from "@/components/shared/data-table"; // Pour lister les avenants
  import { ColumnDef } from "@tanstack/react-table"; // Pour les colonnes des avenants
  import { Badge } from "@/components/ui/badge";
  // import { Decimal } from ".prisma/client/runtime/library";
  
  export enum StatutLigneGarantie {
    Active = "Active",
    Expiree = "Expiree",
    Suspendue = "Suspendue",
    // ... autres statuts
  }
  
  type LigneGarantie = {
    id: number;
    nom: string;
    referenceConvention?: string;
    description?: string;
    montantInitial: number;
    montantDisponible: number;
    dateOuverture: string | Date;
    dateExpiration: string | Date;
    devise: string;
    statut: StatutLigneGarantie;
    autresInformations?: any;
  };
  
  type Bailleur = { nom: string };
  // TODO: Extract an interface that matches the API/Prisma model.
// This keeps compile-time guarantees and enables editor IntelliSense.
interface AvenantPrisma {
  id: number;
  typeAvenant: TypeAvenant;
  montantModification?: number;
  nouvelleDateExpiration?: string;
  dateAvenant: string;
  raison: string;
  referenceDocument?: string;
  // add the remaining fields returned by the API…
}
  
  // Type pour les données de la ligne enrichies
  type LigneGarantieDetails = LigneGarantie & {
    bailleur: Pick<Bailleur, "nom">;
    avenants: AvenantPrisma[]; // Ou un type AvenantColumn si vous le définissez
    // _count?: { allocations: number };
  };
  
  const typeAvenantOptions = Object.values(TypeAvenant).map(val => ({ value: val, label: val.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }));
  const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';
  
  
  // Colonnes pour la table des avenants
  const getAvenantColumns = ( onEditAvenant: (avenant: AvenantPrisma) => void, onDeleteAvenant: (avenantId: number) => void ): ColumnDef<AvenantPrisma>[] => [
      {
          id: "actions",          // virtual column – no data accessor
          header: "Actions",
          cell: ({ row }) => {
              const avenant = row.original;
              return (
                  <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditAvenant(avenant);
                        }}
                      >
                           Modifier
                       </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteAvenant(avenant.id);
                        }}
                        // disabled={isDeletingAvenantId === avenant.id} // Remove or define isDeletingAvenantId if needed
                      >
                           Supprimer
                       </Button>
                   </div>
              );
          }
      },
      { accessorKey: "dateAvenant", header: "Date Avenant", cell: ({row}) => formatDate(row.getValue("dateAvenant")) },
      { accessorKey: "typeAvenant", header: "Type", cell: ({row}) => <Badge variant="outline">{row.getValue("typeAvenant")}</Badge> },
      { accessorKey: "montantModification", header: "Modif. Montant", cell: ({row}) => formatNumberWithThousandsSeparator(row.getValue("montantModification")) },
      { accessorKey: "nouvelleDateExpiration", header: "Nlle Date Exp.", cell: ({row}) => formatDate(row.getValue("nouvelleDateExpiration")) },
      { accessorKey: "raison", header: "Raison", cell: ({row}) => <span className="truncate block max-w-xs">{row.getValue("raison")}</span> },
      // Ajouter des actions pour modifier/supprimer un avenant plus tard
  ];
  
  
  export default function LigneGarantieDetailPage() {
    if (process.env.NODE_ENV === 'development') {
    console.log("[LigneDetailPage] Component rendering started.");
  }
    const params = useParams();
    const router = useRouter();
    const { toast } = useToast();
    const { data: session, status: sessionStatus } = useSession();
  
    // Stabilise l'id pour éviter les changements de référence
    const id = useMemo(() => params.id as string, [params.id]);
  
    const [ligneDetails, setLigneDetails] = useState<LigneGarantieDetails | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [hasFetchedInitialData, setHasFetchedInitialData] = useState(false);
    const [isAvenantFormOpen, setIsAvenantFormOpen] = useState(false);
    const [editingAvenant, setEditingAvenant] = useState<AvenantPrisma | null>(null);
  
  if (process.env.NODE_ENV === 'development') {
    console.log("[LigneDetailPage] Session pour avenant:", session);
    console.log("[LigneDetailPage] Rôle utilisateur:", session?.user?.role);
  }
    // Utilisez la version de base du schéma pour omettre le champ, puis appliquez le resolver
const avenantForm = useForm({
  resolver: zodResolver(AvenantLigneGarantieSchema),
      defaultValues: {
        typeAvenant: undefined,
        montantModificationStr: "",
        nouvelleDateExpiration: undefined,
        dateAvenant: new Date(),
        raison: "",
        referenceDocument: "",
      },
    });
  
    // Fonction pour ouvrir le formulaire en mode édition
    const handleEditAvenant = useCallback((avenant: AvenantPrisma) => {
      setEditingAvenant(avenant);
      avenantForm.reset({
        typeAvenant: avenant.typeAvenant as TypeAvenant, // Assurez-vous que le type correspond
        montantModificationStr: avenant.montantModification ? String(avenant.montantModification) : "",
        nouvelleDateExpiration: avenant.nouvelleDateExpiration ? new Date(avenant.nouvelleDateExpiration) : undefined,
        dateAvenant: new Date(avenant.dateAvenant),
        raison: avenant.raison,
        referenceDocument: avenant.referenceDocument || "",
      });
      setIsAvenantFormOpen(true);
    }, [avenantForm]);
    const typeAvenantSelected = avenantForm.watch("typeAvenant");
  
    // 1. Gestion stricte de l'accès
  const isAutoriseGlobale = session && ["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(session.user?.role as string);
  const canAddAvenant = session && ["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(session.user?.role as string);
  if (process.env.NODE_ENV === 'development') {
    console.log("[LigneDetailPage] canAddAvenant:", canAddAvenant);
  }
  
    // 2. Affichage accès refusé AVANT tout fetch
    if (sessionStatus === "loading") {
      return <div className="p-6 text-center">Chargement...</div>;
    }
    if (sessionStatus === "unauthenticated") {
      return <div className="p-6 text-center text-red-600">Accès refusé. Veuillez vous connecter.</div>;
    }
    if (!isAutoriseGlobale) {
      return <div className="p-6 text-center text-red-600">Accès refusé. Vous n'avez pas les droits pour voir cette page.</div>;
    }
  
  
    // 3. Fetch uniquement si autorisé
    const fetchLigneDetails = useCallback(async () => {
      if (!id) return;
      setIsLoading(true);
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[LigneDetailPage] Fetching details for ligneId: ${id}`);
        }
         const response = await fetch(`/api/lignes-garantie/${id}?includeAvenants=true`);
        if (process.env.NODE_ENV === 'development') {
          console.log(`[LigneDetailPage] API response status: ${response.status}`);
        }
         if (!response.ok) {
           const errorText = await response.text(); // Log ajouté
          if (process.env.NODE_ENV === 'development') {
            console.error("[LigneDetailPage] API Error:", errorText);
          }
           throw new Error("Ligne de garantie non trouvée ou erreur serveur.");
         }
         const data = await response.json();
        if (process.env.NODE_ENV === 'development') {
          console.log("[LigneDetailPage] Data received:", data);
        }
        setLigneDetails(data);
      } catch (error: any) {
        if (process.env.NODE_ENV === 'development') {
          console.error("[LigneDetailPage] Catch Error in fetchLigneDetails:", error);
        }
         toast({ title: "Erreur", description: error.message, variant: "destructive" });
      } finally {
        setIsLoading(false);
      }
    }, [id, toast]); // router retiré des dépendances
  
    useEffect(() => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log("[LigneDetailPage] useEffect for data fetching triggered. HasFetchedInitialData:", hasFetchedInitialData, "ID:", id);
      }
      }
      if (!hasFetchedInitialData && id) {
        fetchLigneDetails().then(() => setHasFetchedInitialData(true));
      }
       
    }, [id, fetchLigneDetails, hasFetchedInitialData]);
  
    const onAvenantSubmit = useCallback(async (values: AvenantLigneGarantieFormValues) => {
      if (!ligneDetails) return;
      const apiUrl = editingAvenant 
        ? `/api/lignes-garantie/${id}/avenants/${editingAvenant.id}` 
        : `/api/lignes-garantie/${id}/avenants`;
      const method = editingAvenant ? "PUT" : "POST";
  
      try {
        const response = await fetch(apiUrl, {
          method: method,
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(values),
        });
        if (!response.ok) {
          let message = `Échec de ${editingAvenant ? 'la mise à jour' : 'la création'} de l'avenant`;
          try {
            const errorData = await response.json();
            message = errorData.error || errorData.message || message;
          } catch {
            message = await response.text().catch(() => message);
          }
          throw new Error(message);
        }
        toast({ title: `Avenant ${editingAvenant ? 'Modifié' : 'Créé'}`, description: `L'avenant a été ${editingAvenant ? 'modifié' : 'ajouté'} avec succès.` });
        setIsAvenantFormOpen(false);
        setEditingAvenant(null);
        avenantForm.reset({
          typeAvenant: undefined,
          montantModificationStr: "",
          nouvelleDateExpiration: undefined,
          dateAvenant: new Date(),
          raison: "",
          referenceDocument: "",
        });
        setHasFetchedInitialData(false); // Provoque le refetch via useEffect
      } catch (error: any) {
        toast({ title: "Erreur", description: error.message, variant: "destructive" });
      }
    }, [ligneDetails, avenantForm, toast, id, editingAvenant]);
  
    const handleDeleteAvenant = useCallback(async (avenantId: number) => {
      if (!ligneDetails || !confirm("Êtes-vous sûr de vouloir supprimer cet avenant ? Cette action est irréversible.")) return;
  
      try {
        const response = await fetch(`/api/lignes-garantie/${id}/avenants/${avenantId}`, {
          method: "DELETE",
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || errorData.message || "Échec de la suppression de l'avenant");
        }
        toast({ title: "Avenant Supprimé", description: "L'avenant a été supprimé avec succès." });
        setHasFetchedInitialData(false); // Provoque le refetch
      } catch (error: any) {
        toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
      }
    }, [ligneDetails, toast, id]);
  
    const avenantColumns = useMemo(() => getAvenantColumns(handleEditAvenant, handleDeleteAvenant), [handleEditAvenant, handleDeleteAvenant]);
  
  
    if (isLoading) return <div className="p-6 text-center">Chargement...</div>;
    if (process.env.NODE_ENV === 'development') {
      console.log("[LigneDetailPage] LigneDetails state:", ligneDetails);
    }
    if (!ligneDetails) return <div className="p-6 text-center">Ligne de garantie non trouvée.</div>;
  
    return (
      <div className="container mx-auto py-8 px-4">
        <Button variant="outline" onClick={() => router.push('/lignes-garantie')} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" /> Retour à la liste
        </Button>
  
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">{ligneDetails.nom}</CardTitle>
            <CardDescription>Bailleur: {ligneDetails.bailleur.nom} - Statut: <Badge className={getStatutBadgeVariant(ligneDetails.statut)}>{ligneDetails.statut}</Badge></CardDescription>
          </CardHeader>
          <CardContent className="grid md:grid-cols-2 gap-4 text-sm">
            <div><strong>Réf. Convention:</strong> {ligneDetails.referenceConvention || "-"}</div>
            <div><strong>Devise:</strong> {ligneDetails.devise}</div>
            <div><strong>Montant Initial:</strong> {formatNumberWithThousandsSeparator(ligneDetails.montantInitial)}</div>
            <div><strong>Montant Disponible:</strong> {formatNumberWithThousandsSeparator(ligneDetails.montantDisponible)}</div>
            <div><strong>Date Ouverture:</strong> {formatDate(ligneDetails.dateOuverture)}</div>
            <div><strong>Date Expiration:</strong> {formatDate(ligneDetails.dateExpiration)}</div>
            {ligneDetails.description && <div className="md:col-span-2"><strong>Description:</strong> <p className="whitespace-pre-wrap">{ligneDetails.description}</p></div>}
            {ligneDetails.autresInformations && <div className="md:col-span-2"><strong>Autres Infos:</strong> <pre className="text-xs bg-slate-100 p-2 rounded overflow-auto">{JSON.stringify(ligneDetails.autresInformations, null, 2)}</pre></div>}
          </CardContent>
        </Card>
  
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Avenants sur la Ligne</h2>
              {canAddAvenant && (
                <Dialog
    open={isAvenantFormOpen}
    onOpenChange={(open) => {
      setIsAvenantFormOpen(open);
      if (!open) {
        setEditingAvenant(null);
        avenantForm.reset();
      }
    }}>
                    <DialogTrigger asChild>
                        <Button onClick={() => { 
                            setEditingAvenant(null); // Assure qu'on est en mode création
                            avenantForm.reset({
                              typeAvenant: undefined,
                              montantModificationStr: "",
                              nouvelleDateExpiration: undefined,
                              dateAvenant: new Date(),
                              raison: "",
                              referenceDocument: "",
                            }); 
                            setIsAvenantFormOpen(true); 
                          }}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Avenant
                        </Button>
                    </DialogTrigger>
                  <DialogContent className="sm:max-w-xl">
                      <DialogHeader><DialogTitle>{editingAvenant ? "Modifier l'Avenant" : "Ajouter un Avenant"}</DialogTitle></DialogHeader>
                      <Form {...avenantForm}>
                          <form onSubmit={avenantForm.handleSubmit(onAvenantSubmit)} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
                              {/* Row 1: Type Avenant & Montant Modif (conditional) */}
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                  <FormField control={avenantForm.control} name="typeAvenant" render={({ field }) => (
                                      <FormItem><FormLabel>Type d'Avenant <span className="text-red-500">*</span></FormLabel>
                                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                                              <FormControl><SelectTrigger><SelectValue placeholder="Choisir un type..." /></SelectTrigger></FormControl>
                                              <SelectContent>{typeAvenantOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                                          </Select><FormMessage />
                                      </FormItem>
                                  )} />
                                  {(typeAvenantSelected === TypeAvenant.AUGMENTATION_MONTANT || typeAvenantSelected === TypeAvenant.REDUCTION_MONTANT) ? (
                                      <FormField control={avenantForm.control} name="montantModificationStr" render={({ field }) => (
                                          <FormItem><FormLabel>Montant de Modification <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Ex: 50000000" {...field} value={String(field.value ?? "")} /></FormControl><FormDescription>Entrez un montant positif.</FormDescription><FormMessage /></FormItem>
                                      )} />
                                  ) : (
                                      <div /> /* Empty div for grid balance */
                                  )}
                              </div>
  
                              {/* Row 2: Date Avenant & Nouvelle Date Exp (conditional) */}
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                  <FormField control={avenantForm.control} name="dateAvenant" render={({ field }) => (
                                      <FormItem className="flex flex-col"><FormLabel>Date de l'Avenant <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value} onDateChange={field.onChange} /><FormMessage /></FormItem>
                                  )} />
                                  {typeAvenantSelected === TypeAvenant.PROLONGATION_DUREE ? (
                                      <FormField control={avenantForm.control} name="nouvelleDateExpiration" render={({ field }) => (
                                          <FormItem className="flex flex-col"><FormLabel>Nouvelle Date d'Expiration <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value} onDateChange={field.onChange} /><FormMessage /></FormItem>
                                      )} />
                                  ) : (
                                      <div /> /* Empty div for grid balance */
                                  )}
                              </div>
                              
                              {/* Row 3: Raison (full width) */}
                              <FormField control={avenantForm.control} name="raison" render={({ field }) => (
                                  <FormItem><FormLabel>Raison <span className="text-red-500">*</span></FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>
                              )} />
                              
                              {/* Row 4: Reference Document (full width) */}
                              <FormField control={avenantForm.control} name="referenceDocument" render={({ field }) => ( <FormItem><FormLabel>Référence Document</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem> )}/>
                              
                              <DialogFooter className="pt-4">
                                  <DialogClose asChild><Button type="button" variant="outline" onClick={() => { setIsAvenantFormOpen(false); setEditingAvenant(null); avenantForm.reset(); }}>Annuler</Button></DialogClose>
                                  <Button type="submit" disabled={avenantForm.formState.isSubmitting}>
                                      {avenantForm.formState.isSubmitting ? "Sauvegarde..." : (editingAvenant ? "Modifier l'Avenant" : "Ajouter l'Avenant")}
                                  </Button>
                              </DialogFooter>
                          </form>
                      </Form>
                  </DialogContent>
              </Dialog>
              )}
          </div>
          {ligneDetails.avenants && ligneDetails.avenants.length > 0 ? (
              <DataTable columns={avenantColumns} data={ligneDetails.avenants} />
          ) : (
              <p className="text-muted-foreground text-center py-4">Aucun avenant enregistré pour cette ligne.</p>
          )}
        </div>
      </div>
    );
  }
  
  // Fonction getStatutBadgeVariant à définir ou importer si elle n'est pas déjà globale
  const getStatutBadgeVariant = (statut: StatutLigneGarantie | undefined): string => {
    switch (statut) {
      case StatutLigneGarantie.Active:
        return "bg-green-100 text-green-800";
      case StatutLigneGarantie.Expiree:
        return "bg-red-100 text-red-800";
  
      case StatutLigneGarantie.Suspendue:
        return "bg-yellow-100 text-yellow-800";
      default:
        return "";
    }
  };

