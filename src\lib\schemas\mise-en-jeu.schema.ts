import { z } from "zod";
import { StatutMiseEnJeu } from "@prisma/client";

// Helper pour les chaînes numériques positives
const positiveNumericString = (fieldName: string) =>
  z.string().min(1, `${fieldName} est requis.`)
    .refine(val => !isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) > 0, {
        message: `${fieldName} doit être un nombre positif valide.`,
    });

// Schéma pour la création d'une demande de mise en jeu
export const DemandeMiseEnJeuSchema = z.object({
  // garantieId sera fourni par le contexte (ex: URL), non par le formulaire direct
  dateDemande: z.date({
    required_error: "La date de demande est requise.",
    invalid_type_error: "Format de date de demande invalide.",
  }),
  montantDemandeStr: positiveNumericString("Le montant demandé"), // Montant demandé par le partenaire
  motifDemande: z.string().min(10, "Le motif de la demande doit contenir au moins 10 caractères.").max(2000),
});

export type DemandeMiseEnJeuFormValues = z.infer<typeof DemandeMiseEnJeuSchema>;

// --- Schémas pour la Décision et le Paiement (à utiliser plus tard) ---

const statutDecisionMiseEnJeuValues = [
    StatutMiseEnJeu.ApprouveePartiellement,
    StatutMiseEnJeu.ApprouveeTotalement,
    StatutMiseEnJeu.Refusee,
] as [string, ...string[]];

export const DecisionMiseEnJeuSchema = z.object({
    statut: z.enum(statutDecisionMiseEnJeuValues, {
        required_error: "La décision (statut) est requise.",
    }),
    dateDecision: z.date({ required_error: "La date de décision est requise."}),
    montantApprouveStr: z.string().optional()
        .refine(val => {
            if (!val || val.trim() === '') return true; // Empty is allowed
            const num = parseFloat(val.replace(',', '.'));
            return !isNaN(num) && num >= 0;
        }, {
            message: "Le montant approuvé doit être un nombre valide non négatif.",
        }),
    commentairesDecision: z.string().max(1000).optional().or(z.literal('')),
}).refine(data => {
    if (data.statut === StatutMiseEnJeu.ApprouveeTotalement || data.statut === StatutMiseEnJeu.ApprouveePartiellement) {
        return data.montantApprouveStr !== undefined && data.montantApprouveStr.trim() !== "";
    }
    return true;
}, {
    message: "Le montant approuvé est requis si la mise en jeu est approuvée (totalement ou partiellement).",
    path: ["montantApprouveStr"],
});
export type DecisionMiseEnJeuFormValues = z.infer<typeof DecisionMiseEnJeuSchema>;

export const PaiementMiseEnJeuSchema = z.object({
    montantPayeStr: positiveNumericString("Le montant payé"),
    datePaiement: z.date({ required_error: "La date de paiement est requise."}),
    referencePaiement: z.string().min(1, "La référence du paiement est requise.").max(100),
});
export type PaiementMiseEnJeuFormValues = z.infer<typeof PaiementMiseEnJeuSchema>; 