// src/app/configuration/partenaires/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Partenaire, TypePartenaire, Utilisateur } from "@prisma/client"; // Importer TypePartenaire
import { ArrowUpDown, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export type PartenaireColumn = Partenaire & {
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
    utilisateurModification?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface PartenaireColumnsProps {
    onEdit: (partenaire: PartenaireColumn) => void;
    onDelete: (partenaire: PartenaireColumn) => void;
}

// Fonction pour obtenir un libellé lisible pour TypePartenaire
const getTypePartenaireLabel = (type: TypePartenaire): string => {
    switch (type) {
        case TypePartenaire.Banque: return "Banque";
        case TypePartenaire.IMF: return "Institution de Microfinance";
        case TypePartenaire.SFD: return "Système Financier Décentralisé";
        case TypePartenaire.AutreFinancier: return "Autre Financier";
        case TypePartenaire.Technique: return "Technique";
        case TypePartenaire.Institutionnel: return "Institutionnel";
        default: return type;
    }
};

export const getPartenaireColumns = ({ onEdit, onDelete }: PartenaireColumnsProps): ColumnDef<PartenaireColumn>[] => [
  {
    accessorKey: "nom",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Nom <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "typePartenaire",
    header: "Type",
    cell: ({ row }) => {
        const type = row.getValue("typePartenaire") as TypePartenaire;
        return <Badge variant="outline">{getTypePartenaireLabel(type)}</Badge>;
    }
  },
  {
    accessorKey: "convention",
    header: "Réf. Convention",
    cell: ({ row }) => row.getValue("convention") || "-",
  },
  {
    accessorKey: "contact",
    header: "Contact Principal",
    cell: ({ row }) => {
        const contact = row.getValue("contact") as { nomRepresentant?: string, email?: string, telephone?: string, adresse?: string } | null;
        if (!contact || Object.values(contact).every(v => !v)) return <span className="text-muted-foreground">-</span>;
        return (
            <div className="text-xs">
                {contact.nomRepresentant && <div>{contact.nomRepresentant}</div>}
                {contact.email && <div className="text-blue-600">{contact.email}</div>}
                {contact.telephone && <div>{contact.telephone}</div>}
                {contact.adresse && <div className="text-muted-foreground truncate max-w-[150px]" title={contact.adresse}>{contact.adresse}</div>}
            </div>
        );
    }
  },
  {
    accessorKey: "dateCreation",
    header: "Créé le",
    cell: ({ row }) => new Date(row.getValue("dateCreation")).toLocaleDateString("fr-FR"),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const partenaire = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button variant="ghost" size="icon" onClick={() => onEdit(partenaire)} title="Modifier">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(partenaire)} className="text-red-600 hover:text-red-700" title="Supprimer">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];