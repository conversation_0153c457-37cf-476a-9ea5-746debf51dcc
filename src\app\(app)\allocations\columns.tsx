// src/app/(app)/allocations/columns.tsx
"use client";
import { ColumnDef } from "@tanstack/react-table";
import { AllocationLignePartenaire, LigneGarantie, Partenaire, StatutAllocation, Utilisateur } from "@prisma/client";
import { ArrowUpDown, Edit, Trash2 } from "lucide-react"; // Eye removed
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

export type AllocationColumn = AllocationLignePartenaire & {
    ligneGarantie: Pick<LigneGarantie, "id" | "nom" | "devise">;
    partenaire: Pick<Partenaire, "id" | "nom">;
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
    // _count?: { garanties: number, avenants: number }; // Pour afficher des compteurs
};

interface AllocationColumnsProps {
    onEdit: (allocation: AllocationColumn) => void;
    onDelete: (allocation: AllocationColumn) => void;
    // onViewDetails?: (allocation: AllocationColumn) => void; // Pour une future page de détail d'allocation
}

export const formatCurrency = (amount: any, currency: string = "XOF") => {
     if (amount === null || amount === undefined) return "-";
     const num = typeof amount === 'string'
  ? Number(
      amount
        .replace(/\s/g, '')      // remove thin/non-breaking spaces
        .replace(/,/g, '.')      // convert any comma to dot
    )
  : amount;
     if (isNaN(num)) return "-";
    const cached = formatterCache.get(currency) ??
      new Intl.NumberFormat("fr-FR",
        { style: "currency", currency, minimumFractionDigits: 0 });
    formatterCache.set(currency, cached);
    return cached.format(num);
 };

const formatterCache = new Map<string, Intl.NumberFormat>();

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

const getStatutAllocationBadgeVariant = (statut: StatutAllocation | undefined) => {
    switch (statut) {
        case StatutAllocation.Active: return "bg-green-100 text-green-700 border-green-300";
        case StatutAllocation.EnAttenteValidation: return "bg-yellow-100 text-yellow-700 border-yellow-300";
        case StatutAllocation.Epuisee: return "bg-orange-100 text-orange-700 border-orange-300";
        case StatutAllocation.Expiree: case StatutAllocation.Cloturee: return "bg-red-100 text-red-700 border-red-300";
        case StatutAllocation.Suspendue: return "bg-gray-100 text-gray-700 border-gray-300";
        default: return "bg-slate-100 text-slate-700 border-slate-300";
    }
};

export const getAllocationColumns = ({ onEdit, onDelete /*, onViewDetails */ }: AllocationColumnsProps): ColumnDef<AllocationColumn>[] => [
  {
    accessorKey: "id",
    header: "ID Alloc.",
    cell: ({ row }) => (
        <Link href={`/allocations/${row.original.id.toString()}`} className="hover:underline text-blue-600 font-mono text-xs" title="Voir détails de l'allocation">
            {row.getValue("id")}
        </Link>
    )
  },
  {
    accessorKey: "ligneGarantie.nom",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Ligne de Garantie <ArrowUpDown className="ml-1 h-3 w-3"/> </Button> ),
  cell: ({ row }) => (
      <Link href={`/lignes-garantie/${row.original.ligneGarantieId}`} className="hover:underline text-blue-600">
          {row.original.ligneGarantie.nom}
      </Link>
  ),
},
{
  accessorKey: "partenaire.nom",
  header: ({ column }) => (
    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
      Partenaire <ArrowUpDown className="ml-1 h-3 w-3"/>
    </Button>
  ),
  cell: ({ row }) => row.original.partenaire.nom,
},
  {
    accessorKey: "montantAlloue",
    header: "Montant Alloué",
    cell: ({ row }) => formatCurrency(row.getValue("montantAlloue"), row.original.ligneGarantie.devise),
  },
  {
    accessorKey: "montantDisponible",
    header: "Montant Disponible (Alloc.)",
    cell: ({ row }) => formatCurrency(row.getValue("montantDisponible"), row.original.ligneGarantie.devise),
  },
  {
    accessorKey: "dateAllocation",
    header: "Date Allocation",
    cell: ({ row }) => formatDate(row.getValue("dateAllocation")),
  },
  {
    accessorKey: "dateExpiration",
    header: "Date Expiration",
    cell: ({ row }) => formatDate(row.getValue("dateExpiration")),
  },
  {
    accessorKey: "statut",
    header: "Statut",
    cell: ({ row }) => <Badge className={getStatutAllocationBadgeVariant(row.getValue("statut"))}>{row.getValue("statut")}</Badge>,
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const allocation = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          {/* onViewDetails && (
            <Button variant="ghost" size="icon" onClick={() => onViewDetails(allocation)} title="Voir Détails & Avenants">
              <Eye className="h-4 w-4" />
            </Button>
          )*/}
          <Button variant="ghost" size="icon" onClick={() => onEdit(allocation)} title="Modifier">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(allocation)} className="text-red-600 hover:text-red-700" title="Supprimer">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];