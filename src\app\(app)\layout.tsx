import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";

const mainNavLinks = [
    // ... autres liens
    { href: "/garanties", label: "Garanties", icon: Shield<PERSON>heck },
    { href: "/suivi/mainlevees", label: "Suivi & Mainlevées", icon: ListChecks },
    // ...
];

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-background">
      <nav className="border-b">
        <div className="container mx-auto px-4">
          <ul className="flex items-center gap-4">
            {mainNavLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href}>
                  <link.icon className="w-5 h-5" />
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>
      <main className="container mx-auto py-6">
        {children}
      </main>
    </div>
  );
} 