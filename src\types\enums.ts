export enum RoleUtilisateur {
  Administrateur = "Administrateur",
  GestionnaireGesGar = "GestionnaireGesGar",
  AnalysteFinancier = "AnalysteFinancier",
  Partenaire = "Partenaire",
  Bailleur = "Bailleur",
  Auditeur = "Auditeur",
}

export enum TypeGarantie {
  Individuelle = "Individuelle",
  Portefeuille = "Portefeuille",
  Cautionnement = "Cautionnement",
}

export enum StatutGarantie {
  EnInstruction = "EnInstruction",
  Echue = "Echue",
  Active = "Active",
  MainleveeDemandee = "MainleveeDemandee",
  MainleveeAccordee = "MainleveeAccordee",
  MainleveeRefusee = "MainleveeRefusee",
  Transferree = "Transferree",
  EnSouffrance = "EnSouffrance",
  MiseEnJeuDemandee = "MiseEnJeuDemandee",
  MiseEnJeuAcceptee = "MiseEnJeuAcceptee",
  MiseEnJeuPayee = "MiseEnJeuPayee",
  MiseEnJeuRefusee = "MiseEnJeuRefusee",
  ClotureeAnormalement = "ClotureeAnormalement",
  Radiee = "Radiee",
  Supprimee = "Supprimee",
}

export enum StatutAllocation {
  Active = "Active",
  Inactive = "Inactive",
  // Ajoute d'autres statuts si besoin
}

export enum TypeMainlevee {
  Totale = "Totale",
  Partielle = "Partielle",
  // Ajoute ici d'autres types si besoin
}

export enum StatutMainlevee {
  Accordee = "Accordee",
  Refusee = "Refusee",
  // Ajoute ici d'autres statuts si besoin
}

export enum StatutMiseEnJeu {
  Demandee = "Demandee",
  EnCoursInstruction = "EnCoursInstruction",
  ApprouveePartiellement = "ApprouveePartiellement",
  ApprouveeTotalement = "ApprouveeTotalement",
  Refusee = "Refusee",
  Payee = "Payee",
  EnAttentePaiement = "EnAttentePaiement",
}