// src/app/api/auth/[...nextauth]/route.ts
import NextAuth, { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import prisma from "@/lib/prisma"; // Notre instance Prisma partagée
import bcrypt from "bcryptjs";
import { RoleUtilisateur } from "@prisma/client";

export const authOptions: NextAuthOptions = {
  // Configure one or more authentication providers
  providers: [
    CredentialsProvider({
      // Le nom à afficher sur le formulaire de connexion (par exemple "Sign in with...")
      name: "Credentials",
      // `credentials` est utilisé pour générer un formulaire sur la page de connexion par défaut.
      // Vous pouvez spécifier les champs que vous attendez.
      // Mais ici, nous allons utiliser notre propre page de connexion, donc c'est plus pour la structure.
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Mot de passe", type: "password" },
      },
      async authorize(credentials, req) {
        // C'est ici que vous ajoutez la logique pour rechercher l'utilisateur
        // à partir des identifiants qu'il a fournis.
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email et mot de passe requis.");
        }

        const user = await prisma.utilisateur.findUnique({
          where: { email: credentials.email },
        });

        if (!user || !user.motDePasse) {
          // Aucun utilisateur trouvé ou l'utilisateur n'a pas de mot de passe (ex: créé via OAuth plus tard)
          throw new Error("Identifiants invalides.");
        }

        // Vérifier le mot de passe
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.motDePasse
        );

        if (!isPasswordValid) {
          throw new Error("Identifiants invalides.");
        }

        if (!user.estActif) {
          throw new Error("Ce compte utilisateur a été désactivé. Veuillez contacter l'administrateur.");
        }
        // Si tout est bon, retournez l'objet utilisateur (sans le mot de passe)
        // Cet objet sera encodé dans le JWT.
        return {
          id: user.id.toString(), // L'ID doit être une chaîne pour NextAuth
          email: user.email,
          name: `${user.prenom} ${user.nom}`, // Ou juste user.nomUtilisateur
          role: user.role, // Nous allons ajouter le rôle à la session
          // Ajoutez d'autres champs utilisateur que vous voulez dans la session ici
        };
      },
    }),
    // ...ajouter d'autres fournisseurs ici plus tard (Google, GitHub, etc.)
  ],
  session: {
    strategy: "jwt", // Nous utilisons JWT pour les sessions
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // `user` is only present during the initial sign-in.
      // The `User` type from `next-auth` has been extended in `src/types/next-auth.d.ts`
      // to include `id` and `role`, matching the object returned by the `authorize` callback.
      if (user) {
        token.id = user.id; // user.id is already a string from authorize callback
        token.role = user.role; // user.role is RoleUtilisateur from authorize callback
      }
      // Si la session est mise à jour (ex: changement de nom d'utilisateur)
      if (trigger === "update" && session?.name) {
        token.name = session.name;
      }
      return token;
    },
    async session({ session, token }) {
      // Envoyer les propriétés du JWT à la session côté client
      if (token) {
        // The `Session` and `JWT` types from `next-auth` have been extended in `src/types/next-auth.d.ts`.
        // session.user.id expects a string, and token.id is already a string.
        // session.user.role expects a string, and token.role is RoleUtilisateur.
        // Prisma's RoleUtilisateur enum values are strings at runtime, so direct assignment is fine.
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/connexion", // Notre page de connexion personnalisée
    // signOut: '/auth/signout', // Optionnel
    // error: '/auth/error', // Page pour afficher les erreurs (par exemple, échec de la connexion)
    // verifyRequest: '/auth/verify-request', // (pour les fournisseurs d'email)
    // newUser: '/auth/inscription' // Redirige les nouveaux utilisateurs ici après la connexion OAuth (si configuré)
  },
  // IMPORTANT: NEXTAUTH_SECRET is crucial for security, especially in production.
  // It MUST be set as an environment variable in all environments (development, staging, production).
  // Use a strong, unique random string. You can generate one using:
  // `openssl rand -hex 32` or `node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"`
  // Store this value securely and do not commit it to your repository if using .env files locally (add .env to .gitignore).
  secret: process.env.NEXTAUTH_SECRET,
  // debug: process.env.NODE_ENV === "development", // Pour voir les logs de NextAuth en dev
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };