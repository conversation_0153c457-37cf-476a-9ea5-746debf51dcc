// src/app/configuration/projets/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Projet, SecteurActivite, ClientBeneficiaire, Utilisateur } from "@prisma/client";
import { ArrowUpDown, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge"; // Pour afficher le secteur par ex.

export type ProjetColumn = Projet & {
    secteurActivite: Pick<SecteurActivite, "id" | "nom">;
    clientBeneficiaire: Pick<ClientBeneficiaire, "id" | "nomOuRaisonSociale">;
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface ProjetColumnsProps {
    onEdit: (projet: ProjetColumn) => void;
    onDelete: (projet: ProjetColumn) => void;
}

export const getProjetColumns = ({ onEdit, onDelete }: ProjetColumnsProps): ColumnDef<ProjetColumn>[] => [
  {
    accessorKey: "nom",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Nom du Projet <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "clientBeneficiaire.nomOuRaisonSociale", // Accès via la relation
    header: "Client Bénéficiaire",
    cell: ({ row }) => row.original.clientBeneficiaire.nomOuRaisonSociale,
  },
  {
    accessorKey: "secteurActivite.nom", // Accès via la relation
    header: "Secteur d'Activité",
    cell: ({ row }) => <Badge variant="outline">{row.original.secteurActivite.nom}</Badge>,
  },
  {
    accessorKey: "coutTotalProjet",
    header: "Coût Total",
    cell: ({ row }) => {
        const raw = row.getValue("coutTotalProjet");
const amount = typeof raw === "string" ? parseFloat(raw.replace(',', '.'))
             : typeof raw === "number" ? raw 
             : typeof raw === "object" && raw !== null && "toNumber" in raw ? (raw as { toNumber: () => number }).toNumber() 
             : NaN;
        if (isNaN(amount)) return "-";
        return new Intl.NumberFormat("fr-FR", { style: "currency", currency: "XOF" }).format(amount); // Adapter la devise
    }
  },
  {
    accessorKey: "dateCreation",
    header: "Créé le",
    cell: ({ row }) => new Date(row.getValue("dateCreation")).toLocaleDateString("fr-FR"),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const projet = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button variant="ghost" size="icon" onClick={() => onEdit(projet)} title="Modifier"><Edit className="h-4 w-4" /></Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(projet)} className="text-red-600 hover:text-red-700" title="Supprimer"><Trash2 className="h-4 w-4" /></Button>
        </div>
      );
    },
  },
];