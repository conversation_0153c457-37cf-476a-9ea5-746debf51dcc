// src/app/(app)/allocations/[id]/components/avenant-allocation-columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { AvenantAllocation, TypeAvenant, Utilisateur } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2 } from "lucide-react"; // Pour futures actions

export type AvenantAllocationColumn = AvenantAllocation & {
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface AvenantAllocationColumnsProps {
    onEdit: (avenant: AvenantAllocationColumn) => void;
    onDelete: (avenant: AvenantAllocationColumn) => void;
}

const formatCurrency = (amount: any, currency: string = "XOF") => {
    if (!amount) return "-";
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : Number(amount);
    return `${numAmount.toLocaleString('fr-FR')} ${currency}`;
};

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

export const getAvenantAllocationColumns = ({ onEdit, onDelete }: AvenantAllocationColumnsProps): ColumnDef<AvenantAllocationColumn>[] => [
  { 
    accessorKey: "dateAvenant", 
    header: "Date Avenant", 
    cell: ({row}) => formatDate(row.getValue("dateAvenant")) 
  },
  { 
    accessorKey: "typeAvenant", 
    header: "Type", 
    cell: ({row}) => <Badge variant="outline">{row.getValue("typeAvenant")}</Badge> 
  },
  {
    accessorKey: "montantModification",
    header: "Modif. Montant",
    cell: ({ row }) => {
        const montant = row.getValue("montantModification");
        // TODO: Récupérer la devise de la ligne de garantie parente de l'allocation pour un formatage correct
        return formatCurrency(montant, "XOF");
    }
  },
  { 
    accessorKey: "nouvelleDateExpiration", 
    header: "Nlle Date Exp.", 
    cell: ({row}) => formatDate(row.getValue("nouvelleDateExpiration")) 
  },
  { 
    accessorKey: "raison", 
    header: "Raison", 
    cell: ({row}) => <span className="truncate block max-w-xs">{row.getValue("raison")}</span> 
  },
  { 
    accessorKey: "referenceDocument", 
    header: "Réf. Doc", 
    cell: ({row}) => row.getValue("referenceDocument") || "-" 
  },
  { 
    accessorKey: "utilisateurCreation.nomUtilisateur", 
    header: "Créé par", 
    cell: ({row}) => row.original.utilisateurCreation?.nomUtilisateur || "N/A"
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const avenant = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button variant="ghost" size="icon" onClick={() => onEdit(avenant)} title="Modifier Avenant">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(avenant)} className="text-red-600 hover:text-red-700" title="Supprimer Avenant">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  }
];