// src/app/api/admin/audit-log/enhanced/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { withSensitiveDataAccess } from "@/lib/audit-wrapper";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }

  // Seuls les administrateurs et les auditeurs peuvent voir les logs d'audit
  if (
    session.user?.role !== RoleUtilisateur.Administrateur &&
    session.user?.role !== RoleUtilisateur.Auditeur
  ) {
    return NextResponse.json({ message: "Accès non autorisé" }, { status: 403 });
  }

// Déplacer la déclaration des variables de filtre ici pour qu'elles soient accessibles dans la métadonnée du wrapper
const { searchParams } = new URL(request.url);
const page = parseInt(searchParams.get("page") || "1");
const limit = parseInt(searchParams.get("limit") || "20");
const utilisateurIdFilter = searchParams.get("utilisateurId");
const actionFilter = searchParams.get("action");
const entiteFilter = searchParams.get("entite");
const moduleFilter = searchParams.get("module");
const criticalityLevelFilter = searchParams.get("criticalityLevel");
const dateDebutFilter = searchParams.get("dateDebut");
const dateFinFilter = searchParams.get("dateFin");
const searchTerm = searchParams.get("search");
const isExport = searchParams.get("export") === "true";

const skip = (page - 1) * limit;

return withSensitiveDataAccess(
    async () => {
        // Construire la clause WHERE
        const whereClause: any = {};

        if (utilisateurIdFilter && !isNaN(parseInt(utilisateurIdFilter))) {
            whereClause.utilisateurId = parseInt(utilisateurIdFilter);
        }

        if (actionFilter) {
            whereClause.action = { contains: actionFilter, mode: 'insensitive' };
        }

        if (entiteFilter) {
            whereClause.entite = { contains: entiteFilter, mode: 'insensitive' };
        }

        if (moduleFilter) {
            whereClause.module = moduleFilter;
        }

        if (criticalityLevelFilter) {
            whereClause.criticalityLevel = criticalityLevelFilter;
        }

        if (dateDebutFilter) {
            whereClause.timestamp = { 
                ...whereClause.timestamp, 
                gte: new Date(dateDebutFilter) 
            };
        }

        if (dateFinFilter) {
            const endDate = new Date(dateFinFilter);
            endDate.setDate(endDate.getDate() + 1);
            whereClause.timestamp = { 
                ...whereClause.timestamp, 
                lt: endDate 
            };
        }

        // Recherche textuelle dans la description et les métadonnées
        if (searchTerm) {
            whereClause.OR = [
                { descriptionAction: { contains: searchTerm, mode: 'insensitive' } },
                { action: { contains: searchTerm, mode: 'insensitive' } },
                { entite: { contains: searchTerm, mode: 'insensitive' } },
                { 
                    utilisateur: {
                        OR: [
                            { nomUtilisateur: { contains: searchTerm, mode: 'insensitive' } },
                            { nom: { contains: searchTerm, mode: 'insensitive' } },
                            { prenom: { contains: searchTerm, mode: 'insensitive' } }
                        ]
                    }
                }
            ];
        }

        try {
            // Si c'est un export, récupérer toutes les données
            const queryOptions: any = {
                where: whereClause,
                include: {
                    utilisateur: {
                        select: {
                            id: true,
                            nomUtilisateur: true,
                            nom: true,
                            prenom: true,
                        },
                    },
                },
                orderBy: {
                    timestamp: "desc",
                },
            };

            if (!isExport) {
                queryOptions.skip = skip;
                queryOptions.take = limit;
            }

            const auditLogs = await prisma.auditLog.findMany({
                ...queryOptions,
                include: {
                    ...queryOptions.include,
                    utilisateur: {
                        select: {
                            id: true,
                            nomUtilisateur: true,
                            nom: true,
                            prenom: true,
                        },
                    },
                },
            });

            if (isExport) {
                // Générer un CSV pour l'export
                const csvHeaders = [
                    'Date/Heure',
                    'Utilisateur',
                    'Action',
                    'Module',
                    'Entité',
                    'ID Entité',
                    'Niveau Criticité',
                    'Description',
                    'Adresse IP',
                    'Session ID',
                    'Request ID'
                ].join(',');

                const csvRows = auditLogs.map((log: any) => [
                    log.timestamp.toISOString(),
                    log.utilisateur ? `${log.utilisateur.prenom} ${log.utilisateur.nom} (${log.utilisateur.nomUtilisateur})` : 'Système',
                    log.action,
                    log.module || '',
                    log.entite || '',
                    log.entiteId || '',
                    log.criticalityLevel || 'MEDIUM',
                    `"${(log.descriptionAction || '').replace(/"/g, '""')}"`,
                    log.adresseIp || '',
                    log.sessionId || '',
                    log.requestId || ''
                ].join(','));

                const csvContent = [csvHeaders, ...csvRows].join('\n');

                return new NextResponse(csvContent, {
                    headers: {
                        'Content-Type': 'text/csv',
                        'Content-Disposition': `attachment; filename="audit-log-${new Date().toISOString().split('T')[0]}.csv"`
                    }
                });
            }

            // Compter le total pour la pagination
            const totalLogs = await prisma.auditLog.count({ where: whereClause });

            return NextResponse.json({
                data: auditLogs,
                totalPages: Math.ceil(totalLogs / limit),
                currentPage: page,
                totalRecords: totalLogs,
            });

        } catch (error) {
            console.error("Erreur lors de la récupération des logs d'audit:", error);
            return NextResponse.json(
                { message: "Erreur interne du serveur lors de la récupération des logs d'audit" },
                { status: 500 }
            );
        }
    },
    {
        dataType: 'AuditLog',
        accessReason: 'Consultation interface d\'administration',
        metadata: {
            filters: {
                utilisateurId: utilisateurIdFilter,
                action: actionFilter,
                entite: entiteFilter,
                module: moduleFilter,
                criticalityLevel: criticalityLevelFilter,
                dateRange: { debut: dateDebutFilter, fin: dateFinFilter },
                search: searchTerm
            },
            isExport,
            page,
            limit
        }
    }
);
}