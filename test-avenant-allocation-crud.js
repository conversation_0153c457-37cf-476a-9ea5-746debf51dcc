// test-avenant-allocation-crud.js
// Script pour tester les opérations CRUD sur les avenants d'allocation

const BASE_URL = 'http://localhost:3000';

// Fonction utilitaire pour faire des requêtes
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    
    console.log(`${options.method || 'GET'} ${url}`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---');
    
    return { response, data };
  } catch (error) {
    console.error(`Erreur lors de la requête ${url}:`, error);
    return { error };
  }
}

async function testAvenantAllocationCRUD() {
  console.log('=== TEST CRUD AVENANTS D\'ALLOCATION ===\n');
  
  // Supposons qu'on a une allocation avec l'ID 1
  const allocationId = 1;
  
  // 1. Test création d'un avenant d'allocation
  console.log('1. Test création d\'un avenant d\'allocation');
  const createData = {
    typeAvenant: 'AUGMENTATION_MONTANT',
    montantModificationStr: '500000',
    dateAvenant: new Date().toISOString(),
    raison: 'Augmentation du montant suite à révision du projet',
    referenceDocument: 'REF-2025-001'
  };
  
  const { data: createdAvenant } = await makeRequest(
    `${BASE_URL}/api/allocations/${allocationId}/avenants`,
    {
      method: 'POST',
      body: JSON.stringify(createData)
    }
  );
  
  if (!createdAvenant || createdAvenant.error) {
    console.log('❌ Échec de la création d\'avenant');
    return;
  }
  
  const avenantId = createdAvenant.id;
  console.log(`✅ Avenant créé avec l'ID: ${avenantId}\n`);
  
  // 2. Test récupération d'un avenant spécifique
  console.log('2. Test récupération d\'un avenant spécifique');
  await makeRequest(`${BASE_URL}/api/allocations/${allocationId}/avenants/${avenantId}`);
  
  // 3. Test modification d'un avenant
  console.log('3. Test modification d\'un avenant');
  const updateData = {
    typeAvenant: 'AUGMENTATION_MONTANT',
    montantModificationStr: '750000', // Modification du montant
    dateAvenant: createdAvenant.dateAvenant,
    raison: 'Augmentation du montant suite à révision du projet - Montant ajusté',
    referenceDocument: 'REF-2025-001-MODIF'
  };
  
  await makeRequest(
    `${BASE_URL}/api/allocations/${allocationId}/avenants/${avenantId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  );
  
  // 4. Test suppression d'un avenant
  console.log('4. Test suppression d\'un avenant');
  await makeRequest(
    `${BASE_URL}/api/allocations/${allocationId}/avenants/${avenantId}`,
    {
      method: 'DELETE'
    }
  );
  
  // 5. Vérification que l'avenant a été supprimé
  console.log('5. Vérification de la suppression');
  await makeRequest(`${BASE_URL}/api/allocations/${allocationId}/avenants/${avenantId}`);
  
  console.log('=== FIN DES TESTS ===');
}

// Test avec différents types d'avenants
async function testDifferentAvenantTypes() {
  console.log('\n=== TEST DIFFÉRENTS TYPES D\'AVENANTS ===\n');
  
  const allocationId = 1;
  
  // Test avenant de prolongation
  console.log('Test avenant de prolongation de durée');
  const prolongationData = {
    typeAvenant: 'PROLONGATION_DUREE',
    nouvelleDateExpiration: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // +1 an
    dateAvenant: new Date().toISOString(),
    raison: 'Prolongation de la durée de l\'allocation',
    referenceDocument: 'PROLONG-2025-001'
  };
  
  await makeRequest(
    `${BASE_URL}/api/allocations/${allocationId}/avenants`,
    {
      method: 'POST',
      body: JSON.stringify(prolongationData)
    }
  );
  
  // Test avenant de réduction
  console.log('Test avenant de réduction de montant');
  const reductionData = {
    typeAvenant: 'REDUCTION_MONTANT',
    montantModificationStr: '200000',
    dateAvenant: new Date().toISOString(),
    raison: 'Réduction du montant suite à révision budgétaire',
    referenceDocument: 'REDUC-2025-001'
  };
  
  await makeRequest(
    `${BASE_URL}/api/allocations/${allocationId}/avenants`,
    {
      method: 'POST',
      body: JSON.stringify(reductionData)
    }
  );
}

// Exécuter les tests
async function runAllTests() {
  await testAvenantAllocationCRUD();
  await testDifferentAvenantTypes();
}

// Vérifier si le script est exécuté directement
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  global.fetch = fetch;
  runAllTests().catch(console.error);
} else {
  // Browser environment
  console.log('Script prêt. Appelez runAllTests() pour exécuter les tests.');
}