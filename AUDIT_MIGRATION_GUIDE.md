# Guide de Migration - Système d'Audit Amélioré

## Vue d'ensemble

Ce guide détaille la migration du système d'audit existant vers le nouveau système d'audit amélioré qui offre une traçabilité complète et systématique de toutes les actions utilisateur.

## Nouveautés du Système d'Audit Amélioré

### 1. Champs Étendus dans AuditLog

Le modèle `AuditLog` a été étendu avec les nouveaux champs :
- `sessionId` : Identifiant de session utilisateur
- `requestId` : Identifiant unique de requête
- `module` : Module fonctionnel (GARANTIES, ALLOCATIONS, etc.)
- `operation` : Opération métier spécifique
- `resourceId` : ID de la ressource concernée
- `criticalityLevel` : Niveau de criticité (LOW, MEDIUM, HIGH, CRITICAL)
- `metadata` : Métadonnées supplémentaires au format JSON

### 2. Actions Métier Standardisées

Plus de 50 actions métier prédéfinies dans l'enum `BusinessAction` :
- Authentification : `LOGIN`, `LOGOUT`, `LOGIN_FAILED`, etc.
- Gestion utilisateurs : `USER_CREATE`, `USER_UPDATE`, `USER_DEACTIVATE`, etc.
- Opérations financières : `MISE_EN_JEU_PAYMENT`, `PAIEMENT_CREATE`, etc.
- Configuration système : `SYSTEM_SETTING_UPDATE`, etc.

### 3. Wrappers d'Audit Spécialisés

- `withAudit()` : Wrapper générique
- `withAuthAudit()` : Pour l'authentification
- `withFinancialAudit()` : Pour les opérations financières
- `withUserManagementAudit()` : Pour la gestion des utilisateurs
- `withSystemConfigAudit()` : Pour la configuration système
- `withSensitiveDataAccess()` : Pour l'accès aux données sensibles
- `withDataTransferAudit()` : Pour les imports/exports

## Migration des Routes API

### Étape 1 : Remplacer l'ancien système

**Avant :**
```typescript
import { auditContext } from '@/lib/prisma-audit.middleware';

return auditContext.run({ userId: creatorId, ip, userAgent }, async () => {
  // logique métier
});
```

**Après :**
```typescript
import { withAudit, BusinessAction } from '@/lib/audit-wrapper';

return withAudit(
  async () => {
    // logique métier
  },
  {
    module: 'ALLOCATIONS',
    operation: BusinessAction.ALLOCATION_CREATE,
    resourceId: allocationId,
    metadata: { /* données contextuelles */ }
  }
);
```

### Étape 2 : Routes à Migrer par Module

#### Module Authentification
- `src/app/api/auth/[...nextauth]/route.ts`
- `src/app/api/auth/register/route.ts`

**Exemple de migration :**
```typescript
// Utiliser withAuthAudit pour les opérations d'authentification
await withAuthAudit(
  async () => { /* logique de connexion */ },
  BusinessAction.LOGIN,
  {
    username: nomUtilisateur,
    success: true,
    metadata: { loginMethod: 'credentials' }
  }
);
```

#### Module Gestion des Utilisateurs
- `src/app/api/admin/users/route.ts`
- `src/app/api/admin/users/[id]/route.ts`

**Exemple de migration :**
```typescript
await withUserManagementAudit(
  async () => { /* création utilisateur */ },
  BusinessAction.USER_CREATE,
  {
    targetUserId: newUser.id,
    targetUsername: newUser.nomUtilisateur,
    changes: userData
  }
);
```

#### Module Configuration
- `src/app/api/configuration/bailleurs/route.ts`
- `src/app/api/configuration/partenaires/route.ts`
- `src/app/api/configuration/projets/route.ts`
- `src/app/api/configuration/secteurs-activite/route.ts`
- `src/app/api/configuration/clients-beneficiaires/route.ts`

#### Module Garanties et Allocations
- `src/app/api/allocations/route.ts`
- `src/app/api/allocations/[id]/route.ts`
- `src/app/api/lignes-garantie/route.ts`
- `src/app/api/garanties/route.ts`

#### Module Processus Métier
- `src/app/api/mainlevees/route.ts`
- `src/app/api/mises-en-jeu/route.ts`
- `src/app/api/garanties/[id]/mainlevees/route.ts`
- `src/app/api/garanties/[id]/mises-en-jeu/route.ts`

#### Module Avenants
- `src/app/api/allocations/[id]/avenants/route.ts`
- `src/app/api/lignes-garantie/[Id]/avenants/route.ts`

#### Module Administration
- `src/app/api/admin/system-settings/route.ts`
- `src/app/api/admin/audit-log/route.ts`

### Étape 3 : Ajout d'Audit pour Nouvelles Fonctionnalités

#### Accès aux Données Sensibles
```typescript
await withSensitiveDataAccess(
  async () => {
    return await prisma.auditLog.findMany({ /* critères */ });
  },
  {
    dataType: 'AuditLog',
    accessReason: 'Consultation logs d\'audit',
    metadata: { filters: searchCriteria }
  }
);
```

#### Imports/Exports de Données
```typescript
await withDataTransferAudit(
  async () => {
    // logique d'export
  },
  BusinessAction.DATA_EXPORT,
  {
    dataType: 'Garanties',
    recordCount: exportedRecords.length,
    fileInfo: { name: 'export.xlsx', size: fileSize, type: 'xlsx' }
  }
);
```

#### Opérations Financières
```typescript
await withFinancialAudit(
  async () => {
    // traitement paiement
  },
  BusinessAction.MISE_EN_JEU_PAYMENT,
  {
    entityType: 'MiseEnJeu',
    entityId: miseEnJeuId,
    amount: montant,
    currency: 'XOF',
    reference: referencePaiement
  }
);
```

## Audit des Actions Personnalisées

Pour les actions qui ne passent pas par Prisma :

```typescript
import { logCustomAuditEvent, BusinessAction } from '@/lib/audit-wrapper';

await logCustomAuditEvent(BusinessAction.DOCUMENT_DOWNLOAD, {
  entite: 'Document',
  entiteId: documentId,
  description: `Téléchargement du document ${fileName}`,
  metadata: {
    fileName,
    fileSize,
    downloadReason: 'Consultation dossier'
  },
  criticalityLevel: 'MEDIUM'
});
```

## Gestion des Erreurs avec Audit

```typescript
import { logSystemError } from '@/lib/audit-wrapper';

try {
  // opération risquée
} catch (error) {
  await logSystemError(error as Error, {
    operation: 'CREATION_GARANTIE',
    module: 'GARANTIES',
    userId: session.user.id,
    metadata: { garantieData }
  });
  throw error;
}
```

## Interface d'Administration Étendue

### Nouveaux Filtres dans l'Audit Log
- Filtrage par niveau de criticité
- Filtrage par module
- Filtrage par type d'opération
- Recherche dans les métadonnées

### Colonnes Supplémentaires
- Module
- Opération
- Niveau de criticité
- Session ID
- Request ID

## Conformité et Sécurité

### Niveaux de Criticité
- **CRITICAL** : Modifications système, gestion utilisateurs
- **HIGH** : Opérations financières, suppressions, accès données sensibles
- **MEDIUM** : Modifications données métier
- **LOW** : Consultations, créations simples

### Rétention des Logs
- Logs CRITICAL : Conservation 7 ans minimum
- Logs HIGH : Conservation 5 ans minimum
- Logs MEDIUM/LOW : Conservation 3 ans minimum

### Alertes Automatiques
- Tentatives d'accès non autorisé
- Modifications système critiques
- Échecs d'opérations financières
- Accès anormaux aux données sensibles

## Checklist de Migration

### Phase 1 : Infrastructure
- [x] Mise à jour du schéma Prisma
- [x] Migration de la base de données
- [x] Création du middleware amélioré
- [x] Création des wrappers d'audit

### Phase 2 : Migration des Routes (À faire)
- [ ] Routes d'authentification
- [ ] Routes de gestion des utilisateurs
- [ ] Routes de configuration
- [ ] Routes des garanties et allocations
- [ ] Routes des processus métier
- [ ] Routes d'administration

### Phase 3 : Interface Utilisateur (À faire)
- [ ] Mise à jour de l'interface d'audit log
- [ ] Ajout des nouveaux filtres
- [ ] Tableaux de bord d'audit
- [ ] Alertes en temps réel

### Phase 4 : Tests et Validation (À faire)
- [ ] Tests unitaires des wrappers
- [ ] Tests d'intégration
- [ ] Validation de la performance
- [ ] Tests de sécurité

## Exemples d'Implémentation

Voir les fichiers d'exemple :
- `src/app/api/auth/login-audit/route.ts` : Audit d'authentification
- `src/app/api/audit-examples/financial-operations/route.ts` : Audit financier

## Support et Documentation

Pour toute question sur la migration :
1. Consulter les exemples d'implémentation
2. Vérifier les types TypeScript dans `src/lib/audit-wrapper.ts`
3. Tester avec les routes d'exemple avant migration complète