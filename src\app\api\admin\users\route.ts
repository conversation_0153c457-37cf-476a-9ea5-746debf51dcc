// src/app/api/admin/users/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez si votre chemin est différent
import { RoleUtilisateur } from "@prisma/client";
import bcrypt from "bcryptjs";
import { CreateUserSchema } from "@/lib/schemas/user.schema";
import { auditContext } from '@/lib/prisma-audit.middleware'; // Importer le contexte d'audit
import { headers } from 'next/headers'; // Pour récupérer IP et User-Agent

// GET: Lister les utilisateurs actifs
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }
  if (session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Accès non autorisé" }, { status: 403 });
  }

  try {
    const users = await prisma.utilisateur.findMany({
      where: {
        estActif: true, // Ne récupérer que les utilisateurs actifs
      },
      select: {
        id: true,
        nomUtilisateur: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        photoUrl: true,
        dateCreation: true,
        estActif: true,
      },
      orderBy: {
        nom: "asc",
      },
    });
    return NextResponse.json(users);
  } catch (error) {
    console.error("Erreur lors de la récupération des utilisateurs:", error);
    return NextResponse.json(
      { message: "Erreur interne du serveur lors de la récupération des utilisateurs" },
      { status: 500 }
    );
  }
}

// POST: Créer un nouvel utilisateur
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }
  if (session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Accès non autorisé" }, { status: 403 });
  }

  const adminCreatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  if (!adminCreatorId) {
    console.error("ID de l'administrateur créateur non trouvé dans la session pour POST /api/admin/users.");
    return NextResponse.json({ message: "Erreur lors de l'identification du créateur." }, { status: 500 });
  }

  // Vérification que l'utilisateur créateur existe bien dans la base
  const adminCreatorExists = await prisma.utilisateur.findUnique({ where: { id: adminCreatorId } });
  if (!adminCreatorExists) {
    console.error(`L'utilisateur créateur (id=${adminCreatorId}) n'existe pas dans la base.`);
    return NextResponse.json({ message: "L'utilisateur créateur n'existe pas ou a été supprimé." }, { status: 400 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || headersList.get('cf-connecting-ip') || request.headers.get('x-client-ip') || request.headers.get('x-forwarded-for')?.split(',')[0].trim() || null;
  const userAgentHeader = headersList.get('user-agent') || null;


  // Exécuter la logique dans le contexte d'audit
  return auditContext.run({ userId: adminCreatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = CreateUserSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json(
          { message: "Données invalides", errors: validation.error.formErrors.fieldErrors },
          { status: 400 }
        );
      }

      const { nomUtilisateur, email, nom, prenom, role, motDePasse } = validation.data;

      const existingUserByEmail = await prisma.utilisateur.findUnique({ where: { email } });
      if (existingUserByEmail) {
        return NextResponse.json({ message: "Un utilisateur avec cet email existe déjà." }, { status: 409 });
      }
      const existingUserByUsername = await prisma.utilisateur.findUnique({ where: { nomUtilisateur } });
      if (existingUserByUsername) {
        return NextResponse.json({ message: "Ce nom d'utilisateur est déjà pris." }, { status: 409 });
      }

      const hashedPassword = await bcrypt.hash(motDePasse, 10);

      const newUser = await prisma.utilisateur.create({
        data: {
          nomUtilisateur,
          email,
          nom,
          prenom,
          role: role as RoleUtilisateur,
          motDePasse: hashedPassword,
          utilisateurCreationId: adminCreatorId, // Enregistrer qui a créé l'utilisateur
          estActif: true, // Par défaut, un nouvel utilisateur est actif
        },
      });

      const { motDePasse: _, ...userToReturn } = newUser;
      return NextResponse.json(userToReturn, { status: 201 });

    } catch (error) {
      console.error("Erreur lors de la création de l'utilisateur (POST /api/admin/users):", error);
      return NextResponse.json(
        { message: "Erreur interne du serveur lors de la création de l'utilisateur" },
        { status: 500 }
      );
    }
  });
}