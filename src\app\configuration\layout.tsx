import React from "react";
import Link from "next/link";
import { getServerSession } from "next-auth/next"; // Pour récupérer la session côté serveur
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez le chemin
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  LogOut,
  Settings,
  UserCircle,
  LayoutDashboard,
  Users,
  FileText,
  BarChart2,
  Building,
  Landmark,
  BriefcaseBusiness,
  FolderKanban,
} from "lucide-react"; // Icônes

// Liens de navigation pour la barre latérale

const configurationLinks = [
  {
    href: "/configuration/bailleurs",
    label: "Bailleurs de Fonds",
    icon: Landmark,
  },
  { href: "/configuration/partenaires", label: "Partenaires Financiers", icon: Building },
  { href: "/configuration/clients-beneficiaires", label: "Clients Bénéficiaires", icon: Users },
  { href: "/configuration/secteurs-activite", label: "Secteurs d'Activité", icon: BriefcaseBusiness },
  { href: "/configuration/projets", label: "Projets", icon: FolderKanban },
];
export default async function ConfigurationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions); // Récupérer la session côté serveur

  // Idéalement, le middleware devrait déjà avoir géré la redirection si non admin
  // Mais une vérification ici est une bonne pratique pour le rendu côté serveur du layout
  if (!session || session.user?.role !== "Administrateur") {
    // Vous pourriez rediriger ici ou afficher un message d'accès refusé
    // Pour un layout, il est souvent préférable de laisser le middleware gérer la redirection
    // et ici, potentiellement, ne pas rendre le layout admin si la condition n'est pas remplie
    // ou rendre une version minimale.
    // Pour l'instant, on assume que le middleware a fait son travail.
  }

  const userInitial =
    session?.user?.name?.charAt(0).toUpperCase() ||
    session?.user?.email?.charAt(0).toUpperCase() ||
    "U";

  return (
    <div className="flex flex-col flex-1">
      {/* Header si tu veux le garder, sinon retire-le */}
      <main className="flex-1 p-6">{children}</main>
    </div>
  );
}
