// src/app/api/configuration/secteurs-activite/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { SecteurActiviteSchema } from "@/lib/schemas/secteur-activite.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

interface RouteParams { params: { id: string } }

// GET: Récupérer un secteur spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  try {
    const secteur = await prisma.secteurActivite.findUnique({ where: { id }});
    if (!secteur) return NextResponse.json({ message: "Secteur d'activité non trouvé" }, { status: 404 });
    return NextResponse.json(secteur);
  } catch (error) {
    console.error(`Erreur GET /api/configuration/secteurs-activite/${id}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// PUT: Mettre à jour un secteur d'activité
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: modifierId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = SecteurActiviteSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }
      const { nom, code, description } = validation.data;

      const existingNom = await prisma.secteurActivite.findFirst({ where: { nom, id: { not: id } } });
      if (existingNom) {
        return NextResponse.json({ message: "Un autre secteur avec ce nom existe déjà." }, { status: 409 });
      }
      if (code && code.trim() !== "") {
        const existingCode = await prisma.secteurActivite.findFirst({ where: { code, id: { not: id } } });
        if (existingCode) {
          return NextResponse.json({ message: "Un autre secteur avec ce code existe déjà." }, { status: 409 });
        }
      }

      const updatedSecteur = await prisma.secteurActivite.update({
        where: { id },
        data: {
          nom,
          code: (code && code.trim() !== "") ? code : null,
          description,
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedSecteur);
    } catch (error: any) {
      console.error(`Erreur PUT /api/configuration/secteurs-activite/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Secteur non trouvé" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// DELETE: Supprimer un secteur d'activité
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const deleterId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: deleterId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      // Vérifier si le secteur est lié à des Projets ou ClientsBeneficiaires
      const projetsCount = await prisma.projet.count({ where: { secteurActiviteId: id } });
      if (projetsCount > 0) {
        return NextResponse.json({ message: `Impossible de supprimer: lié à ${projetsCount} projet(s).` }, { status: 400 });
      }
      const clientsCount = await prisma.clientBeneficiaire.count({ where: { secteurActivitePrincipalId: id } });
      if (clientsCount > 0) {
        return NextResponse.json({ message: `Impossible de supprimer: lié à ${clientsCount} client(s).` }, { status: 400 });
      }

      await prisma.secteurActivite.delete({ where: { id } });
      return NextResponse.json({ message: "Secteur d'activité supprimé avec succès" }, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur DELETE /api/configuration/secteurs-activite/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Secteur non trouvé" }, { status: 404 });
      if (error.code === 'P2003') return NextResponse.json({ message: "Impossible de supprimer: référencé ailleurs." }, { status: 400 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}