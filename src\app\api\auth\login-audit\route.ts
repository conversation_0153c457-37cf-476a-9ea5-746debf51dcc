// src/app/api/auth/login-audit/route.ts
// Exemple d'implémentation de l'audit pour l'authentification

import { NextResponse } from "next/server";
import { withAuthAudit, BusinessAction } from "@/lib/audit-wrapper";
import bcrypt from "bcryptjs";
import prisma from "@/lib/prisma";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { nomUtilisateur, motDePasse } = body;

    if (!nomUtilisateur || !motDePasse) {
      return NextResponse.json(
        { message: "Nom d'utilisateur et mot de passe requis" },
        { status: 400 }
      );
    }

    // Utiliser le wrapper d'audit pour l'authentification
    const result = await withAuthAudit(
      async () => {
        // Rechercher l'utilisateur
        const utilisateur = await prisma.utilisateur.findUnique({
          where: { nomUtilisateur }
        });

        if (!utilisateur) {
          throw new Error("Utilisateur non trouvé");
        }

        if (!utilisateur.estActif) {
          throw new Error("Compte désactivé");
        }

        // Vérifier le mot de passe
        const motDePasseValide = await bcrypt.compare(motDePasse, utilisateur.motDePasse);
        
        if (!motDePasseValide) {
          throw new Error("Mot de passe incorrect");
        }

        return {
          id: utilisateur.id,
          nomUtilisateur: utilisateur.nomUtilisateur,
          nom: utilisateur.nom,
          prenom: utilisateur.prenom,
          email: utilisateur.email,
          role: utilisateur.role
        };
      },
      BusinessAction.LOGIN,
      {
        username: nomUtilisateur,
        success: true,
        metadata: {
          loginMethod: 'credentials',
          userAgent: request.headers.get('user-agent'),
          timestamp: new Date().toISOString()
        }
      }
    );

    return NextResponse.json({
      message: "Connexion réussie",
      user: result
    });

  } catch (error) {
    // L'audit de l'échec est géré automatiquement par withAuthAudit
    return NextResponse.json(
      { message: (error as Error).message },
      { status: 401 }
    );
  }
}