// src/app/api/auth/register/route.ts
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { NextResponse } from "next/server";
import { RoleUtilisateur } from "@prisma/client"; // Importer l'enum

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { nomUtilisateur, email, password, nom, prenom } = body;

    if (!nomUtilisateur || !email || !password || !nom || !prenom) {
      return NextResponse.json(
        { message: "Tous les champs sont requis." },
        { status: 400 }
      );
    }

    // Vérifier si l'utilisateur existe déjà (email ou nomUtilisateur)
    const existingUserByEmail = await prisma.utilisateur.findUnique({
      where: { email },
    });
    if (existingUserByEmail) {
      return NextResponse.json(
        { message: "Un utilisateur avec cet email existe déjà." },
        { status: 409 } // 409 Conflict
      );
    }
    const existingUserByUsername = await prisma.utilisateur.findUnique({
      where: { nomUtilisateur },
    });
    if (existingUserByUsername) {
      return NextResponse.json(
        { message: "Ce nom d'utilisateur est déjà pris." },
        { status: 409 }
      );
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10); // 10 est le "salt rounds"

    // Créer l'utilisateur
    // Pour l'instant, assignons un rôle par défaut.
    // Plus tard, la création d'utilisateur sera gérée par un admin.
    const newUser = await prisma.utilisateur.create({
      data: {
        nomUtilisateur,
        email,
        motDePasse: hashedPassword,
        nom,
        prenom,
        role: RoleUtilisateur.GestionnaireGesGar, // Rôle par défaut pour l'inscription
        // photoUrl: null, // Optionnel
        // utilisateurCreationId: null, // Pour l'instant, car pas d'utilisateur connecté pour créer
      },
    });

    // Ne pas retourner le mot de passe haché
    const { motDePasse, ...userWithoutPassword } = newUser;

    return NextResponse.json(
      { user: userWithoutPassword, message: "Utilisateur créé avec succès" },
      { status: 201 }
    );

  } catch (error) {
    console.error("Erreur d'inscription:", error);
    return NextResponse.json(
      { message: "Une erreur interne s'est produite." },
      { status: 500 }
    );
  }
}