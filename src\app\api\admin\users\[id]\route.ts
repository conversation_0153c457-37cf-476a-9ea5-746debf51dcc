// src/app/api/admin/users/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez si votre chemin est différent
import { RoleUtilisateur } from "@prisma/client";
import bcrypt from "bcryptjs";
import { UpdateUserSchema } from "@/lib/schemas/user.schema";
import { auditContext } from '@/lib/prisma-audit.middleware'; // Importer le contexte d'audit
import { headers } from 'next/headers'; // Pour récupérer IP et User-Agent

interface RouteParams {
  params: { id: string };
}

// GET: Récupérer un utilisateur spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userId = parseInt(resolvedParams.id);
  if (isNaN(userId)) {
    return NextResponse.json({ message: "ID utilisateur invalide" }, { status: 400 });
  }

  try {
    const user = await prisma.utilisateur.findUnique({
      where: { id: userId },
      select: {
        id: true,
        nomUtilisateur: true,
        email: true,
        nom: true,
        prenom: true,
        role: true,
        photoUrl: true,
        estActif: true, // Inclure pour le formulaire de modification si nécessaire
      },
    });

    if (!user) {
      return NextResponse.json({ message: "Utilisateur non trouvé" }, { status: 404 });
    }
    return NextResponse.json(user);
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'utilisateur ${userId} (GET /api/admin/users/[id]):`, error);
    return NextResponse.json(
      { message: "Erreur interne du serveur lors de la récupération de l'utilisateur" },
      { status: 500 }
    );
  }
}

// PUT: Mettre à jour un utilisateur
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userIdToUpdate = parseInt(resolvedParams.id);
  if (isNaN(userIdToUpdate)) {
    return NextResponse.json({ message: "ID utilisateur invalide" }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  if (!modifierId) {
    console.error("ID de l'administrateur modificateur non trouvé dans la session pour PUT /api/admin/users/[id].");
    return NextResponse.json({ message: "Erreur lors de l'identification du modificateur." }, { status: 500 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || headersList.get('cf-connecting-ip') || request.headers.get('x-client-ip') || request.headers.get('x-forwarded-for')?.split(',')[0].trim() || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: modifierId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = UpdateUserSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json(
          { message: "Données invalides", errors: validation.error.formErrors.fieldErrors },
          { status: 400 }
        );
      }

      const { nomUtilisateur, email, nom, prenom, role, motDePasse } = validation.data;

      const existingUserByEmail = await prisma.utilisateur.findFirst({
        where: { email, id: { not: userIdToUpdate } },
      });
      if (existingUserByEmail) {
        return NextResponse.json({ message: "Cet email est déjà utilisé par un autre compte." }, { status: 409 });
      }
      const existingUserByUsername = await prisma.utilisateur.findFirst({
        where: { nomUtilisateur, id: { not: userIdToUpdate } },
      });
      if (existingUserByUsername) {
        return NextResponse.json({ message: "Ce nom d'utilisateur est déjà utilisé par un autre compte." }, { status: 409 });
      }

      const dataToUpdate: any = {
        nomUtilisateur,
        email,
        nom,
        prenom,
        role,
        utilisateurModificationId: modifierId,
      };

      if (motDePasse && motDePasse.trim() !== "") {
        dataToUpdate.motDePasse = await bcrypt.hash(motDePasse, 10);
      }
      // Si vous voulez permettre de changer le statut 'estActif' via ce formulaire, ajoutez-le ici.
      // if (typeof validation.data.estActif === 'boolean') {
      //   dataToUpdate.estActif = validation.data.estActif;
      // }


      const updatedUser = await prisma.utilisateur.update({
        where: { id: userIdToUpdate },
        data: dataToUpdate,
      });

      const { motDePasse: _, ...userToReturn } = updatedUser;
      return NextResponse.json(userToReturn);

    } catch (error: any) {
      console.error(`Erreur lors de la mise à jour de l'utilisateur ${userIdToUpdate} (PUT /api/admin/users/[id]):`, error);
      if (error.code === 'P2025') {
        return NextResponse.json({ message: "Utilisateur non trouvé pour la mise à jour" }, { status: 404 });
      }
      return NextResponse.json(
        { message: "Erreur interne du serveur lors de la mise à jour de l'utilisateur" },
        { status: 500 }
      );
    }
  });
}

// DELETE: Désactiver (suppression logique) un utilisateur
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userIdToDeactivate = parseInt(resolvedParams.id);
  if (isNaN(userIdToDeactivate)) {
    return NextResponse.json({ message: "ID utilisateur invalide" }, { status: 400 });
  }

  const deactivatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  if (!deactivatorId) {
    console.error("ID de l'administrateur désactivateur non trouvé dans la session pour DELETE /api/admin/users/[id].");
    return NextResponse.json({ message: "Erreur lors de l'identification du désactivateur." }, { status: 500 });
  }

  // Empêcher l'auto-désactivation de l'admin connecté
  if (deactivatorId === userIdToDeactivate) {
    return NextResponse.json({ message: "Vous ne pouvez pas désactiver votre propre compte administrateur." }, { status: 400 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || headersList.get('cf-connecting-ip') || request.headers.get('x-client-ip') || request.headers.get('x-forwarded-for')?.split(',')[0].trim() || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: deactivatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const userToDeactivate = await prisma.utilisateur.findUnique({
        where: { id: userIdToDeactivate },
      });

      if (!userToDeactivate) {
        return NextResponse.json({ message: "Utilisateur non trouvé" }, { status: 404 });
      }

      if (!userToDeactivate.estActif) {
        // Optionnel: retourner un message spécifique si déjà inactif
        // return NextResponse.json({ message: "L'utilisateur est déjà inactif." }, { status: 200 });
      }

      await prisma.utilisateur.update({
        where: { id: userIdToDeactivate },
        data: {
          estActif: false,
          utilisateurModificationId: deactivatorId,
        },
      });

      return NextResponse.json({ message: "Utilisateur désactivé avec succès" }, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur lors de la désactivation de l'utilisateur ${userIdToDeactivate} (DELETE /api/admin/users/[id]):`, error);
      if (error.code === 'P2025') {
        return NextResponse.json({ message: "Utilisateur non trouvé pour la désactivation" }, { status: 404 });
      }
      return NextResponse.json(
        { message: "Erreur interne du serveur lors de la désactivation de l'utilisateur" },
        { status: 500 }
      );
    }
  });
}