// src/app/configuration/projets/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, Projet, SecteurActivite, ClientBeneficiaire } from "@prisma/client"; // Importer les types
import { getProjetColumns, ProjetColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog";
import { ProjetFormValues, ProjetSchema } from "@/lib/schemas/projet.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";

// Types pour les données des Selects
type SelectOption = { value: string; label: string };

export default function ProjetsPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [projets, setProjets] = useState<ProjetColumn[]>([]);
  const [secteursActivite, setSecteursActivite] = useState<SelectOption[]>([]);
  const [clientsBeneficiaires, setClientsBeneficiaires] = useState<SelectOption[]>([]); // Placeholder

  const [isLoading, setIsLoading] = useState(true);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProjet, setEditingProjet] = useState<ProjetColumn | null>(null);
  const [projetToDelete, setProjetToDelete] = useState<ProjetColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const form = useForm<ProjetFormValues>({
    resolver: zodResolver(ProjetSchema),
    defaultValues: {
      nom: "", description: "", secteurActiviteId: "", clientBeneficiaireId: "",
      localisationRegion: "", localisationDepartement: "", localisationCommune: "", localisationAdressePrecise: "",
      coutTotalProjetStr: "",
      // nouveaux champs pour autres informations
      dateDebut: "",
      responsableProjet: "",
      emailContact: "",
      autresInformationsStr: "", // gardé pour compatibilité backend
    },
  });

  const fetchInitialFormData = useCallback(async () => {
    try {
      // Charger les secteurs d'activité
      const secteursRes = await fetch("/api/configuration/secteurs-activite");
      if (!secteursRes.ok) throw new Error("Échec chargement secteurs");
      const secteursData: SecteurActivite[] = await secteursRes.json();
      setSecteursActivite(secteursData.map(s => ({ value: s.id.toString(), label: s.nom })));

      // Charger les clients bénéficiaires
      const clientsRes = await fetch("/api/configuration/clients-beneficiaires");
      if (!clientsRes.ok) throw new Error("Échec chargement clients");
      const clientsData: ClientBeneficiaire[] = await clientsRes.json();
      setClientsBeneficiaires(clientsData.map(c => ({ value: c.id.toString(), label: c.nomOuRaisonSociale })));

    } catch (error: any) {
      toast({ title: "Erreur chargement données de formulaire", description: error.message, variant: "destructive" });
    }
  }, [toast]);

  const fetchProjetsData = useCallback(async (showLoading = true) => { /* ... (similaire aux autres pages) ... */
    if (showLoading) setIsLoading(true);
    try {
      const response = await fetch("/api/configuration/projets");
      if (!response.ok) throw new Error("Échec de la récupération des projets");
      const data = await response.json();
      setProjets(data);
      if (showLoading) setInitialDataLoaded(true);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      if (showLoading) setInitialDataLoaded(true);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    if (sessionStatus === "loading") { setIsLoading(true); return; }
    if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/"); setIsLoading(false); return;
    }
    if (session && !initialDataLoaded) {
        fetchInitialFormData(); // Charger données pour les Selects
        fetchProjetsData();
    } else if (session && initialDataLoaded) { setIsLoading(false); }
  }, [session, sessionStatus, initialDataLoaded, router, toast, fetchProjetsData, fetchInitialFormData]);

  const refreshData = useCallback(async () => { await fetchProjetsData(false); }, [fetchProjetsData]);

  const handleEdit = useCallback((projet: ProjetColumn) => {
    setEditingProjet(projet);
    const loc = projet.localisation as { region?: string, departement?: string, commune?: string, adressePrecise?: string } | null;
    type AutresInformations = {
      dateDebut?: string;
      responsableProjet?: string;
      emailContact?: string;
      [key: string]: any;
    };
    let autres: AutresInformations = {};
    try { autres = (projet.autresInformations as AutresInformations) || {}; } catch {}
    form.reset({
      nom: projet.nom,
      description: projet.description,
      secteurActiviteId: projet.secteurActiviteId.toString(),
      clientBeneficiaireId: projet.clientBeneficiaireId.toString(),
      localisationRegion: loc?.region || "",
      localisationDepartement: loc?.departement || "",
      localisationCommune: loc?.commune || "",
      localisationAdressePrecise: loc?.adressePrecise || "",
      coutTotalProjetStr: projet.coutTotalProjet?.toString().replace('.', ',') || "",
      dateDebut: autres.dateDebut || "",
      responsableProjet: autres.responsableProjet || "",
      emailContact: autres.emailContact || "",
      autresInformationsStr: projet.autresInformations ? JSON.stringify(projet.autresInformations, null, 2) : "",
    });
    setIsFormOpen(true);
  }, [form]);

  const handleDeleteConfirm = useCallback((projet: ProjetColumn) => { /* ... */ setProjetToDelete(projet); setIsDeleteDialogOpen(true); }, []);
  const handleDelete = useCallback(async () => { /* ... (similaire, utilise refreshData) ... */ }, [projetToDelete, toast, refreshData]);
  const onSubmit = useCallback(async (values: ProjetFormValues) => {
    // Compose l'objet autresInformations uniquement avec les champs non vides
    const autresInformations: Record<string, string> = {};
    if (values.dateDebut) autresInformations.dateDebut = values.dateDebut;
    if (values.responsableProjet) autresInformations.responsableProjet = values.responsableProjet;
    if (values.emailContact) autresInformations.emailContact = values.emailContact;

    const payload = {
      nom: values.nom,
      description: values.description,
      secteurActiviteId: values.secteurActiviteId,
      clientBeneficiaireId: values.clientBeneficiaireId,
      localisationRegion: values.localisationRegion,
      localisationDepartement: values.localisationDepartement,
      localisationCommune: values.localisationCommune,
      localisationAdressePrecise: values.localisationAdressePrecise,
      coutTotalProjetStr: values.coutTotalProjetStr,
      dateDebut: values.dateDebut,
      responsableProjet: values.responsableProjet,
      emailContact: values.emailContact,
      autresInformationsStr: Object.keys(autresInformations).length > 0 ? JSON.stringify(autresInformations) : '',
    };
    const isEdit = !!editingProjet;
    const url = isEdit ? `/api/configuration/projets/${editingProjet?.id}` : "/api/configuration/projets";
    const method = isEdit ? "PUT" : "POST";

    try {
      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const result = await response.json();
      if (!response.ok) {
        if (result.errors) {
          Object.keys(result.errors).forEach((key) => {
            form.setError(key as keyof ProjetFormValues, { type: "server", message: result.errors[key].join(", ") });
          });
        }
        throw new Error(result.message || (isEdit ? "Échec de la mise à jour" : "Échec de la création"));
      }
      toast({ title: isEdit ? "Projet mis à jour" : "Projet créé" });
      setIsFormOpen(false);
      setEditingProjet(null);
      form.reset();
      await refreshData();
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [editingProjet, form, refreshData, toast]);

  const columns = useMemo(() => getProjetColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }), [handleEdit, handleDeleteConfirm]);

  if (isLoading) return <div className="p-6 text-center">Chargement des projets...</div>;
  // ... (vérification de session pour accès) ...

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des Projets</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => { if (!open) { setEditingProjet(null); form.reset(); } setIsFormOpen(open); }}>
          <DialogTrigger asChild><Button onClick={() => { setEditingProjet(null); form.reset(); setIsFormOpen(true); }}><PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Projet</Button></DialogTrigger>
          <DialogContent className="sm:max-w-2xl"> {/* Plus large pour le formulaire */}
            <DialogHeader><DialogTitle>{editingProjet ? "Modifier le Projet" : "Ajouter un Projet"}</DialogTitle></DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4 max-h-[80vh] overflow-y-auto pr-3">
                <FormField control={form.control} name="nom" render={({ field }) => (
                  <FormItem><FormLabel>Nom du Projet <span className="text-red-500">*</span></FormLabel><FormControl><Input placeholder="Ex: Construction d'une école" {...field} /></FormControl><FormMessage /></FormItem>
                )}/>
                <FormField control={form.control} name="description" render={({ field }) => (
                  <FormItem><FormLabel>Description <span className="text-red-500">*</span></FormLabel><FormControl><Textarea rows={4} placeholder="Décris brièvement le projet..." {...field} /></FormControl><FormMessage /></FormItem>
                )}/>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="secteurActiviteId" render={({ field }) => (
                        <FormItem> <FormLabel>Secteur d'Activité <span className="text-red-500">*</span></FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir un secteur..." /></SelectTrigger></FormControl>
                                <SelectContent>
                                    {secteursActivite.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                                </SelectContent>
                            </Select> <FormMessage />
                        </FormItem>
                    )} />
                    <FormField control={form.control} name="clientBeneficiaireId" render={({ field }) => (
                        <FormItem> <FormLabel>Client Bénéficiaire <span className="text-red-500">*</span></FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir un client..." /></SelectTrigger></FormControl>
                                <SelectContent>
                                    {clientsBeneficiaires.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                                    {clientsBeneficiaires.length === 0 && <SelectItem value="no-client" disabled>Aucun client disponible</SelectItem>}
                                </SelectContent>
                            </Select> <FormMessage />
                        </FormItem>
                    )} />
                </div>
                <FormField control={form.control} name="coutTotalProjetStr" render={({ field }) => (
                    <FormItem><FormLabel>Coût Total du Projet (XOF)</FormLabel><FormControl><Input type="text" placeholder="Ex: 15000000" {...field} /></FormControl><FormMessage /></FormItem>
                )} />

                <h3 className="text-lg font-medium border-b pb-2 pt-3">Localisation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="localisationRegion" render={({ field }) => ( <FormItem><FormLabel>Région</FormLabel><FormControl><Input placeholder="Ex: Dakar" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="localisationDepartement" render={({ field }) => ( <FormItem><FormLabel>Département</FormLabel><FormControl><Input placeholder="Ex: Pikine" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="localisationCommune" render={({ field }) => ( <FormItem><FormLabel>Commune</FormLabel><FormControl><Input placeholder="Ex: Dalifort" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="localisationAdressePrecise" render={({ field }) => ( <FormItem><FormLabel>Adresse Précise</FormLabel><FormControl><Textarea rows={2} placeholder="Ex: Rue 123, Zone Industrielle" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                </div>

                {/* Champs Autres Informations */}
                <h3 className="text-lg font-medium border-b pb-2 pt-3">Autres Informations</h3>
                {/*
                  On n'affiche plus de textarea JSON pour autresInformationsStr.
                  Les champs ci-dessous sont utilisés pour construire le JSON côté JS à la soumission.
                */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField control={form.control} name="dateDebut" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date de début</FormLabel>
                      <FormControl>
                        <Input type="date" placeholder="Date de début" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}/>
                  <FormField control={form.control} name="responsableProjet" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Responsable projet</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Jean Dupont" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}/>
                  <FormField control={form.control} name="emailContact" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email de contact</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}/>
                </div>
                <DialogFooter className="pt-4"><DialogClose asChild><Button variant="outline">Annuler</Button></DialogClose><Button type="submit" disabled={form.formState.isSubmitting}>Sauvegarder</Button></DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      <DataTable columns={columns} data={projets} />
      {projetToDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
              <AlertDialogDescription>
                Êtes-vous sûr de vouloir supprimer le projet <b>{projetToDelete.nom}</b> ?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>
                Annuler
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={async () => {
                  await handleDelete();
                  setIsDeleteDialogOpen(false);
                }}
                className="bg-red-600 hover:bg-red-700"
              >
                Supprimer
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}