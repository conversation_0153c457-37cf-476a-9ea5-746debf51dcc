// src/lib/schemas/ligne-garantie.schema.ts
import { z } from "zod";
import { StatutLigneGarantie } from "@/types/statut-ligne-garantie";

const statutLigneGarantieValues = Object.values(StatutLigneGarantie) as [string, ...string[]];

export const LigneGarantieSchema = z.object({
  nom: z.string().min(3, "Le nom de la ligne est requis (min 3 caractères).").max(150),
  bailleurId: z.string().min(1, "Veuillez sélectionner un bailleur de fonds.")
    .refine(val => !isNaN(parseInt(val)), { message: "ID de bailleur invalide." }),
  referenceConvention: z.string().max(100).optional().or(z.literal('')),
  description: z.string().optional().or(z.literal('')),
  montantInitialStr: z.string().min(1, "Le montant initial est requis.")
    .refine(val => !isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) > 0, {
        message: "Le montant initial doit être un nombre positif valide.",
    }),
  dateOuverture: z.date({
    required_error: "La date d'ouverture est requise.",
    invalid_type_error: "Format de date d'ouverture invalide.",
  }),
  dateExpiration: z.date({
    required_error: "La date d'expiration est requise.",
    invalid_type_error: "Format de date d'expiration invalide.",
  }),
  devise: z.string().min(3, "La devise est requise (ex: XOF).").max(5).default("XOF"),
  statut: z.enum(statutLigneGarantieValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un statut valide." }),
  }),
  // autresInformationsStr: z.string().optional().or(z.literal(''))
  //   .refine((val) => {
  //       if (!val || val.trim() === "") return true;
  //       try { JSON.parse(val); return true; } catch (e) { return false; }
  //   }, { message: "Le champ 'Autres Informations' doit être un JSON valide s'il est renseigné." }),
  // NOUVEAUX Champs pour Autres Informations (structurés)
  autreInfoObjectifLigne: z.string().optional().or(z.literal('')),
  autreInfoConditionsSpecifiques: z.string().optional().or(z.literal('')),
  autreInfoIndicateurPerf: z.string().optional().or(z.literal('')),
})
.refine(data => data.dateExpiration > data.dateOuverture, {
    message: "La date d'expiration doit être postérieure à la date d'ouverture.",
    path: ["dateExpiration"], // Appliquer l'erreur au champ dateExpiration
});

export type LigneGarantieFormValues = z.infer<typeof LigneGarantieSchema>;