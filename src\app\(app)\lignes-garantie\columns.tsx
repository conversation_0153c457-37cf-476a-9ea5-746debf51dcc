// src/app/(app)/lignes-garantie/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { StatutLigneGarantie } from "@/types/statut-ligne-garantie";
import { ArrowUpDown, Edit, Trash2, Eye } from "lucide-react"; // Eye pour voir détails/avenants
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link"; // Pour le bouton "Voir"

export type LigneGarantieColumn = {
  id: number;
  nom: string;
  referenceConvention?: string;
  description?: string;
  montantInitial: number;
  montantDisponible: number;
  dateOuverture: string | Date;
  dateExpiration: string | Date;
  devise: string;
  statut: StatutLigneGarantie;
  autresInformations?: any;
  bailleur: { id: number; nom: string };
  utilisateurCreation?: { nomUtilisateur: string } | null;
};

interface LigneGarantieColumnsProps {
    onEdit: (ligne: LigneGarantieColumn) => void;
    onDelete: (ligne: LigneGarantieColumn) => void;
    onView?: (ligne: LigneGarantieColumn) => void;
}

const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined) return "-";
    const num = typeof amount === 'string' ? parseFloat(amount.replace(',', '.')) : amount;
    if (isNaN(num)) return "-";
    return new Intl.NumberFormat("fr-FR", { style: "currency", currency: "XOF", minimumFractionDigits: 0 }).format(num);
};

const getStatutBadgeVariant = (statut: StatutLigneGarantie) => {
    switch (statut) {
        case StatutLigneGarantie.Active: return "bg-green-100 text-green-700 border-green-300";
        case StatutLigneGarantie.EnAttenteValidation: return "bg-yellow-100 text-yellow-700 border-yellow-300";
        case StatutLigneGarantie.Epuisee: return "bg-orange-100 text-orange-700 border-orange-300";
        case StatutLigneGarantie.Expiree: case StatutLigneGarantie.Cloturee: return "bg-red-100 text-red-700 border-red-300";
        case StatutLigneGarantie.Suspendue: return "bg-gray-100 text-gray-700 border-gray-300";
        default: return "bg-slate-100 text-slate-700 border-slate-300";
    }
};

// Utilitaire pour extraire les champs structurés
function getAutresInfos(autresInformations: any): Record<string, any> {
  if (!autresInformations) return {};
  if (typeof autresInformations === 'string') {
    try { return JSON.parse(autresInformations); } catch { return {}; }
  }
  return autresInformations;
}

export const getLigneGarantieColumns = ({ onEdit, onDelete, onView }: LigneGarantieColumnsProps): ColumnDef<LigneGarantieColumn>[] => [
  { accessorKey: "nom", header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Nom Ligne <ArrowUpDown /> </Button> ) },
  { accessorKey: "bailleur.nom", header: "Bailleur", cell: ({row}) => row.original.bailleur.nom },
  { accessorKey: "montantInitial", header: "Montant Initial", cell: ({ row }) => formatCurrency(row.getValue("montantInitial")) },
  { accessorKey: "montantDisponible", header: "Montant Disponible", cell: ({ row }) => formatCurrency(row.getValue("montantDisponible")) },
  { accessorKey: "dateOuverture", header: "Ouverture", cell: ({ row }) => new Date(row.getValue("dateOuverture")).toLocaleDateString("fr-FR") },
  { accessorKey: "dateExpiration", header: "Expiration", cell: ({ row }) => new Date(row.getValue("dateExpiration")).toLocaleDateString("fr-FR") },
  { accessorKey: "statut", header: "Statut", cell: ({ row }) => <Badge className={getStatutBadgeVariant(row.getValue("statut"))}>{row.getValue("statut")}</Badge> },
  { accessorKey: "autresInformations.objectif_ligne", header: "Objectif", cell: ({ row }) => getAutresInfos(row.original.autresInformations).objectif_ligne || "-" },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const ligne = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          {onView && (
            <Button variant="ghost" size="icon" title="Voir Détails & Avenants" onClick={() => onView(ligne)}><Eye className="h-4 w-4" /></Button>
          )}
          <Button variant="ghost" size="icon" onClick={() => onEdit(ligne)} title="Modifier"><Edit className="h-4 w-4" /></Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(ligne)} className="text-red-600 hover:text-red-700" title="Supprimer"><Trash2 className="h-4 w-4" /></Button>
        </div>
      );
    },
  },
];