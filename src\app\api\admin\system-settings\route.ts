// src/app/api/admin/system-settings/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeParametre } from "@prisma/client";
import { SystemSettingSchema, validateSettingValue } from "@/lib/schemas/system-setting.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET: Lister tous les paramètres système
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const adminId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: adminId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const settings = await prisma.parametreSysteme.findMany({
        orderBy: { cle: "asc" },
        include: { // Si vous avez ajouté les champs de tracking utilisateur
          utilisateurCreation: { select: { nomUtilisateur: true }},
          utilisateurModification: { select: { nomUtilisateur: true }},
        }
      });
      return NextResponse.json(settings);
    } catch (error) {
      console.error("Erreur GET /api/admin/system-settings:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// POST: Créer un nouveau paramètre système
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const adminId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: adminId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = SystemSettingSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { cle, valeur, description, typeValeur, estModifiable } = validation.data;

      // Validation supplémentaire de la valeur en fonction du type
      const valueValidationResult = validateSettingValue(valeur, typeValeur as TypeParametre);
      if (typeof valueValidationResult === 'string') {
          return NextResponse.json({ message: "Données invalides", errors: { valeur: [valueValidationResult] } }, { status: 400 });
      }

      const existingSetting = await prisma.parametreSysteme.findUnique({ where: { cle } });
      if (existingSetting) {
        return NextResponse.json({ message: "Un paramètre avec cette clé existe déjà." }, { status: 409 });
      }

      const newSetting = await prisma.parametreSysteme.create({
        data: {
          cle,
          valeur,
          description,
          typeValeur: typeValeur as TypeParametre, // Zod enum est string, Prisma enum est TypeParametre
          estModifiable,
          utilisateurCreationId: adminId, // Si vous tracez
        },
      });
      return NextResponse.json(newSetting, { status: 201 });
    } catch (error) {
      console.error("Erreur POST /api/admin/system-settings:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}