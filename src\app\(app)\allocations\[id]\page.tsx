// src/app/(app)/allocations/[id]/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useParams, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { AllocationLignePartenaire, LigneGarantie, Partenaire, AvenantAllocation as AvenantAllocationPrisma, TypeAvenant, StatutAllocation, RoleUtilisateur } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, <PERSON>ert<PERSON>ialog<PERSON>ooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { AvenantAllocationFormValues, AvenantAllocationSchema } from "@/lib/schemas/avenant-allocation.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { PlusCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { DataTable } from "@/components/shared/data-table";
import { getAvenantAllocationColumns, AvenantAllocationColumn } from "./components/avenant-allocation-columns";
import { Badge } from "@/components/ui/badge";

// Type pour les données de l'allocation enrichies
type AllocationDetails = AllocationLignePartenaire & {
  ligneGarantie: Pick<LigneGarantie, "id" | "nom" | "devise" | "montantInitial" | "dateExpiration">;
  partenaire: Pick<Partenaire, "id" | "nom" | "typePartenaire">;
  avenants: AvenantAllocationColumn[];
  utilisateurCreation?: { nomUtilisateur: string; nom: string; prenom: string } | null;
  utilisateurModification?: { nomUtilisateur: string; nom: string; prenom: string } | null;
};

const typeAvenantOptions = Object.values(TypeAvenant).map(val => ({ 
  value: val, 
  label: val.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) 
}));

const formatCurrency = (amount: any, currency: string = "XOF") => {
  if (!amount) return "-";
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : Number(amount);
  return `${numAmount.toLocaleString('fr-FR')} ${currency}`;
};

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

const getStatutAllocationBadgeVariant = (statut: StatutAllocation | undefined) => {
  switch (statut) {
    case StatutAllocation.Active: return "default";
    case StatutAllocation.Suspendue: return "secondary";
    case StatutAllocation.Expiree: return "destructive";
    case StatutAllocation.Epuisee: return "outline";
    default: return "outline";
  }
};

export default function AllocationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { data: session, status: sessionStatus } = useSession();
  const allocationId = params.id as string;

  const [allocationDetails, setAllocationDetails] = useState<AllocationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAvenantFormOpen, setIsAvenantFormOpen] = useState(false);
  const [editingAvenant, setEditingAvenant] = useState<AvenantAllocationColumn | null>(null);
  const [avenantToDelete, setAvenantToDelete] = useState<AvenantAllocationColumn | null>(null);
  const [isDeleteAvenantDialogOpen, setIsDeleteAvenantDialogOpen] = useState(false);

  const avenantForm = useForm<AvenantAllocationFormValues>({
    resolver: zodResolver(AvenantAllocationSchema),
    defaultValues: {
        typeAvenant: undefined, 
        montantModificationStr: "", 
        nouvelleDateExpiration: undefined,
        dateAvenant: new Date(), 
        raison: "", 
        referenceDocument: ""
    },
  });
  
  const typeAvenantSelected = avenantForm.watch("typeAvenant");

  const fetchAllocationDetails = useCallback(async () => {
    if (!allocationId) return;
    setIsLoading(true);
    try {
      const response = await fetch(`/api/allocations/${allocationId}`);
      if (!response.ok) throw new Error("Allocation non trouvée ou erreur serveur.");
      const data = await response.json();
      setAllocationDetails(data);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      router.push("/allocations");
    } finally {
      setIsLoading(false);
    }
  }, [allocationId, toast, router]);

  useEffect(() => {
    let isMounted = true;
    
    const loadData = async () => {
      if (sessionStatus === "loading") return;
      if (!session || !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"].includes(session.user?.role as string)) {
        router.replace("/");
        return;
      }
      
      if (!allocationId || !isMounted) return;
      setIsLoading(true);
      
      try {
        const response = await fetch(`/api/allocations/${allocationId}`);
        if (!response.ok) throw new Error("Allocation non trouvée ou erreur serveur.");
        const data = await response.json();
        if (isMounted) {
          setAllocationDetails(data);
        }
      } catch (error: any) {
        if (isMounted) {
          toast({ title: "Erreur", description: error.message, variant: "destructive" });
          router.push("/allocations");
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    loadData();
    
    return () => {
      isMounted = false;
    };
  }, [session, sessionStatus, allocationId]); // Removed toast and router from dependencies

  const handleOpenCreateAvenantForm = () => {
    setEditingAvenant(null);
    avenantForm.reset({
      typeAvenant: undefined, 
      montantModificationStr: "", 
      nouvelleDateExpiration: undefined,
      dateAvenant: new Date(), 
      raison: "", 
      referenceDocument: ""
    });
    setIsAvenantFormOpen(true);
  };

  const handleOpenEditAvenantForm = useCallback((avenant: AvenantAllocationColumn) => {
    setEditingAvenant(avenant);
    avenantForm.reset({
      typeAvenant: avenant.typeAvenant,
      montantModificationStr: avenant.montantModification?.toString().replace('.',',') || "",
      nouvelleDateExpiration: avenant.nouvelleDateExpiration ? new Date(avenant.nouvelleDateExpiration) : undefined,
      dateAvenant: new Date(avenant.dateAvenant),
      raison: avenant.raison,
      referenceDocument: avenant.referenceDocument || "",
    });
    setIsAvenantFormOpen(true);
  }, [avenantForm]);

  const onAvenantSubmit = async (values: AvenantAllocationFormValues) => {
    if (!allocationDetails) return;
    const url = editingAvenant
      ? `/api/allocations/${allocationDetails.id}/avenants/${editingAvenant.id}`
      : `/api/allocations/${allocationDetails.id}/avenants`;
    const method = editingAvenant ? "PUT" : "POST";

    try {
      const response = await fetch(url, { method, headers: { "Content-Type": "application/json" }, body: JSON.stringify(values) });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || "Échec opération sur avenant");
      toast({ title: editingAvenant ? "Avenant Modifié" : "Avenant Créé" });
      setIsAvenantFormOpen(false);
      setEditingAvenant(null);
      fetchAllocationDetails(); // Recharger
    } catch (error: any) {
      toast({ title: "Erreur Avenant", description: error.message, variant: "destructive" });
    }
  };

  const handleDeleteAvenantConfirm = useCallback((avenant: AvenantAllocationColumn) => {
    setAvenantToDelete(avenant);
    setIsDeleteAvenantDialogOpen(true);
  }, []);

  const handleDeleteAvenant = async () => {
    if (!avenantToDelete || !allocationDetails) return;
    try {
      const response = await fetch(`/api/allocations/${allocationDetails.id}/avenants/${avenantToDelete.id}`, { method: "DELETE" });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec suppression avenant");
      }
      toast({ title: "Avenant Supprimé" });
      fetchAllocationDetails(); // Recharger
    } catch (error: any) {
      toast({ title: "Erreur Suppression", description: error.message, variant: "destructive" });
    }
    finally {
      setIsDeleteAvenantDialogOpen(false);
      setAvenantToDelete(null);
    }
  };

  const avenantColumns = useMemo(() => getAvenantAllocationColumns({
    onEdit: handleOpenEditAvenantForm,
    onDelete: handleDeleteAvenantConfirm,
  }), [handleOpenEditAvenantForm, handleDeleteAvenantConfirm]);

  if (isLoading || sessionStatus === 'loading') {
    return <div className="p-6 text-center">Chargement des détails de l'allocation...</div>;
  }
  
  if (!allocationDetails) {
    return <div className="p-6 text-center">Allocation non trouvée.</div>;
  }

  const canManageAvenants = session?.user?.role === "Administrateur" || session?.user?.role === "GestionnaireGesGar";

  return (
    <div className="container mx-auto py-8 px-4">
      <Button variant="outline" asChild className="mb-6">
        <Link href="/allocations">
          <ArrowLeft className="mr-2 h-4 w-4" /> Retour à la liste des allocations
        </Link>
      </Button>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-2xl">Détails de l'Allocation #{allocationDetails.id}</CardTitle>
          <CardDescription>
            Ligne: {allocationDetails.ligneGarantie.nom} <br/>
            Partenaire: {allocationDetails.partenaire.nom} - Statut: <Badge variant={getStatutAllocationBadgeVariant(allocationDetails.statut)}>{allocationDetails.statut}</Badge>
          </CardDescription>
        </CardHeader>
        <CardContent className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
          <div><strong>Réf. Convention:</strong> {allocationDetails.referenceConvention || "-"}</div>
          <div><strong>Montant Alloué:</strong> {formatCurrency(allocationDetails.montantAlloue, allocationDetails.ligneGarantie.devise)}</div>
          <div><strong>Montant Disponible:</strong> {formatCurrency(allocationDetails.montantDisponible, allocationDetails.ligneGarantie.devise)}</div>
          <div><strong>Date Allocation:</strong> {formatDate(allocationDetails.dateAllocation)}</div>
          <div><strong>Date Expiration:</strong> {formatDate(allocationDetails.dateExpiration)}</div>
          <div><strong>Taux Couv. Max:</strong> {allocationDetails.tauxCouvertureMax?.toString() || "-"}%</div>
          <div><strong>Taux Intérêt:</strong> {allocationDetails.tauxInteret?.toString() || "-"}%</div>
          <div><strong>Taux Commission:</strong> {allocationDetails.tauxCommission?.toString() || "-"}%</div>
          <div><strong>Périodicité Intérêts:</strong> {allocationDetails.periodicitePaiementInteret || "-"}</div>
          <div><strong>Périodicité Commissions:</strong> {allocationDetails.periodicitePaiementCommission || "-"}</div>
          {allocationDetails.commentaires && (
            <div className="md:col-span-full">
              <strong>Commentaires:</strong> 
              <p className="whitespace-pre-wrap">{allocationDetails.commentaires}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Section des Avenants sur Allocation */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Avenants sur l'Allocation</h2>
            {canManageAvenants && (
                <Dialog open={isAvenantFormOpen} onOpenChange={(open) => {
                    if (!open) setEditingAvenant(null);
                    setIsAvenantFormOpen(open);
                }}>
                    <DialogTrigger asChild>
                        <Button onClick={handleOpenCreateAvenantForm}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Avenant d'Allocation
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-lg">
                        <DialogHeader>
                          <DialogTitle>{editingAvenant ? "Modifier" : "Ajouter"} Avenant d'Allocation</DialogTitle>
                        </DialogHeader>
                        <Form {...avenantForm}>
                            <form onSubmit={avenantForm.handleSubmit(onAvenantSubmit)} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
                                <FormField 
                                  control={avenantForm.control} 
                                  name="typeAvenant" 
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Type d'Avenant</FormLabel>
                                      <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Sélectionner le type d'avenant" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {typeAvenantOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )} 
                                />
                                
                                {(typeAvenantSelected === TypeAvenant.AUGMENTATION_MONTANT || typeAvenantSelected === TypeAvenant.REDUCTION_MONTANT) && (
                                    <FormField 
                                      control={avenantForm.control} 
                                      name="montantModificationStr" 
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Montant de Modification</FormLabel>
                                          <FormControl>
                                            <Input {...field} placeholder="Montant (ex: 1000000)" />
                                          </FormControl>
                                          <FormDescription>
                                            Montant en {allocationDetails.ligneGarantie.devise}
                                          </FormDescription>
                                          <FormMessage />
                                        </FormItem>
                                      )} 
                                    />
                                )}
                                
                                {typeAvenantSelected === TypeAvenant.PROLONGATION_DUREE && (
                                    <FormField 
                                      control={avenantForm.control} 
                                      name="nouvelleDateExpiration" 
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Nouvelle Date d'Expiration</FormLabel>
                                          <FormControl>
                                            <DatePicker 
                                              date={field.value} 
                                              onDateChange={field.onChange}
                                              placeholder="Sélectionner la nouvelle date"
                                            />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )} 
                                    />
                                )}
                                
                                <FormField 
                                  control={avenantForm.control} 
                                  name="dateAvenant" 
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Date de l'Avenant</FormLabel>
                                      <FormControl>
                                        <DatePicker 
                                          date={field.value} 
                                          onDateChange={field.onChange}
                                          placeholder="Sélectionner la date"
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )} 
                                />
                                
                                <FormField 
                                  control={avenantForm.control} 
                                  name="raison" 
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Raison de l'Avenant</FormLabel>
                                      <FormControl>
                                        <Textarea {...field} placeholder="Expliquer la raison de cet avenant..." />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )} 
                                />
                                
                                <FormField 
                                  control={avenantForm.control} 
                                  name="referenceDocument" 
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Référence Document (optionnel)</FormLabel>
                                      <FormControl>
                                        <Input {...field} placeholder="Référence du document justificatif" />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )} 
                                />

                                <DialogFooter className="pt-4">
                                    <DialogClose asChild>
                                      <Button type="button" variant="outline">Annuler</Button>
                                    </DialogClose>
                                    <Button type="submit" disabled={avenantForm.formState.isSubmitting}>
                                        {avenantForm.formState.isSubmitting ? "Sauvegarde..." : (editingAvenant ? "Mettre à Jour" : "Ajouter")}
                                    </Button>
                                </DialogFooter>
                            </form>
                        </Form>
                    </DialogContent>
                </Dialog>
            )}
        </div>
        {allocationDetails.avenants && allocationDetails.avenants.length > 0 ? (
            <DataTable columns={avenantColumns} data={allocationDetails.avenants} />
        ) : (
            <p className="text-muted-foreground text-center py-4">Aucun avenant enregistré pour cette allocation.</p>
        )}
      </div>

      {/* AlertDialog pour la suppression d'avenant d'allocation */}
      {avenantToDelete && (
        <AlertDialog open={isDeleteAvenantDialogOpen} onOpenChange={setIsDeleteAvenantDialogOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Supprimer l'Avenant d'Allocation ?</AlertDialogTitle>
                    <AlertDialogDescription>
                        Êtes-vous sûr de vouloir supprimer cet avenant (Type: {avenantToDelete.typeAvenant}, Date: {formatDate(avenantToDelete.dateAvenant)}) ?
                        Cette action impactera les montants de l'allocation et de la ligne de garantie parente.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setAvenantToDelete(null)}>Annuler</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteAvenant} className="bg-red-600 hover:bg-red-700">Oui, Supprimer</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
      )}

      {/* TODO: Section pour les Paiements d'Intérêts et Commissions */}
      {/* TODO: Section pour les Garanties liées à cette allocation */}
    </div>
  );
}