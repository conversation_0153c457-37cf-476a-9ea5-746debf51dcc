// prisma/seed.ts
import { PrismaClient, RoleUtilisateur } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log(`Début du seeding ...`);

  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'Admin123!'; // CHANGEZ CECI EN PRODUCTION !

  // Vérifier si l'admin existe déjà
  const existingAdmin = await prisma.utilisateur.findUnique({
    where: { email: adminEmail },
  });

  if (existingAdmin) {
    console.log(`L'utilisateur administrateur ${adminEmail} existe déjà.`);
  } else {
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    const adminUser = await prisma.utilisateur.create({
      data: {
        nomUtilisateur: 'admin_gesgar',
        email: adminEmail,
        motDePasse: hashedPassword,
        nom: 'Admin',
        prenom: 'GesGar',
        role: RoleUtilisateur.Administrateur,
        estActif: true,
        // utilisateurCreationId: null, // Pas d'utilisateur créateur pour le seed initial
      },
    });
    console.log(`Utilisateur administrateur ${adminUser.email} créé avec le mot de passe: ${adminPassword} (NOTE: Ceci est pour le dev uniquement, ne pas afficher en prod)`);
  }

  // Vous pouvez ajouter d'autres données de seed ici (Bailleurs par défaut, etc.)

  console.log(`Seeding terminé.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });