// src/lib/schemas/avenant-ligne-garantie.schema.ts
import { z } from "zod";
export enum TypeAvenant {
  AUGMENTATION_MONTANT = "AUGMENTATION_MONTANT",
  REDUCTION_MONTANT = "REDUCTION_MONTANT",
  PROLONGATION_DUREE = "PROLONGATION_DUREE",
  MODIFICATION_CONDITIONS = "MODIFICATION_CONDITIONS",
  AUTRE = "AUTRE",
}

const typeAvenantValues = Object.values(TypeAvenant) as [string, ...string[]];

// --- BASE SCHEMA (no .refine/.superRefine) ---
export const AvenantLigneGarantieBaseSchema = z.object({
  ligneGarantieId: z.number().int().positive("ID de la ligne de garantie invalide."),
  typeAvenant: z.enum(typeAvenantValues, {
    required_error: "Le type d'avenant est requis.",
    invalid_type_error: "Type d'avenant invalide.",
  }),
  montantModificationStr: z.preprocess(
    (v) => (typeof v === 'string' && v.trim() === '' ? undefined : v),
    z.string()
      .regex(/^\d+(\.\d+)?$/, {
        message: "Le montant doit être au format numérique ISO (ex: 1234.56, sans virgule ni séparateur de milliers)",
      })
      .optional()
  ),
  nouvelleDateExpiration: z.date().optional(),
  dateAvenant: z.date({
    required_error: "La date de l'avenant est requise.",
    invalid_type_error: "Format de date d'avenant invalide.",
  }),
  raison: z.string().min(5, "La raison doit contenir au moins 5 caractères.").max(1000),
  referenceDocument: z.string().max(100).optional().or(z.literal('')),
});

// --- FULL SCHEMA (with .refine/.superRefine) ---
export const AvenantLigneGarantieSchema = AvenantLigneGarantieBaseSchema
  .refine(data => {
    if (data.typeAvenant === TypeAvenant.AUGMENTATION_MONTANT || data.typeAvenant === TypeAvenant.REDUCTION_MONTANT) {
      if (!data.montantModificationStr || data.montantModificationStr.trim() === "") {
        return false;
      }
      return /^\d+(\.\d+)?$/.test(data.montantModificationStr.trim());
    }
    return true;
  }, {
    message: "Le montant de modification est requis et doit être un nombre au format ISO (ex: 1234.56)",
    path: ["montantModificationStr"],
  })
  .refine(data => {
    if (data.typeAvenant === TypeAvenant.PROLONGATION_DUREE) {
      return data.nouvelleDateExpiration !== undefined && data.nouvelleDateExpiration !== null;
    }
    return true;
  }, {
    message: "La nouvelle date d'expiration est requise pour une prolongation de durée.",
    path: ["nouvelleDateExpiration"],
  })
  .superRefine((data, ctx) => {
    if (
      data.typeAvenant === TypeAvenant.PROLONGATION_DUREE &&
      data.nouvelleDateExpiration &&
      data.dateAvenant
    ) {
      if (data.nouvelleDateExpiration <= data.dateAvenant) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "La nouvelle date d'expiration doit être postérieure à la date de l'avenant.",
          path: ["nouvelleDateExpiration"],
        });
      }
    }
  });

// --- UPDATE SCHEMA ---
export const AvenantUpdateSchema = AvenantLigneGarantieBaseSchema.pick({
  typeAvenant: true,
  montantModificationStr: true,
  nouvelleDateExpiration: true,
  dateAvenant: true,
  raison: true,
  referenceDocument: true,
}).transform((data) => ({
  typeAvenant: data.typeAvenant,
  montantModification: data.montantModificationStr ? parseFloat(data.montantModificationStr) : undefined, // 'montantModificationStr' must follow an ISO numeric format (e.g., 1234.56) as validated in the base schema
  nouvelleDateExpiration: data.nouvelleDateExpiration,
  dateAvenant: data.dateAvenant,
  raison: data.raison,
  referenceDocument: data.referenceDocument,
}));

export type AvenantLigneGarantieFormValues = Omit<z.infer<typeof AvenantLigneGarantieSchema>, "ligneGarantieId">;
// On omet ligneGarantieId du type de formulaire car il viendra du contexte de la page.

// Type DTO pour API/frontend : dates en string ISO
export type AvenantLigneGarantieDto = Omit<AvenantLigneGarantieFormValues, "dateAvenant" | "nouvelleDateExpiration"> & {
  dateAvenant: string;
  nouvelleDateExpiration?: string;
};

// Helper: transform validated form data to update DTO
export function toAvenantUpdateDto(data: z.infer<typeof AvenantLigneGarantieSchema>) {
  return {
    typeAvenant: data.typeAvenant,
    montantModification: data.montantModificationStr ? parseFloat(data.montantModificationStr) : undefined,
    nouvelleDateExpiration: data.nouvelleDateExpiration,
    dateAvenant: data.dateAvenant,
    raison: data.raison,
    referenceDocument: data.referenceDocument,
  };
}
