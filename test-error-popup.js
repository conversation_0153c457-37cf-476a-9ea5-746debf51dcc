// Test script pour vérifier le popup d'erreur 403
// Ce script simule l'erreur 403 qui se produit dans la console

console.log("=== Test du Popup d'Erreur 403 ===");

// Simulation de l'erreur 403 comme dans la console
const simulateApiError = () => {
  const error = {
    status: 403,
    message: "Non autorisé",
    response: '{"message":"Non autorisé"}'
  };
  
  console.error(`API Bailleurs Erreur: ${error.status} "${error.response}"`);
  
  // Simulation de l'erreur dans le contexte React
  const errorMessage = "Erreur 403: Non autorisé";
  console.log("Popup d'erreur qui devrait s'afficher:");
  console.log({
    title: "Erreur d'autorisation - API Bailleurs",
    description: `${errorMessage}\n\nVeuillez vérifier vos droits d'accès ou contacter l'administrateur système.`,
    type: "error"
  });
};

// Exécuter la simulation
simulateApiError();

console.log("\n=== Modifications apportées ===");
console.log("1. Ajout du composant ErrorDialog dans src/components/ui/error-dialog.tsx");
console.log("2. Modification de src/app/(app)/lignes-garantie/page.tsx pour:");
console.log("   - Importer ErrorDialog");
console.log("   - Ajouter l'état errorDialog");
console.log("   - Capturer les erreurs 403 et afficher le popup");
console.log("   - Afficher le composant ErrorDialog dans le JSX");

console.log("\n=== Fonctionnalités du popup ===");
console.log("- Affichage d'un popup modal avec icône d'erreur");
console.log("- Titre spécifique selon l'API (Bailleurs ou Lignes de Garantie)");
console.log("- Message d'erreur détaillé avec le code 403");
console.log("- Instructions pour l'utilisateur");
console.log("- Bouton de fermeture");

console.log("\n=== Test terminé ===");