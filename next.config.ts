import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    // Recommended: Restrict hostnames to known domains for security.
    // Using '**' is permissive. Replace with specific hostnames like:
    // { protocol: 'https', hostname: 'cdn.example.com' },
    // { protocol: 'https', hostname: 's3.amazonaws.com' },
    // Only HTTPS is allowed by default below. Add specific HTTP sources only if necessary and trusted.
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // TODO: Replace with specific, trusted hostnames.
      },
    ],
  },
  experimental: {
    typedRoutes: true,
  },
};

export default nextConfig;
