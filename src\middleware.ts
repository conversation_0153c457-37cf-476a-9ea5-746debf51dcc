import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { auditContext } from '@/lib/prisma-audit.middleware';
import { enhancedAuditContext, createAuditContext } from '@/lib/enhanced-audit.middleware';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  // Récupérer l'IP client
  // Note: For optimal accuracy, especially when behind multiple proxies,
  // configure the NEXT_PRIVATE_TRUSTED_PROXY_IPS environment variable.
  // This allows Next.js to correctly use request.ip.
  // See: https://nextjs.org/docs/app/api-reference/functions/next-request#nextrequestip
  let ipAddress: string = '';
  const xForwardedFor = request.headers.get('x-forwarded-for');

  if (xForwardedFor) {
    // x-forwarded-for can be a comma-separated list of IPs. The first one is usually the client.
    const ips = xForwardedFor.split(',').map(ipVal => ipVal.trim());
    ipAddress = ips[0] || ''; // Take the first IP, or empty if the list was just commas/empty strings.
  }

  if (!ipAddress && request.headers.get('x-real-ip')) {
    // Fallback to x-real-ip if x-forwarded-for is not present or yielded no IP.
    const realIp = request.headers.get('x-real-ip');
    if (realIp) {
        ipAddress = realIp.trim();
    }
  }

  if (!ipAddress && request.ip) { // request.ip is available in Next.js 15
    // Fallback to request.ip (available in Next.js 13.4+).
    // This is often the most reliable if NEXT_PRIVATE_TRUSTED_PROXY_IPS is set.
    ipAddress = request.ip;
  }
  // Ensure 'ip' is the variable used by subsequent code.
  const ip = ipAddress || '';

  // Récupérer le user-agent
  const userAgent = request.headers.get('user-agent') || '';

  // Récupérer le token de session pour avoir l'ID utilisateur
  const token = await getToken({ req: request });

  // Stocker le contexte d'audit
  const auditStore = {
    userId: token?.sub ? Number(token.sub) : undefined,
    ip,
    userAgent
  };

  // Exécuter les handlers suivants avec le contexte d'audit
  const response = NextResponse.next();

  // N'injecte le contexte d'audit que si userId existe
  if (auditStore.userId) {
    auditContext.run(auditStore, () => {});
  }

  return response;
}

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/admin/:path*",
    "/api/:path*",
  ],
};