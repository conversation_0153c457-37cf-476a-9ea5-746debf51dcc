// Ce fichier est généré automatiquement depuis prisma/schema.prisma. Ne pas éditer à la main !
export enum RoleUtilisateur {
  Administrateur = "Administrateur",
  GestionnaireGesGar = "GestionnaireGesGar",
  AnalysteFinancier = "AnalysteFinancier",
  Partenaire = "Partenaire",
  Bailleur = "Bailleur",
  Auditeur = "Auditeur",
}

export enum TypeAvenant {
  AUGMENTATION_MONTANT = "AUGMENTATION_MONTANT",
  REDUCTION_MONTANT = "REDUCTION_MONTANT",
  PROLONGATION_DUREE = "PROLONGATION_DUREE",
  MODIFICATION_CONDITIONS = "MODIFICATION_CONDITIONS",
  AUTRE = "AUTRE",
}

export enum StatutLigneGarantie {
  Active = "Active",
  Epuisee = "Epuisee",
  Suspendue = "Suspendue",
  Expiree = "Expiree",
  Cloturee = "Cloturee",
  Renouvelee = "Renouvelee",
  EnAttenteValidation = "EnAttenteValidation",
}

export enum TypePartenaire {
  Banque = "Banque",
  IMF = "IMF",
  SFD = "SFD",
  AutreFinancier = "AutreFinancier",
  Technique = "Technique",
  Institutionnel = "Institutionnel",
}

export enum StatutAllocation {
  Active = "Active",
  Epuisee = "Epuisee",
  Suspendue = "Suspendue",
  Expiree = "Expiree",
  Cloturee = "Cloturee",
  Renouvelee = "Renouvelee",
  Reaffectee = "Reaffectee",
  EnAttenteValidation = "EnAttenteValidation",
}

export enum Periodicite {
  Mensuelle = "Mensuelle",
  Trimestrielle = "Trimestrielle",
  Semestrielle = "Semestrielle",
  Annuelle = "Annuelle",
  Unique = "Unique",
}

export enum TypeClient {
  PME = "PME",
  PMI = "PMI",
  GE = "GE",
  GIE = "GIE",
  Cooperative = "Cooperative",
  Association = "Association",
  PersonnePhysique = "PersonnePhysique",
}

export enum GenreClient {
  Masculin = "Masculin",
  Feminin = "Feminin",
  Mixte = "Mixte",
  NonSpecifie = "NonSpecifie",
}

export enum TypeGarantie {
  Individuelle = "Individuelle",
  Portefeuille = "Portefeuille",
  Cautionnement = "Cautionnement",
}

export enum StatutGarantie {
  EnInstruction = "EnInstruction",
  Echue = "Echue",
  Active = "Active",
  MainleveeDemandee = "MainleveeDemandee",
  MainleveeAccordee = "MainleveeAccordee",
  MainleveeRefusee = "MainleveeRefusee",
  Transferree = "Transferree",
  EnSouffrance = "EnSouffrance",
  MiseEnJeuDemandee = "MiseEnJeuDemandee",
  MiseEnJeuAcceptee = "MiseEnJeuAcceptee",
  MiseEnJeuPayee = "MiseEnJeuPayee",
  MiseEnJeuRefusee = "MiseEnJeuRefusee",
  ClotureeAnormalement = "ClotureeAnormalement",
  Radiee = "Radiee",
  Supprimee = "Supprimee",
}

export enum OperateurRegle {
  EQUAL = "EQUAL",
  NOT_EQUAL = "NOT_EQUAL",
  GREATER_THAN = "GREATER_THAN",
  LESS_THAN = "LESS_THAN",
  GREATER_THAN_OR_EQUAL = "GREATER_THAN_OR_EQUAL",
  LESS_THAN_OR_EQUAL = "LESS_THAN_OR_EQUAL",
  IN = "IN",
  NOT_IN = "NOT_IN",
  CONTAINS = "CONTAINS",
  STARTS_WITH = "STARTS_WITH",
  ENDS_WITH = "ENDS_WITH",
  IS_NULL = "IS_NULL",
  IS_NOT_NULL = "IS_NOT_NULL",
}

export enum TypeMainlevee {
  RemboursementTotalCredit = "RemboursementTotalCredit",
  RemboursementPartielAvecRecouvrement = "RemboursementPartielAvecRecouvrement",
  PourTransfertGarantie = "PourTransfertGarantie",
  AutreMotif = "AutreMotif",
}

export enum StatutMainlevee {
  Demandee = "Demandee",
  EnCoursApprobation = "EnCoursApprobation",
  Accordee = "Accordee",
  Refusee = "Refusee",
}

export enum StatutMiseEnJeu {
  Demandee = "Demandee",
  EnCoursInstruction = "EnCoursInstruction",
  ApprouveePartiellement = "ApprouveePartiellement",
  ApprouveeTotalement = "ApprouveeTotalement",
  Refusee = "Refusee",
  Payee = "Payee",
  EnAttentePaiement = "EnAttentePaiement",
}

export enum TypePaiementInteretCommission {
  Interet = "Interet",
  Commission = "Commission",
}

export enum TypeParametre {
  STRING = "STRING",
  NUMBER = "NUMBER",
  BOOLEAN = "BOOLEAN",
  JSON = "JSON",
  TEXT = "TEXT",
}
