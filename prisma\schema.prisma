// schema.prisma

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Utilisateur {
  id                    Int      @id @default(autoincrement())
  nomUtilisateur        String   @unique
  motDePasse            String
  nom                   String
  prenom                String
  email                 String   @unique
  photoUrl              String?
  role                  RoleUtilisateur
  estActif              Bo<PERSON>an  @default(true) // Pour la suppression logique
  dateCreation          DateTime @default(now())
  utilisateurCreationId Int?
  utilisateurCreation   Utilisateur? @relation("UtilisateurCreePar", fields: [utilisateurCreationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  dateModification      DateTime @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("UtilisateurModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  bailleursCrees                 Bailleur[]                 @relation("BailleurCreePar")
  bailleursModifies              Bailleur[]                 @relation("BailleurModifiePar")
  lignesGarantieCrees            LigneGarantie[]            @relation("LigneGarantieCreePar")
  lignesGarantieModifies         LigneGarantie[]            @relation("LigneGarantieModifiePar")
  partenairesCrees               Partenaire[]               @relation("PartenaireCreePar")
  partenairesModifies            Partenaire[]               @relation("PartenaireModifiePar")
  allocationsCrees               AllocationLignePartenaire[] @relation("AllocationCreePar")
  allocationsModifies            AllocationLignePartenaire[] @relation("AllocationModifiePar")
  clientsBeneficiairesCrees      ClientBeneficiaire[]       @relation("ClientBeneficiaireCreePar")
  clientsBeneficiairesModifies   ClientBeneficiaire[]       @relation("ClientBeneficiaireModifiePar")
  secteursActiviteCrees          SecteurActivite[]          @relation("SecteurActiviteCreePar")
  secteursActiviteModifies       SecteurActivite[]          @relation("SecteurActiviteModifiePar")
  projetsCrees                   Projet[]                   @relation("ProjetCreePar")
  projetsModifies                Projet[]                   @relation("ProjetModifiePar")
  garantiesCrees                 Garantie[]                 @relation("GarantieCreePar")
  garantiesModifies              Garantie[]                 @relation("GarantieModifiePar")
  reglesEligibiliteCrees         RegleEligibilite[]         @relation("RegleEligibiliteCreePar")
  reglesEligibiliteModifies      RegleEligibilite[]         @relation("RegleEligibiliteModifiePar")
  groupesRegleEligibiliteCrees   GroupeRegleEligibilite[]   @relation("GroupeRegleEligibiliteCreePar")
  groupesRegleEligibiliteModifies GroupeRegleEligibilite[]  @relation("GroupeRegleEligibiliteModifiePar")
  mainleveesCrees                Mainlevee[]                @relation("MainleveeCreePar")
  mainleveesModifies             Mainlevee[]                @relation("MainleveeModifiePar")
  misesEnJeuCrees                MiseEnJeu[]                @relation("MiseEnJeuCreePar")
  misesEnJeuModifies             MiseEnJeu[]                @relation("MiseEnJeuModifiePar")
  transfertsGarantiesCrees       TransfertGarantie[]        @relation("TransfertGarantieCreePar")
  transfertsGarantiesModifies    TransfertGarantie[]        @relation("TransfertGarantieModifiePar")
  paiementsInteretsCommissionsCrees PaiementInteretCommission[] @relation("PaiementInteretCommissionCreePar")
  paiementsInteretsCommissionsModifies PaiementInteretCommission[] @relation("PaiementInteretCommissionModifiePar")
  parametresSystemeCrees         ParametreSysteme[]         @relation("ParametreSystemeCreePar")
  parametresSystemeModifies      ParametreSysteme[]         @relation("ParametreSystemeModifiePar")
  avenantsLigneGarantieCrees     AvenantLigneGarantie[]     @relation("AvenantLigneGarantieCreePar") // Ajouté
  avenantsLigneGarantieModifies  AvenantLigneGarantie[]     @relation("AvenantLigneGarantieModifiePar") // Ajouté
  avenantsAllocationCrees        AvenantAllocation[]        @relation("AvenantAllocationCreePar") // Ajouté
  avenantsAllocationModifies     AvenantAllocation[]        @relation("AvenantAllocationModifiePar") // Ajouté

  utilisateursCrees              Utilisateur[]              @relation("UtilisateurCreePar")
  utilisateursModifies           Utilisateur[]              @relation("UtilisateurModifiePar")
  documentsUploades              Document[]                 @relation("DocumentUploadPar")
  auditLogs                      AuditLog[]                 @relation("AuditLogUtilisateur")
}

enum RoleUtilisateur {
  Administrateur
  GestionnaireGesGar
  AnalysteFinancier
  Partenaire
  Bailleur
  Auditeur
}

model Bailleur {
  id                      Int       @id @default(autoincrement())
  nom                     String    @unique
  description             String?   @db.Text
  contact                 Json?     // { nomRepresentant: string, email: string, telephone: string }
  autresInformations      Json?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?      // Optionnel pour seed
  utilisateurCreation     Utilisateur? @relation("BailleurCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("BailleurModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  lignesGarantie LigneGarantie[]
}

model LigneGarantie {
  id                        Int       @id @default(autoincrement())
  bailleurId                Int
  bailleur                  Bailleur  @relation(fields: [bailleurId], references: [id])
  nom                       String    @unique
  referenceConvention       String?
  description               String?   @db.Text
  montantInitial            Decimal   @db.Decimal(18, 2)
  montantDisponible         Decimal   @db.Decimal(18, 2)
  dateOuverture             DateTime  @db.Date
  dateExpiration            DateTime  @db.Date
  devise                    String    @default("XOF")
  statut                    StatutLigneGarantie
  autresInformations        Json?
  ligneGarantiePrecedenteId Int?      @unique
  ligneGarantiePrecedente   LigneGarantie? @relation("RenouvellementLigneGarantie", fields: [ligneGarantiePrecedenteId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ligneGarantieSuivante     LigneGarantie? @relation("RenouvellementLigneGarantie")
  dateCreation              DateTime  @default(now())
  utilisateurCreationId     Int?
  utilisateurCreation       Utilisateur? @relation("LigneGarantieCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification          DateTime  @updatedAt
  utilisateurModificationId   Int?
  utilisateurModification   Utilisateur? @relation("LigneGarantieModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  avenants                  AvenantLigneGarantie[]
  allocations               AllocationLignePartenaire[]
  garanties                 Garantie[]
  reglesEligibilite         RegleEligibilite[]
  documents                 Document[] @relation("DocumentsLigneGarantie")
}

model AvenantLigneGarantie {
  id                      Int       @id @default(autoincrement())
  ligneGarantieId         Int
  ligneGarantie           LigneGarantie @relation(fields: [ligneGarantieId], references: [id])
  typeAvenant             TypeAvenant
  montantModification     Decimal?  @db.Decimal(18, 2)
  nouvelleDateExpiration  DateTime? @db.Date
  dateAvenant             DateTime  @db.Date
  raison                  String    @db.Text
  referenceDocument       String?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("AvenantLigneGarantieCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("AvenantLigneGarantieModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
}

enum TypeAvenant {
  AUGMENTATION_MONTANT
  REDUCTION_MONTANT
  PROLONGATION_DUREE
  MODIFICATION_CONDITIONS
  AUTRE
}

enum StatutLigneGarantie {
  Active
  Epuisee
  Suspendue
  Expiree
  Cloturee
  Renouvelee
  EnAttenteValidation
}

model Partenaire {
  id                      Int       @id @default(autoincrement())
  typePartenaire          TypePartenaire
  nom                     String    @unique
  description             String?   @db.Text
  contact                 Json?     // { nomRepresentant: string, adresse: string, email: string, telephone: string }
  convention              String?
  autresInformations      Json?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("PartenaireCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("PartenaireModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  allocations      AllocationLignePartenaire[]
  garanties        Garantie[]
  documents        Document[] @relation("DocumentsPartenaire")
}

enum TypePartenaire {
  Banque
  IMF
  SFD
  AutreFinancier
  Technique
  Institutionnel
}

model AllocationLignePartenaire {
  id                        Int       @id @default(autoincrement())
  ligneGarantieId           Int
  ligneGarantie             LigneGarantie @relation(fields: [ligneGarantieId], references: [id])
  partenaireId              Int
  partenaire                Partenaire @relation(fields: [partenaireId], references: [id])
  referenceConvention       String?
  montantAlloue             Decimal   @db.Decimal(18, 2)
  montantDisponible         Decimal   @db.Decimal(18, 2)
  dateAllocation            DateTime  @db.Date
  dateExpiration            DateTime? @db.Date
  statut                    StatutAllocation
  tauxCouvertureMax         Decimal?  @db.Decimal(5, 2)
  tauxInteret               Decimal?  @db.Decimal(5, 2)
  tauxCommission            Decimal?  @db.Decimal(5, 2)
  periodicitePaiementInteret Periodicite?
  periodicitePaiementCommission Periodicite?
  commentaires              String?   @db.Text
  allocationPrecedenteId    Int?      @unique
  allocationPrecedente      AllocationLignePartenaire? @relation("RenouvellementAllocation", fields: [allocationPrecedenteId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  allocationSuivante        AllocationLignePartenaire? @relation("RenouvellementAllocation")
  dateCreation              DateTime  @default(now())
  utilisateurCreationId     Int?
  utilisateurCreation       Utilisateur? @relation("AllocationCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification          DateTime  @updatedAt
  utilisateurModificationId   Int?
  utilisateurModification   Utilisateur? @relation("AllocationModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  avenants                    AvenantAllocation[]
  garanties                   Garantie[]
  paiementsInteretsCommissions PaiementInteretCommission[]
  documents                   Document[] @relation("DocumentsAllocation")
}

model AvenantAllocation {
  id                      Int       @id @default(autoincrement())
  allocationId            Int
  allocation              AllocationLignePartenaire @relation(fields: [allocationId], references: [id])
  typeAvenant             TypeAvenant
  montantModification     Decimal?  @db.Decimal(18, 2)
  nouvelleDateExpiration  DateTime? @db.Date
  dateAvenant             DateTime  @db.Date
  raison                  String    @db.Text
  referenceDocument       String?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("AvenantAllocationCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("AvenantAllocationModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
}

enum StatutAllocation {
  Active
  Epuisee
  Suspendue
  Expiree
  Cloturee
  Renouvelee
  Reaffectee
  EnAttenteValidation
}

enum Periodicite {
  Mensuelle
  Trimestrielle
  Semestrielle
  Annuelle
  Unique
}

model ClientBeneficiaire {
  id                      Int       @id @default(autoincrement())
  typeClient              TypeClient
  nomOuRaisonSociale      String
  identifiantUnique       String?   @unique
  age                     Int?
  genre                   GenreClient?
  informationsContact     Json      // { adressePhysique: string, email: string, telephone1: string, telephone2: string? }
  secteurActivitePrincipalId Int?
  secteurActivitePrincipal SecteurActivite? @relation("ClientSecteurPrincipal", fields: [secteurActivitePrincipalId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  autresInformations      Json?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("ClientBeneficiaireCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("ClientBeneficiaireModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  projets   Projet[]
  garanties Garantie[]
  documents Document[] @relation("DocumentsClient")
}

enum TypeClient {
  PME
  PMI
  GE
  GIE
  Cooperative
  Association
  PersonnePhysique
}

enum GenreClient {
  Masculin
  Feminin
  Mixte
  NonSpecifie
}

model SecteurActivite {
  id                      Int       @id @default(autoincrement())
  code                    String?   @unique
  nom                     String    @unique
  description             String?   @db.Text
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("SecteurActiviteCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("SecteurActiviteModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  projets                   Projet[]
  clientsBeneficiaires      ClientBeneficiaire[] @relation("ClientSecteurPrincipal")
}

model Projet {
  id                      Int       @id @default(autoincrement())
  nom                     String
  description             String    @db.Text
  secteurActiviteId       Int
  secteurActivite         SecteurActivite @relation(fields: [secteurActiviteId], references: [id])
  clientBeneficiaireId    Int
  clientBeneficiaire      ClientBeneficiaire @relation(fields: [clientBeneficiaireId], references: [id])
  localisation            Json?
  coutTotalProjet         Decimal?  @db.Decimal(18, 2)
  autresInformations      Json?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("ProjetCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("ProjetModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  garanties Garantie[]
  documents Document[] @relation("DocumentsProjet")
}

model Garantie {
  id                        Int       @id @default(autoincrement())
  referenceGarantie         String    @unique
  ligneGarantieId           Int
  ligneGarantie             LigneGarantie @relation(fields: [ligneGarantieId], references: [id])
  allocationId              Int
  allocation                AllocationLignePartenaire @relation(fields: [allocationId], references: [id])
  partenaireId              Int
  partenaire                Partenaire @relation(fields: [partenaireId], references: [id])
  projetId                  Int
  projet                    Projet    @relation(fields: [projetId], references: [id])
  clientBeneficiaireId      Int
  clientBeneficiaire        ClientBeneficiaire @relation(fields: [clientBeneficiaireId], references: [id])

  typeGarantie              TypeGarantie
  montantCredit             Decimal   @db.Decimal(18, 2)
  tauxCouvertureApplique    Decimal   @db.Decimal(5, 2)
  montantGarantie           Decimal   @db.Decimal(18, 2)

  dateOctroiCredit          DateTime  @db.Date
  dateDemandeGarantie       DateTime  @db.Date
  dateAccordGarantie        DateTime  @db.Date
  dateEffetGarantie         DateTime  @db.Date
  dateEcheanceInitialeCredit DateTime @db.Date
  dateEcheanceGarantie      DateTime  @db.Date

  statut                    StatutGarantie
  conditionsParticulieres   String?   @db.Text
  identifiantCreditPartenaire String

  dateDernierRemboursementClient DateTime? @db.Date
  montantRestantDuCredit    Decimal?  @db.Decimal(18, 2)
  nombreEcheancesImpayees   Int?      @default(0)
  delaiMiseEnJeu            Int       @default(90)

  dateCreation              DateTime  @default(now())
  utilisateurCreationId     Int?
  utilisateurCreation       Utilisateur? @relation("GarantieCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification          DateTime  @updatedAt
  utilisateurModificationId   Int?
  utilisateurModification   Utilisateur? @relation("GarantieModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  mainlevees           Mainlevee[]
  misesEnJeu           MiseEnJeu[]
  transfertsOrigine    TransfertGarantie[] @relation("GarantieOrigine")
  transfertsDestination TransfertGarantie[] @relation("GarantieDestination")
  documents            Document[]          @relation("DocumentsGarantie")
}

enum TypeGarantie {
  Individuelle
  Portefeuille
  Cautionnement
}

enum StatutGarantie {
  EnInstruction
  Echue
  Active
  Validee
  MainleveeDemandee
  MainleveeAccordee
  MainleveeRefusee
  Transferree
  EnSouffrance
  MiseEnJeuDemandee
  MiseEnJeuAcceptee
  MiseEnJeuPayee
  MiseEnJeuRefusee
  ClotureeAnormalement
  Radiee
  Supprimee
}

model RegleEligibilite {
  id                      Int       @id @default(autoincrement())
  code                    String    @unique
  description             String    @db.Text
  groupeRegleId           Int?
  groupeRegle             GroupeRegleEligibilite? @relation(fields: [groupeRegleId], references: [id])
  ligneGarantieId         Int?
  ligneGarantie           LigneGarantie? @relation(fields: [ligneGarantieId], references: [id])

  champConcerne           String
  operateur               OperateurRegle
  valeurReference         String
  messageErreur           String?
  estActive               Boolean   @default(true)

  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("RegleEligibiliteCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("RegleEligibiliteModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
}

model GroupeRegleEligibilite {
  id                      Int       @id @default(autoincrement())
  nom                     String    @unique
  description             String?   @db.Text
  estActif                Boolean   @default(true)
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("GroupeRegleEligibiliteCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("GroupeRegleEligibiliteModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  regles                  RegleEligibilite[]
}

enum OperateurRegle {
  EQUAL
  NOT_EQUAL
  GREATER_THAN
  LESS_THAN
  GREATER_THAN_OR_EQUAL
  LESS_THAN_OR_EQUAL
  IN
  NOT_IN
  CONTAINS
  STARTS_WITH
  ENDS_WITH
  IS_NULL
  IS_NOT_NULL
}

model Mainlevee {
  id                      Int       @id @default(autoincrement())
  garantieId              Int       @unique
  garantie                Garantie  @relation(fields: [garantieId], references: [id])
  typeMainlevee           TypeMainlevee
  dateDemande             DateTime  @db.Date
  dateDecision            DateTime? @db.Date
  montantRecupere         Decimal?  @db.Decimal(18, 2)
  commentairesDemande     String?   @db.Text
  commentairesDecision    String?   @db.Text
  statut                  StatutMainlevee
  raisonRefus             String?   @db.Text
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("MainleveeCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("MainleveeModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  transfertsGaranties TransfertGarantie[]
  documents           Document[] @relation("DocumentsMainlevee")
}

enum TypeMainlevee {
  RemboursementTotalCredit
  RemboursementPartielAvecRecouvrement
  PourTransfertGarantie
  AutreMotif
}

enum StatutMainlevee {
  Demandee
  EnCoursApprobation
  Accordee
  Refusee
}

model MiseEnJeu {
  id                      Int       @id @default(autoincrement())
  garantieId              Int
  garantie                Garantie  @relation(fields: [garantieId], references: [id])
  dateDemande             DateTime  @db.Date
  dateDecision            DateTime? @db.Date
  montantDemande          Decimal   @db.Decimal(18, 2)
  montantApprouve         Decimal?  @db.Decimal(18, 2)
  montantPaye             Decimal?  @db.Decimal(18, 2)
  datePaiement            DateTime? @db.Date
  motifDemande            String    @db.Text
  statut                  StatutMiseEnJeu
  commentairesDecision    String?   @db.Text
  referencePaiement       String?
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("MiseEnJeuCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("MiseEnJeuModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  documents               Document[] @relation("DocumentsMiseEnJeu")
}

enum StatutMiseEnJeu {
  Demandee
  EnCoursInstruction
  ApprouveePartiellement
  ApprouveeTotalement
  Refusee
  Payee
  EnAttentePaiement
}

model TransfertGarantie {
  id                      Int       @id @default(autoincrement())
  garantieOrigineId       Int
  garantieOrigine         Garantie  @relation("GarantieOrigine", fields: [garantieOrigineId], references: [id])
  garantieDestinationId   Int       @unique
  garantieDestination     Garantie  @relation("GarantieDestination", fields: [garantieDestinationId], references: [id])
  dateTransfert           DateTime  @db.Date
  mainleveeOrigineId      Int       @unique
  mainleveeOrigine        Mainlevee @relation(fields: [mainleveeOrigineId], references: [id])
  commentaires            String?   @db.Text
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("TransfertGarantieCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("TransfertGarantieModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  documents               Document[] @relation("DocumentsTransfert")
}

model PaiementInteretCommission {
  id                      Int       @id @default(autoincrement())
  allocationId            Int
  allocation              AllocationLignePartenaire @relation(fields: [allocationId], references: [id])
  datePaiement            DateTime  @db.Date
  montantInteret          Decimal?  @db.Decimal(18, 2)
  montantCommission       Decimal?  @db.Decimal(18, 2)
  typePaiement            TypePaiementInteretCommission
  periodeConcerneeDebut   DateTime? @db.Date
  periodeConcerneeFin     DateTime? @db.Date
  referencePaiement       String?
  commentaires            String?   @db.Text
  dateCreation            DateTime  @default(now())
  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("PaiementInteretCommissionCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  dateModification        DateTime  @updatedAt
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("PaiementInteretCommissionModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  documents               Document[] @relation("DocumentsPaiement")
}

enum TypePaiementInteretCommission {
  Interet
  Commission
}

model Document {
  id                  Int       @id @default(autoincrement())
  nomFichier          String
  cheminStockage      String
  typeMime            String
  taille              BigInt
  dateUpload          DateTime  @default(now())
  description         String?
  categorieDocument   String?

  ligneGarantieId     Int?
  ligneGarantie       LigneGarantie? @relation("DocumentsLigneGarantie", fields: [ligneGarantieId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  partenaireId        Int?
  partenaire          Partenaire?    @relation("DocumentsPartenaire", fields: [partenaireId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  allocationId        Int?
  allocation          AllocationLignePartenaire? @relation("DocumentsAllocation", fields: [allocationId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  clientBeneficiaireId Int?
  clientBeneficiaire  ClientBeneficiaire? @relation("DocumentsClient", fields: [clientBeneficiaireId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projetId            Int?
  projet              Projet?        @relation("DocumentsProjet", fields: [projetId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  garantieId          Int?
  garantie            Garantie?      @relation("DocumentsGarantie", fields: [garantieId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  mainleveeId         Int?
  mainlevee           Mainlevee?     @relation("DocumentsMainlevee", fields: [mainleveeId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  miseEnJeuId         Int?
  miseEnJeu           MiseEnJeu?     @relation("DocumentsMiseEnJeu", fields: [miseEnJeuId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  transfertGarantieId Int?
  transfertGarantie   TransfertGarantie? @relation("DocumentsTransfert", fields: [transfertGarantieId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  paiementId          Int?
  paiement            PaiementInteretCommission? @relation("DocumentsPaiement", fields: [paiementId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  utilisateurUploadId Int
  utilisateurUpload   Utilisateur @relation("DocumentUploadPar", fields: [utilisateurUploadId], references: [id])
}

model AuditLog {
  id                Int       @id @default(autoincrement())
  timestamp         DateTime  @default(now())
  utilisateurId     Int?
  utilisateur       Utilisateur? @relation("AuditLogUtilisateur", fields: [utilisateurId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  action            String
  entite            String?
  entiteId          String?
  ancienValeurs     Json?
  nouvellesValeurs  Json?
  descriptionAction String?
  adresseIp         String?
  userAgent         String?
  
  // Champs étendus pour l'audit amélioré
  sessionId         String?
  requestId         String?
  module            String?   // Module fonctionnel (GARANTIES, ALLOCATIONS, etc.)
  operation         String?   // Opération métier spécifique
  resourceId        String?   // ID de la ressource concernée
  criticalityLevel  String?   @default("MEDIUM") // LOW, MEDIUM, HIGH, CRITICAL
  metadata          Json?     // Métadonnées supplémentaires
  
  @@index([utilisateurId])
  @@index([timestamp])
  @@index([action])
  @@index([entite])
  @@index([criticalityLevel])
  @@index([module])
}

model ParametreSysteme {
  id                Int      @id @default(autoincrement())
  cle               String   @unique
  valeur            String   @db.Text
  description       String?  @db.Text
  typeValeur        TypeParametre @default(STRING)
  estModifiable     Boolean  @default(true)
  dateCreation      DateTime @default(now())
  dateModification  DateTime @updatedAt

  utilisateurCreationId   Int?
  utilisateurCreation     Utilisateur? @relation("ParametreSystemeCreePar", fields: [utilisateurCreationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  utilisateurModificationId Int?
  utilisateurModification Utilisateur? @relation("ParametreSystemeModifiePar", fields: [utilisateurModificationId], references: [id], onDelete: SetNull, onUpdate: NoAction)
}

enum TypeParametre {
  STRING
  NUMBER
  BOOLEAN
  JSON
  TEXT
}