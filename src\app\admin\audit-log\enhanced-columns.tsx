"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, AlertTriangle, Shield, Info, Zap } from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

// Type étendu pour les logs d'audit améliorés
export type EnhancedAuditLog = {
  id: number;
  timestamp: Date;
  utilisateur?: {
    id: number;
    nomUtilisateur: string;
    nom: string;
    prenom: string;
  } | null;
  action: string;
  entite?: string | null;
  entiteId?: string | null;
  ancienValeurs?: any;
  nouvellesValeurs?: any;
  descriptionAction?: string | null;
  adresseIp?: string | null;
  userAgent?: string | null;
  sessionId?: string | null;
  requestId?: string | null;
  module?: string | null;
  operation?: string | null;
  resourceId?: string | null;
  criticalityLevel?: string | null;
  metadata?: any;
};

// Fonction pour obtenir l'icône selon le niveau de criticité
const getCriticalityIcon = (level?: string | null) => {
  switch (level) {
    case 'CRITICAL':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'HIGH':
      return <Shield className="h-4 w-4 text-orange-500" />;
    case 'MEDIUM':
      return <Info className="h-4 w-4 text-yellow-500" />;
    case 'LOW':
      return <Zap className="h-4 w-4 text-green-500" />;
    default:
      return <Info className="h-4 w-4 text-gray-500" />;
  }
};

// Fonction pour obtenir la couleur du badge selon le niveau de criticité
const getCriticalityColor = (level?: string | null): "default" | "secondary" | "destructive" | "outline" => {
  switch (level) {
    case 'CRITICAL':
      return 'destructive';
    case 'HIGH':
      return 'destructive';
    case 'MEDIUM':
      return 'secondary';
    case 'LOW':
      return 'outline';
    default:
      return 'default';
  }
};

// Fonction pour obtenir la couleur du module
const getModuleColor = (module?: string | null): "default" | "secondary" | "destructive" | "outline" => {
  switch (module) {
    case 'USER_MANAGEMENT':
      return 'destructive';
    case 'SYSTEM_CONFIG':
      return 'destructive';
    case 'FINANCIAL_OPERATIONS':
      return 'secondary';
    case 'GARANTIES':
    case 'ALLOCATIONS':
      return 'outline';
    default:
      return 'default';
  }
};

export const enhancedAuditLogColumns: ColumnDef<EnhancedAuditLog>[] = [
  {
    accessorKey: "timestamp",
    header: "Date/Heure",
    cell: ({ row }) => {
      const date = row.getValue("timestamp") as Date;
      return (
        <div className="text-sm">
          <div className="font-medium">
            {format(date, "dd/MM/yyyy", { locale: fr })}
          </div>
          <div className="text-muted-foreground">
            {format(date, "HH:mm:ss", { locale: fr })}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "criticalityLevel",
    header: "Criticité",
    cell: ({ row }) => {
      const level = row.getValue("criticalityLevel") as string;
      return (
        <div className="flex items-center space-x-2">
          {getCriticalityIcon(level)}
          <Badge variant={getCriticalityColor(level)} className="text-xs">
            {level || 'MEDIUM'}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "module",
    header: "Module",
    cell: ({ row }) => {
      const module = row.getValue("module") as string;
      if (!module) return <span className="text-muted-foreground">-</span>;
      
      return (
        <Badge variant={getModuleColor(module)} className="text-xs">
          {module.replace('_', ' ')}
        </Badge>
      );
    },
  },
  {
    accessorKey: "utilisateur",
    header: "Utilisateur",
    cell: ({ row }) => {
      const utilisateur = row.getValue("utilisateur") as EnhancedAuditLog["utilisateur"];
      if (!utilisateur) {
        return <span className="text-muted-foreground">Système</span>;
      }
      return (
        <div className="text-sm">
          <div className="font-medium">{utilisateur.nomUtilisateur}</div>
          <div className="text-muted-foreground">
            {utilisateur.prenom} {utilisateur.nom}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    cell: ({ row }) => {
      const action = row.getValue("action") as string;
      const operation = row.original.operation;
      
      return (
        <div className="text-sm">
          <div className="font-medium">{action}</div>
          {operation && operation !== action && (
            <div className="text-muted-foreground text-xs">{operation}</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "entite",
    header: "Entité",
    cell: ({ row }) => {
      const entite = row.getValue("entite") as string;
      const entiteId = row.original.entiteId;
      const resourceId = row.original.resourceId;
      
      if (!entite) return <span className="text-muted-foreground">-</span>;
      
      return (
        <div className="text-sm">
          <div className="font-medium">{entite}</div>
          {(entiteId || resourceId) && (
            <div className="text-muted-foreground text-xs">
              ID: {entiteId || resourceId}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "descriptionAction",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("descriptionAction") as string;
      if (!description) return <span className="text-muted-foreground">-</span>;
      
      return (
        <div className="max-w-xs truncate text-sm" title={description}>
          {description}
        </div>
      );
    },
  },
  {
    accessorKey: "adresseIp",
    header: "Adresse IP",
    cell: ({ row }) => {
      const ip = row.getValue("adresseIp") as string;
      const sessionId = row.original.sessionId;
      
      return (
        <div className="text-sm">
          <div className="font-mono">{ip || 'N/A'}</div>
          {sessionId && (
            <div className="text-muted-foreground text-xs font-mono">
              Session: {sessionId.slice(-8)}
            </div>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const auditLog = row.original;
      
      return (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Ouvrir un modal avec les détails complets
              console.log("Détails de l'audit log:", auditLog);
            }}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];

// Composant pour afficher les métadonnées dans un format lisible
export const MetadataDisplay = ({ metadata }: { metadata: any }) => {
  if (!metadata) return null;
  
  return (
    <div className="space-y-2">
      <h4 className="font-medium text-sm">Métadonnées :</h4>
      <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
        {JSON.stringify(metadata, null, 2)}
      </pre>
    </div>
  );
};

// Composant pour afficher les changements de valeurs
export const ValuesComparison = ({ 
  ancienValeurs, 
  nouvellesValeurs 
}: { 
  ancienValeurs: any; 
  nouvellesValeurs: any; 
}) => {
  if (!ancienValeurs && !nouvellesValeurs) return null;
  
  return (
    <div className="space-y-4">
      {ancienValeurs && (
        <div>
          <h4 className="font-medium text-sm text-red-600">Anciennes valeurs :</h4>
          <pre className="text-xs bg-red-50 p-2 rounded overflow-auto max-h-40">
            {JSON.stringify(ancienValeurs, null, 2)}
          </pre>
        </div>
      )}
      {nouvellesValeurs && (
        <div>
          <h4 className="font-medium text-sm text-green-600">Nouvelles valeurs :</h4>
          <pre className="text-xs bg-green-50 p-2 rounded overflow-auto max-h-40">
            {JSON.stringify(nouvellesValeurs, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};