// src/app/api/audit-examples/financial-operations/route.ts
// Exemple d'implémentation de l'audit pour les opérations financières

import { NextResponse } from "next/server";
import { withFinancialAudit, BusinessAction } from "@/lib/audit-wrapper";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
    }

    const body = await request.json();
    const { allocationId, montantInteret, montantCommission, typeOperation } = body;

    // Exemple d'opération financière auditée
    const result = await withFinancialAudit(
      async () => {
        // Vérifier que l'allocation existe
        const allocation = await prisma.allocationLignePartenaire.findUnique({
          where: { id: parseInt(allocationId) },
          include: { ligneGarantie: true, partenaire: true }
        });

        if (!allocation) {
          throw new Error("Allocation non trouvée");
        }

        // Créer un enregistrement de paiement
        const paiement = await prisma.paiementInteretCommission.create({
          data: {
            allocationId: parseInt(allocationId),
            montantInteret: montantInteret ? parseFloat(montantInteret) : null,
            montantCommission: montantCommission ? parseFloat(montantCommission) : null,
            typePaiement: 'Commission',
            datePaiement: new Date(),
            referencePaiement: `PAY-${Date.now()}`,
            utilisateurCreationId: parseInt(session.user.id as string),
            utilisateurModificationId: parseInt(session.user.id as string)
          }
        });

        return paiement;
      },
      BusinessAction.PAIEMENT_CREATE,
      {
        entityType: 'PaiementInteretCommission',
        entityId: allocationId,
        amount: parseFloat(montantInteret || montantCommission || '0'),
        currency: 'XOF', // Devise par défaut
        reference: `PAY-${Date.now()}`,
        metadata: {
          typeOperation,
          processedBy: session.user.name || session.user.email,
          allocationId,
          montantInteret,
          montantCommission,
          timestamp: new Date().toISOString()
        }
      }
    );

    return NextResponse.json({
      message: "Paiement effectué avec succès",
      paiement: result
    });

  } catch (error) {
    return NextResponse.json(
      { message: (error as Error).message },
      { status: 500 }
    );
  }
}