// src/lib/schemas/garantie.schema.ts
import { z } from "zod";
import { TypeGarantie, StatutGarantie } from "@prisma/client";

// ... (GarantieSchema et helpers existants) ...
const statutGarantieValues = Object.values(StatutGarantie) as [string, ...string[]];


export const UpdateGarantieSchema = z.object({
  // Champs généralement non modifiables après création initiale via ce formulaire simple
  // allocationId: z.string().optional(), // Normalement non modifiable ici
  // projetId: z.string().optional(),     // Normalement non modifiable ici
  // typeGarantie: z.enum(typeGarantieValues).optional(),
  // montantCreditStr: z.string().optional(),
  // tauxCouvertureAppliqueStr: z.string().optional(),

  // Champs modifiables
  statut: z.enum(statutGarantieValues, {
    required_error: "Le nouveau statut est requis pour une mise à jour de statut.",
  }),
  identifiantCreditPartenaire: z.string().min(1, "L'identifiant du crédit est requis.").max(100).optional(),
  conditionsParticulieres: z.string().max(2000).optional().or(z.literal('')),
  delaiMiseEnJeu: z.string()
    .refine(val => !isNaN(parseInt(val)) && parseInt(val) >= 0, {
        message: "Le délai de mise en jeu doit être un nombre positif ou nul.",
    }).optional(),

  // Dates qui pourraient être ajustées (selon règles métier)
  // Par exemple, la date d'échéance de la garantie pourrait être prolongée.
  // Les dates d'octroi, demande, accord, effet sont généralement fixées plus tôt.
  dateEcheanceGarantie: z.date({
    invalid_type_error: "Format de date d'échéance de garantie invalide.",
  }).optional(),
  dateAccordGarantie: z.date({ // Peut être défini/modifié lors du passage à Echue/Active
    invalid_type_error: "Format de date d'accord invalide.",
  }).optional().nullable(),
  dateEffetGarantie: z.date({ // Peut être défini/modifié lors du passage à Active
    invalid_type_error: "Format de date d'effet invalide.",
  }).optional().nullable(),


  // Champs de suivi (pourraient être dans un formulaire/API dédié, mais inclus ici pour l'exemple)
  dateDernierRemboursementClient: z.date({
    invalid_type_error: "Format de date invalide.",
  }).optional().nullable(),
  montantRestantDuCreditStr: z.string().optional().or(z.literal(''))
      .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) >= 0), {
          message: "Le montant restant dû doit être un nombre positif ou nul.",
      }),
  nombreEcheancesImpayeesStr: z.string().optional().or(z.literal(''))
      .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseInt(val)) && parseInt(val) >= 0), {
          message: "Le nombre d'échéances impayées doit être un entier positif ou nul.",
      }),
})
// Validations croisées si nécessaire pour les dates de mise à jour
.refine(data => {
    if (data.dateEffetGarantie && data.dateAccordGarantie) {
        return data.dateEffetGarantie >= data.dateAccordGarantie;
    }
    return true;
}, { message: "La date d'effet doit être postérieure ou égale à la date d'accord.", path: ["dateEffetGarantie"]})
.refine(data => {
    if (data.dateEcheanceGarantie && data.dateEffetGarantie) {
        return data.dateEcheanceGarantie >= data.dateEffetGarantie;
    }
    return true;
}, { message: "La date d'échéance doit être postérieure ou égale à la date d'effet.", path: ["dateEcheanceGarantie"]});

export type UpdateGarantieFormValues = z.infer<typeof UpdateGarantieSchema>;

const typeGarantieValues = Object.values(TypeGarantie) as [string, ...string[]];

export const GarantieSchema = z.object({
  allocationId: z.string().min(1, "L'allocation est requise."),
  projetId: z.string().min(1, "Le projet est requis."),
  typeGarantie: z.enum(typeGarantieValues, { required_error: "Le type de garantie est requis." }),
  montantCreditStr: z.string().min(1, "Le montant du crédit est requis."),
  tauxCouvertureAppliqueStr: z.string().min(1, "Le taux de couverture est requis."),
  dateOctroiCredit: z.date(),
  dateDemandeGarantie: z.date(),
  dateEcheanceInitialeCredit: z.date(),
  dateEcheanceGarantie: z.date().optional(),
  statut: z.enum(statutGarantieValues),
  identifiantCreditPartenaire: z.string().min(1, "L'identifiant du crédit est requis.").max(100),
  conditionsParticulieres: z.string().max(2000).optional().or(z.literal('')),
  delaiMiseEnJeu: z.string().refine(val => !isNaN(parseInt(val, 10)) && parseInt(val, 10) >= 0, {
    message: "Le délai de mise en jeu doit être un nombre positif ou nul.",
  }),
});
export type GarantieFormValues = z.infer<typeof GarantieSchema>;