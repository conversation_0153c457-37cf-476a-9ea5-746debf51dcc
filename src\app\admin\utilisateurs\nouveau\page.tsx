// src/app/admin/utilisateurs/nouveau/page.tsx
"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateUserSchema, CreateUserFormValues } from "@/lib/schemas/user.schema";
import { RoleUtilisateur } from "@prisma/client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label"; // Assurez-vous que Label est importé si vous l'utilisez explicitement
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react"; // Pour la protection côté client

// Convertir l'enum Prisma en un format utilisable pour les options du Select
const roleOptions = Object.values(RoleUtilisateur).map(role => ({
  value: role,
  label: role.replace(/([A-Z])/g, ' $1').trim(), // Ajoute des espaces pour la lisibilité ex: GestionnaireGesGar -> Gestionnaire Ges Gar
}));
// Ou traduisez-les manuellement si nécessaire pour l'UI
const roleDisplayOptions = [
    { value: RoleUtilisateur.Administrateur, label: "Administrateur" },
    { value: RoleUtilisateur.GestionnaireGesGar, label: "Gestionnaire GesGar" },
    { value: RoleUtilisateur.AnalysteFinancier, label: "Analyste Financier" },
    { value: RoleUtilisateur.Partenaire, label: "Partenaire (Externe)" },
    { value: RoleUtilisateur.Bailleur, label: "Bailleur (Externe)" },
    { value: RoleUtilisateur.Auditeur, label: "Auditeur" },
];


export default function AddUserPage() {
  const router = useRouter();
  const { data: session, status: sessionStatus } = useSession(); // Pour la protection

  const form = useForm<CreateUserFormValues>({
    resolver: zodResolver(CreateUserSchema),
    defaultValues: {
      nomUtilisateur: "",
      email: "",
      nom: "",
      prenom: "",
      role: undefined, // Laisser undefined pour que le placeholder du Select s'affiche
      motDePasse: "",
    },
  });

  const onSubmit = async (values: CreateUserFormValues) => {
    const requestData = {
      nomUtilisateur: values.nomUtilisateur,
      email: values.email,
      motDePasse: values.motDePasse,
      nom: values.nom,
      prenom: values.prenom,
      role: values.role
    };

    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        // Handle non-JSON error responses (like 405 Method Not Allowed)
        let errorMessage = "Une erreur s'est produite.";
        let result = null;
        
        try {
          result = await response.json();
          errorMessage = result.message || errorMessage;
        } catch (jsonError) {
          // If response is not JSON, use status text
          errorMessage = `Erreur ${response.status}: ${response.statusText}`;
        }

        // Gestion spécifique des erreurs de duplication
        if (response.status === 409 && result) {
          form.setError(result.message.includes('email') ? 'email' : 'nomUtilisateur', {
            type: 'manual',
            message: result.message,
          });
          return;
        }
        
        toast.error("Erreur", {
          description: errorMessage,
        });
        return;
      }

      const result = await response.json();

      toast.success("Utilisateur créé", {
        description: `L'utilisateur ${values.nomUtilisateur} a été ajouté avec succès.`,
      });
      router.push("/admin/utilisateurs");
      router.refresh();

    } catch (error) {
      console.error("Erreur lors de la création de l'utilisateur:", error);
      toast.error("Erreur", {
        description: "Une erreur inattendue s'est produite.",
      });
    }
  };

  // Protection côté client
  useEffect(() => {
    if (sessionStatus === "loading") return;
    if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
      toast.error("Accès refusé", {
        description: "Vous n'avez pas les droits pour accéder à cette page.",
      });
      router.replace("/"); // ou /admin si vous avez une page d'accueil admin
    }
  }, [session, sessionStatus, router, toast]);

  if (sessionStatus === "loading" || !session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return <div className="p-6">Vérification des droits...</div>;
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0 max-w-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Ajouter un Nouvel Utilisateur</h1>
        <Button variant="outline" asChild>
            <Link href="/admin/utilisateurs">Retour à la liste</Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 bg-white p-6 shadow-md rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="prenom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prénom</FormLabel>
                  <FormControl>
                    <Input placeholder="Jean" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom</FormLabel>
                  <FormControl>
                    <Input placeholder="Dupont" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="nomUtilisateur"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom d'utilisateur</FormLabel>
                <FormControl>
                  <Input placeholder="jdupont" {...field} />
                </FormControl>
                <FormDescription>
                  Utilisé pour la connexion. Doit être unique.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="motDePasse"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mot de passe</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="********" {...field} />
                </FormControl>
                <FormDescription>
                  Au moins 8 caractères.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rôle</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un rôle" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {roleDisplayOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={() => router.push('/admin/utilisateurs')}>
              Annuler
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? "Création en cours..." : "Créer l'Utilisateur"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}