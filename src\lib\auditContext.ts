import { persistAuditLog } from "./persistAuditLog";

interface AuditContext {
  userId?: string | number;
  userAgent?: string;
  ipAddress?: string;
  operation: string;
}

export const auditContext = {
   run: async <T>(ctx: AuditContext, fn: () => Promise<T>): Promise<T> => {
    const auditEntry = {
      ...ctx,
      timestamp: new Date(),
    };
    
    try {
      const result = await fn();
      await persistAuditLog({ ...auditEntry, status: 'success' });
      return result;
    } catch (error) {
      await persistAuditLog({ 
        ...auditEntry, 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
   },
 };
