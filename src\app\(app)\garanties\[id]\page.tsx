"use client";

import React, { useEffect, useState, useC<PERSON>back } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { G<PERSON><PERSON>, LigneGarantie, Bailleur, AllocationLignePartenaire, Partenaire, Projet, ClientBeneficiaire, SecteurActivite, TypeGarantie, StatutGarantie, RoleUtilisateur, TypeClient, TypePartenaire } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Edit, Handshake, AlertOctagon, Loader2 } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>T<PERSON><PERSON> } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UpdateGarantieSchema, type UpdateGarantieFormValues } from "@/lib/schemas/garantie.schema";

const formatCurrency = (amount: any, currency: string = "XOF") => {
    if (amount === null || amount === undefined) return "-";
    const num = typeof amount === 'string' ? parseFloat(amount.replace(',', '.')) : parseFloat(amount);
    if (isNaN(num)) return "-";
    return new Intl.NumberFormat("fr-FR", { style: "currency", currency: currency, minimumFractionDigits: 0 }).format(num);
};

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR', { day: '2-digit', month: 'long', year: 'numeric' }) : '-';

const getStatutGarantieBadgeVariant = (statut: StatutGarantie | undefined) => {
  switch (statut) {
    case StatutGarantie.EnInstruction: return "secondary";
    case StatutGarantie.Active: return "default";
    case StatutGarantie.EnSouffrance: return "destructive";
    case StatutGarantie.Echue: return "destructive";
    case StatutGarantie.MainleveeDemandee: return "outline";
    case StatutGarantie.MainleveeAccordee: return "secondary";
    case StatutGarantie.MiseEnJeuDemandee: return "destructive";
    case StatutGarantie.MiseEnJeuAcceptee: return "destructive";
    case StatutGarantie.Supprimee: return "secondary";
    default: return "secondary";
  }
};

const getTypeClientLabel = (type: TypeClient | undefined) => {
  switch (type) {
    case TypeClient.PersonnePhysique: return "Personne Physique";
    case TypeClient.PME: return "PME";
    case TypeClient.PMI: return "PMI";
    case TypeClient.GE: return "Grande Entreprise";
    case TypeClient.GIE: return "GIE";
    case TypeClient.Cooperative: return "Coopérative";
    case TypeClient.Association: return "Association";
    default: return type || "";
  }
};

const getTypePartenaireLabel = (type: TypePartenaire | undefined) => {
  switch (type) {
    case TypePartenaire.Banque: return "Banque";
    case TypePartenaire.IMF: return "Institution de Microfinance";
    case TypePartenaire.SFD: return "Société de Financement Décentralisée";
    case TypePartenaire.AutreFinancier: return "Autre Financier";
    case TypePartenaire.Technique: return "Partenaire Technique";
    case TypePartenaire.Institutionnel: return "Partenaire Institutionnel";
    default: return type || "";
  }
};

type GarantieDetailsType = Garantie & {
    ligneGarantie: Pick<LigneGarantie, "id" | "nom" | "devise"> & { bailleur: Pick<Bailleur, "nom"> };
    allocation: Pick<AllocationLignePartenaire, "id" | "referenceConvention"> & {
        partenaire: Pick<Partenaire, "id" | "nom" | "typePartenaire">;
    };
    projet: Pick<Projet, "id" | "nom" | "description"> & {
        clientBeneficiaire: Pick<ClientBeneficiaire, "id" | "nomOuRaisonSociale" | "typeClient" | "identifiantUnique">;
        secteurActivite: Pick<SecteurActivite, "id" | "nom">;
    };
    utilisateurCreation?: { nomUtilisateur: string; nom: string; prenom: string };
    utilisateurModification?: { nomUtilisateur: string; nom: string; prenom: string };
};

export default function GarantieDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { data: session, status: sessionStatus } = useSession();
  const garantieId = params.id as string;

  const [garantieDetails, setGarantieDetails] = useState<GarantieDetailsType | null>(null);
  const [pageStatus, setPageStatus] = useState<"loading" | "loaded" | "error" | "unauthorized">("loading");

  const fetchGarantieDetails = useCallback(async () => {
    const controller = new AbortController();
    if (!garantieId) return controller;
    if (pageStatus !== "loading") setPageStatus("loading");

    try {
      const response = await fetch(`/api/garanties/${garantieId}`, { signal: controller.signal });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Garantie non trouvée ou erreur serveur." }));
        throw new Error(errorData.message);
      }
      const data = await response.json();
      setGarantieDetails(data);
      setPageStatus("loaded");
    } catch (error: any) {
      if (error.name === "AbortError") return; // Ignore aborts
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      setPageStatus("error");
    }
    return controller;
  }, [garantieId, toast]);

  useEffect(() => {
    if (sessionStatus === "loading") { setPageStatus("loading"); return; }
    if (!session) { router.replace(`/auth/connexion?callbackUrl=/garanties/${garantieId}`); return; }

    const userRole = session.user?.role as RoleUtilisateur;
    const authorizedRoles: RoleUtilisateur[] = [RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar, RoleUtilisateur.AnalysteFinancier, RoleUtilisateur.Partenaire];
    if (!authorizedRoles.includes(userRole)) {
      toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/"); setPageStatus("unauthorized"); return;
    }

    let controller: AbortController | undefined;
    if (garantieId && (pageStatus === "loading" || pageStatus === "error")) {
      const fetchPromise = fetchGarantieDetails();
      if (fetchPromise instanceof Promise) {
        fetchPromise.then(ctrl => { controller = ctrl; });
      }
    }
    return () => {
      if (controller) controller.abort();
    };
  }, [sessionStatus, session, garantieId, fetchGarantieDetails, router, toast, pageStatus]);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleOpenEditModal = () => {
    setIsEditModalOpen(true);
  };
  const handleOpenDemandeMainleveeModal = () => { toast({title: "TODO: Demander Mainlevée"}); };
  const handleOpenDemandeMiseEnJeuModal = () => { toast({title: "TODO: Demander Mise en Jeu"}); };

  if (pageStatus === "loading") return <div className="p-6 text-center">Chargement des détails de la garantie...</div>;
  if (pageStatus === "unauthorized") return <div className="p-6 text-center">Accès non autorisé.</div>;
  if (pageStatus === "error" || !garantieDetails) {
    return (
        <div className="container mx-auto py-8 px-4 text-center">
            <p className="text-xl text-red-600 mb-4">Impossible de charger les détails ou garantie non trouvée.</p>
            <Button variant="outline" asChild>
                <Link href="/garanties"><ArrowLeft className="mr-2 h-4 w-4" /> Retour à la liste</Link>
            </Button>
        </div>
    );
  }

  const canModify = ([RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar] as RoleUtilisateur[]).includes(session?.user?.role as RoleUtilisateur);
  const canRequestMainlevee = (canModify || session?.user?.role === RoleUtilisateur.Partenaire) &&
                             ([StatutGarantie.Active, StatutGarantie.EnSouffrance, StatutGarantie.Echue] as StatutGarantie[]).includes(garantieDetails.statut) &&
                             garantieDetails.statut !== StatutGarantie.MainleveeDemandee && garantieDetails.statut !== StatutGarantie.MainleveeAccordee;
  const canRequestMiseEnJeu = (canModify || session?.user?.role === RoleUtilisateur.Partenaire) &&
                             ([StatutGarantie.Active, StatutGarantie.EnSouffrance] as StatutGarantie[]).includes(garantieDetails.statut) &&
                             garantieDetails.statut !== StatutGarantie.MiseEnJeuDemandee;

  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      <div className="flex justify-between items-center">
        <Button variant="outline" asChild>
          <Link href="/garanties"><ArrowLeft className="mr-2 h-4 w-4" /> Retour à la liste des garanties</Link>
        </Button>
        <div className="flex space-x-2">
            {canModify && <Button onClick={handleOpenEditModal}><Edit className="mr-2 h-4 w-4"/> Modifier / Gérer Statut</Button>}
            {canRequestMainlevee && <Button variant="secondary" onClick={handleOpenDemandeMainleveeModal}><Handshake className="mr-2 h-4 w-4"/> Demander Mainlevée</Button>}
            {canRequestMiseEnJeu && <Button variant="destructive" onClick={handleOpenDemandeMiseEnJeuModal}><AlertOctagon className="mr-2 h-4 w-4"/> Demander Mise en Jeu</Button>}
        </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader className="bg-slate-50 dark:bg-slate-800 rounded-t-lg">
          <div className="flex justify-between items-start">
            <div>
                <CardTitle className="text-2xl">Garantie: {garantieDetails.referenceGarantie}</CardTitle>
                <CardDescription>Type: {garantieDetails.typeGarantie} - Statut: <Badge variant={getStatutGarantieBadgeVariant(garantieDetails.statut)}>{garantieDetails.statut}</Badge></CardDescription>
            </div>
            <div className="text-right">
                <p className="text-sm text-muted-foreground">Créée le: {formatDate(garantieDetails.dateCreation)}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          <section>
            <h3 className="text-lg font-semibold mb-3 border-b pb-2">Informations Générales</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
              <div><strong>Identifiant Crédit Partenaire:</strong> {garantieDetails.identifiantCreditPartenaire}</div>
              <div><strong>Montant du Crédit:</strong> {formatCurrency(garantieDetails.montantCredit, garantieDetails.ligneGarantie.devise)}</div>
              <div><strong>Taux Couverture Appliqué:</strong> {garantieDetails.tauxCouvertureApplique?.toString() || "-"}%</div>
              <div><strong>Montant Garanti:</strong> {formatCurrency(garantieDetails.montantGarantie, garantieDetails.ligneGarantie.devise)}</div>
              <div className="lg:col-span-2"><strong>Conditions Particulières:</strong> <p className="text-muted-foreground whitespace-pre-wrap">{garantieDetails.conditionsParticulieres || "-"}</p></div>
            </div>
          </section>
          <Separator />
          <section>
            <h3 className="text-lg font-semibold mb-3 border-b pb-2">Dates Clés</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
              <div><strong>Octroi Crédit:</strong> {formatDate(garantieDetails.dateOctroiCredit)}</div>
              <div><strong>Demande Garantie:</strong> {formatDate(garantieDetails.dateDemandeGarantie)}</div>
              <div><strong>Accord Garantie:</strong> {formatDate(garantieDetails.dateAccordGarantie)}</div>
              <div><strong>Effet Garantie:</strong> {formatDate(garantieDetails.dateEffetGarantie)}</div>
              <div><strong>Échéance Initiale Crédit:</strong> {formatDate(garantieDetails.dateEcheanceInitialeCredit)}</div>
              <div><strong>Échéance Garantie:</strong> {formatDate(garantieDetails.dateEcheanceGarantie)}</div>
            </div>
          </section>
          <Separator />
          <section>
            <h3 className="text-lg font-semibold mb-3 border-b pb-2">Acteurs Impliqués</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
              {/* TODO: Créer la page de détail /configuration/clients-beneficiaires/[id] */}
              <div><strong>Client Bénéficiaire:</strong> <span className="font-medium text-gray-700 dark:text-gray-300" title="Page de détail non disponible">{garantieDetails.projet.clientBeneficiaire.nomOuRaisonSociale}</span> ({getTypeClientLabel(garantieDetails.projet.clientBeneficiaire.typeClient)})</div>
              {/* TODO: Créer la page de détail /configuration/projets/[id] */}
              <div><strong>Projet:</strong> <span className="font-medium text-gray-700 dark:text-gray-300" title="Page de détail du projet non disponible">{garantieDetails.projet.nom}</span></div>
              <div><strong>Secteur d'Activité (Projet):</strong> {garantieDetails.projet.secteurActivite.nom}</div>
              {/* TODO: Créer la page de détail /configuration/partenaires/[id] */}
              <div><strong>Partenaire Financier:</strong> <span className="font-medium text-gray-700 dark:text-gray-300" title="Page de détail du partenaire non disponible">{garantieDetails.allocation.partenaire.nom}</span> ({getTypePartenaireLabel(garantieDetails.allocation.partenaire.typePartenaire)})</div>
              <div><strong>Allocation Utilisée:</strong> <Link href={`/allocations/${garantieDetails.allocation.id}`} className="text-blue-600 hover:underline">ID {garantieDetails.allocation.id}</Link> (Réf: {garantieDetails.allocation.referenceConvention || "-"})</div>
              <div><strong>Ligne de Garantie Source:</strong> <Link href={`/lignes-garantie/${garantieDetails.ligneGarantie.id}`} className="text-blue-600 hover:underline">{garantieDetails.ligneGarantie.nom}</Link></div>
              <div><strong>Bailleur de Fonds (Ligne):</strong> {garantieDetails.ligneGarantie.bailleur.nom}</div>
            </div>
          </section>
          <Separator />
          {(garantieDetails.dateDernierRemboursementClient || garantieDetails.montantRestantDuCredit || garantieDetails.nombreEcheancesImpayees) && (
            <section>
              <h3 className="text-lg font-semibold mb-3 border-b pb-2">Suivi du Crédit</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
                <div><strong>Date Dernier Remb.:</strong> {formatDate(garantieDetails.dateDernierRemboursementClient)}</div>
                <div><strong>Montant Restant Dû:</strong> {formatCurrency(garantieDetails.montantRestantDuCredit, garantieDetails.ligneGarantie.devise)}</div>
                <div><strong>Nb. Échéances Impayées:</strong> {garantieDetails.nombreEcheancesImpayees ?? "-"}</div>
                <div><strong>Délai Mise en Jeu (jours):</strong> {garantieDetails.delaiMiseEnJeu}</div>
              </div>
            </section>
          )}
        </CardContent>
      </Card>

      {/* Dialog de modification */}
      <GarantieEditDialog
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        garantie={garantieDetails}
        onSuccess={() => {
          setIsEditModalOpen(false);
          fetchGarantieDetails(); // Rafraîchir les données
        }}
      />
    </div>
  );
}

// Composant Dialog de modification
interface GarantieEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  garantie: GarantieDetailsType;
  onSuccess: () => void;
}

function GarantieEditDialog({ isOpen, onClose, garantie, onSuccess }: GarantieEditDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<UpdateGarantieFormValues>({
    resolver: zodResolver(UpdateGarantieSchema),
    defaultValues: {
      statut: garantie.statut,
      identifiantCreditPartenaire: garantie.identifiantCreditPartenaire || "",
      conditionsParticulieres: garantie.conditionsParticulieres || "",
      delaiMiseEnJeu: garantie.delaiMiseEnJeu?.toString() || "",
      dateEcheanceGarantie: garantie.dateEcheanceGarantie ? new Date(garantie.dateEcheanceGarantie) : undefined,
      dateAccordGarantie: garantie.dateAccordGarantie ? new Date(garantie.dateAccordGarantie) : undefined,
      dateEffetGarantie: garantie.dateEffetGarantie ? new Date(garantie.dateEffetGarantie) : undefined,
      dateDernierRemboursementClient: garantie.dateDernierRemboursementClient ? new Date(garantie.dateDernierRemboursementClient) : undefined,
      montantRestantDuCreditStr: garantie.montantRestantDuCredit?.toString() || "",
      nombreEcheancesImpayeesStr: garantie.nombreEcheancesImpayees?.toString() || "",
    },
  });

  // Réinitialiser le formulaire quand la garantie change
  useEffect(() => {
    if (garantie) {
      form.reset({
        statut: garantie.statut,
        identifiantCreditPartenaire: garantie.identifiantCreditPartenaire || "",
        conditionsParticulieres: garantie.conditionsParticulieres || "",
        delaiMiseEnJeu: garantie.delaiMiseEnJeu?.toString() || "",
        dateEcheanceGarantie: garantie.dateEcheanceGarantie ? new Date(garantie.dateEcheanceGarantie) : undefined,
        dateAccordGarantie: garantie.dateAccordGarantie ? new Date(garantie.dateAccordGarantie) : undefined,
        dateEffetGarantie: garantie.dateEffetGarantie ? new Date(garantie.dateEffetGarantie) : undefined,
        dateDernierRemboursementClient: garantie.dateDernierRemboursementClient ? new Date(garantie.dateDernierRemboursementClient) : undefined,
        montantRestantDuCreditStr: garantie.montantRestantDuCredit?.toString() || "",
        nombreEcheancesImpayeesStr: garantie.nombreEcheancesImpayees?.toString() || "",
      });
    }
  }, [garantie, form]);

  // Définir les transitions de statut autorisées
  const getAuthorizedStatuses = (currentStatus: StatutGarantie): StatutGarantie[] => {
    const transitions: Partial<Record<StatutGarantie, StatutGarantie[]>> = {
      [StatutGarantie.EnInstruction]: [StatutGarantie.Echue, StatutGarantie.Active, StatutGarantie.Supprimee],
      [StatutGarantie.Echue]: [StatutGarantie.Active, StatutGarantie.MainleveeDemandee, StatutGarantie.Supprimee],
      [StatutGarantie.Active]: [StatutGarantie.EnSouffrance, StatutGarantie.MainleveeDemandee, StatutGarantie.MiseEnJeuDemandee, StatutGarantie.MainleveeAccordee],
      [StatutGarantie.EnSouffrance]: [StatutGarantie.Active, StatutGarantie.MiseEnJeuDemandee, StatutGarantie.MainleveeDemandee],
      [StatutGarantie.MainleveeDemandee]: [StatutGarantie.MainleveeAccordee, StatutGarantie.MainleveeRefusee],
      [StatutGarantie.MainleveeRefusee]: [StatutGarantie.Active, StatutGarantie.EnSouffrance, StatutGarantie.MainleveeDemandee],
      [StatutGarantie.MiseEnJeuDemandee]: [StatutGarantie.MiseEnJeuAcceptee, StatutGarantie.MiseEnJeuRefusee],
      [StatutGarantie.MiseEnJeuAcceptee]: [StatutGarantie.MiseEnJeuPayee],
      [StatutGarantie.MiseEnJeuRefusee]: [StatutGarantie.Active, StatutGarantie.EnSouffrance],
      [StatutGarantie.MiseEnJeuPayee]: [], // Statut final
      [StatutGarantie.MainleveeAccordee]: [], // Statut final
      [StatutGarantie.Transferree]: [StatutGarantie.Active, StatutGarantie.EnSouffrance],
      [StatutGarantie.ClotureeAnormalement]: [], // Statut final
      [StatutGarantie.Radiee]: [], // Statut final
      [StatutGarantie.Supprimee]: [], // Statut final
    };

    return [currentStatus, ...(transitions[currentStatus] || [])];
  };

  const authorizedStatuses = getAuthorizedStatuses(garantie.statut);

  const onSubmit = async (data: UpdateGarantieFormValues) => {
    setIsSubmitting(true);
    try {
      const requestBody = {
        ...data,
        // Garder les champs string comme attendu par le schéma
        // delaiMiseEnJeu reste en string car le schéma l'attend en string
        // montantRestantDuCreditStr et nombreEcheancesImpayeesStr restent en string
        // Dates converties en ISO string
        dateEcheanceGarantie: data.dateEcheanceGarantie?.toISOString(),
        dateAccordGarantie: data.dateAccordGarantie?.toISOString(),
        dateEffetGarantie: data.dateEffetGarantie?.toISOString(),
        dateDernierRemboursementClient: data.dateDernierRemboursementClient?.toISOString(),
      };
      
      const response = await fetch(`/api/garanties/${garantie.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Erreur lors de la modification" }));
        throw new Error(errorData.message);
      }

      toast({
        title: "Succès",
        description: "La garantie a été modifiée avec succès",
      });

      onSuccess();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatutLabel = (statut: StatutGarantie): string => {
    const labels: Partial<Record<StatutGarantie, string>> = {
      [StatutGarantie.EnInstruction]: "En Instruction",
      [StatutGarantie.Echue]: "Échue",
      [StatutGarantie.Active]: "Active",
      [StatutGarantie.EnSouffrance]: "En Souffrance",
      [StatutGarantie.MainleveeDemandee]: "Mainlevée Demandée",
      [StatutGarantie.MainleveeAccordee]: "Mainlevée Accordée",
      [StatutGarantie.MainleveeRefusee]: "Mainlevée Refusée",
      [StatutGarantie.MiseEnJeuDemandee]: "Mise en Jeu Demandée",
      [StatutGarantie.MiseEnJeuAcceptee]: "Mise en Jeu Acceptée",
      [StatutGarantie.MiseEnJeuPayee]: "Mise en Jeu Payée",
      [StatutGarantie.MiseEnJeuRefusee]: "Mise en Jeu Refusée",
      [StatutGarantie.Transferree]: "Transférée",
      [StatutGarantie.ClotureeAnormalement]: "Clôturée Anormalement",
      [StatutGarantie.Radiee]: "Radiée",
      [StatutGarantie.Supprimee]: "Supprimée",
    };
    return labels[statut] || statut;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Modifier la Garantie</DialogTitle>
          <DialogDescription>
            Référence: {garantie.referenceGarantie} - Modifiez les informations et le statut de la garantie
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Section Statut */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Gestion du Statut</h3>
              <FormField
                control={form.control}
                name="statut"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {authorizedStatuses.map((statut) => (
                          <SelectItem key={statut} value={statut}>
                            {getStatutLabel(statut)}
                            {statut === garantie.statut && " (Actuel)"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Section Informations Générales */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Informations Générales</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="identifiantCreditPartenaire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Identifiant Crédit Partenaire</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Identifiant du crédit" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="delaiMiseEnJeu"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Délai Mise en Jeu (jours)</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" placeholder="Nombre de jours" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="conditionsParticulieres"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conditions Particulières</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Conditions particulières de la garantie"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Section Dates */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Dates</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="dateAccordGarantie"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Accord Garantie</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value || undefined}
                          onDateChange={field.onChange}
                          placeholder="Choisir une date"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateEffetGarantie"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Effet Garantie</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value || undefined}
                          onDateChange={field.onChange}
                          placeholder="Choisir une date"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateEcheanceGarantie"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Échéance Garantie</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value || undefined}
                          onDateChange={field.onChange}
                          placeholder="Choisir une date"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Section Suivi du Crédit */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Suivi du Crédit</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="dateDernierRemboursementClient"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Dernier Remboursement</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value || undefined}
                          onDateChange={field.onChange}
                          placeholder="Choisir une date"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nombreEcheancesImpayeesStr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nombre Échéances Impayées</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" placeholder="0" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="montantRestantDuCreditStr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Montant Restant Dû ({garantie.ligneGarantie.devise})</FormLabel>
                    <FormControl>
                      <Input {...field} type="number" step="0.01" placeholder="0.00" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Annuler
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? "Modification..." : "Modifier"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}