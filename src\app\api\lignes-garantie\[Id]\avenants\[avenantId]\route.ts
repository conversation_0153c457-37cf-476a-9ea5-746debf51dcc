// src/app/api/lignes-garantie/[Id]/avenants/[avenantId]/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import Decimal from "decimal.js";
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { Prisma, TypeAvenant as AvenantType } from '@prisma/client';
import { AvenantUpdateSchema } from '@/lib/schemas/avenant-ligne-garantie.schema';
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET /api/lignes-garantie/[Id]/avenants/[avenantId]
// Récupérer un avenant spécifique
export async function GET(request: Request, { params }: { params: { Id: string, avenantId: string } }) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
  }

  const Id = parseInt(resolvedParams.Id);
  const avenantId = parseInt(resolvedParams.avenantId);

  if (isNaN(Id) || isNaN(avenantId)) {
    return NextResponse.json({ error: 'ID de ligne de garantie ou d\'avenant invalide' }, { status: 400 });
  }

  try {
    const avenant = await prisma.avenantLigneGarantie.findFirst({
      where: { id: avenantId, ligneGarantieId: Id },
     });

    if (!avenant) {
      return NextResponse.json({ error: 'Avenant non trouvé' }, { status: 404 });
    }
    return NextResponse.json(avenant);
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'avenant:', error);
    return NextResponse.json({ error: 'Erreur serveur lors de la récupération de l\'avenant' }, { status: 500 });
  }
}

// PUT /api/lignes-garantie/[Id]/avenants/[avenantId]
// Mettre à jour un avenant spécifique

function validateTypeAvenant(value: string): AvenantType {
  if (!Object.values(AvenantType).includes(value as AvenantType)) {
    throw new Error(`Invalid typeAvenant value: ${value}`);
  }
  return value as AvenantType;
}
export async function PUT(request: Request, { params }: { params: { Id: string, avenantId: string } }) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !['Administrateur', 'GestionnaireGesGar', 'AnalysteFinancier'].includes(session.user?.role as string)) {
    return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
  }

  const Id = parseInt(resolvedParams.Id);
  const avenantId = parseInt(resolvedParams.avenantId);

  if (isNaN(Id) || isNaN(avenantId)) {
    return NextResponse.json({ error: 'ID de ligne de garantie ou d\'avenant invalide' }, { status: 400 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip');
  const userAgentHeader = headersList.get('user-agent');
  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;

  return auditContext.run({ userId: modifierId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const parsed = AvenantUpdateSchema.safeParse(body);
      if (!parsed.success) {
        return NextResponse.json(
          { error: 'Payload invalide', details: parsed.error.flatten().fieldErrors },
          { status: 400 },
        );
      }
      const { typeAvenant, montantModification, dateAvenant, raison, referenceDocument, nouvelleDateExpiration, ...rest } = parsed.data;

      // Validation de base
      if (!typeAvenant || !dateAvenant || !raison) {
        return NextResponse.json({ error: 'Champs requis manquants' }, { status: 400 });
      }

      const utilisateurId = modifierId;

    // Récupérer l'avenant existant pour comparer les changements si nécessaire pour la logique de la ligne de garantie
    const avenantExistant = await prisma.avenantLigneGarantie.findFirst({
      where: { id: avenantId },
    });

    if (!avenantExistant) {
      return NextResponse.json({ error: 'Avenant non trouvé pour la mise à jour' }, { status: 404 });
    }

    // Vérif cohérence entre l'avenant et la ligne de garantie
    if (avenantExistant.ligneGarantieId !== Id) {
      return NextResponse.json({ error: "Incohérence entre l'avenant et la ligne de garantie (ownership)" }, { status: 400 });
    }

    // Regrouper la mise à jour de l'avenant et de la ligne de garantie dans une transaction atomique
    const updatedAvenant = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // 1. Mettre à jour l'avenant
      const avenantMaj = await tx.avenantLigneGarantie.update({
        where: { id: avenantId },
        data: {
          typeAvenant: validateTypeAvenant(typeAvenant),
          // Only keep one 'montantModification' property
          montantModification: montantModification === undefined
   ? undefined // ← leaves the DB value unchanged
   : new Decimal(montantModification).toString(),
          nouvelleDateExpiration: nouvelleDateExpiration ? new Date(nouvelleDateExpiration) : null,
          raison,
          referenceDocument,
          utilisateurModificationId: utilisateurId,
        },
      });

      // 2. Mettre à jour la ligne de garantie
      const ligneGarantie = await tx.ligneGarantie.findUnique({ where: { id: Id } });
      if (ligneGarantie) {
        // Ownership check (redondant mais sécurité)
        if (avenantExistant.ligneGarantieId !== Id) {
          throw new Error("Incohérence entre l'avenant et la ligne de garantie (ownership)");
        }
        const updatedLigneData = {} as any;
        // Annuler l'effet de l'ancien montant si applicable
        if (avenantExistant.typeAvenant === 'AUGMENTATION_MONTANT' && avenantExistant.montantModification) {
          const oldModif = new Decimal(avenantExistant.montantModification.toString());
          updatedLigneData.montantInitial = new Decimal(Number(ligneGarantie.montantInitial)).minus(oldModif).toNumber();
          updatedLigneData.montantDisponible = new Decimal(Number(ligneGarantie.montantDisponible)).minus(oldModif).toNumber();
        } else if (avenantExistant.typeAvenant === 'REDUCTION_MONTANT' && avenantExistant.montantModification) {
          const oldModif = new Decimal(avenantExistant.montantModification.toString());
          updatedLigneData.montantInitial = new Decimal(Number(ligneGarantie.montantInitial)).plus(oldModif).toNumber();
          updatedLigneData.montantDisponible = new Decimal(Number(ligneGarantie.montantDisponible)).plus(oldModif).toNumber();
        }

        // Appliquer le nouvel effet
        if (typeAvenant === 'AUGMENTATION_MONTANT' && montantModification) {
          const newModif = new Decimal(montantModification);
          const baseInitial = updatedLigneData.montantInitial !== undefined ? new Decimal(Number(updatedLigneData.montantInitial)) : new Decimal(Number(ligneGarantie.montantInitial));
          const baseDispo = updatedLigneData.montantDisponible !== undefined ? new Decimal(Number(updatedLigneData.montantDisponible)) : new Decimal(Number(ligneGarantie.montantDisponible));
          updatedLigneData.montantInitial = baseInitial.plus(newModif).toNumber();
          updatedLigneData.montantDisponible = baseDispo.plus(newModif).toNumber();
        } else if (typeAvenant === 'REDUCTION_MONTANT' && montantModification) {
          const newModif = new Decimal(montantModification);
          const baseInitial = updatedLigneData.montantInitial !== undefined ? new Decimal(Number(updatedLigneData.montantInitial)) : new Decimal(Number(ligneGarantie.montantInitial));
          const baseDispo = updatedLigneData.montantDisponible !== undefined ? new Decimal(Number(updatedLigneData.montantDisponible)) : new Decimal(Number(ligneGarantie.montantDisponible));
          updatedLigneData.montantInitial = baseInitial.minus(newModif).toNumber();
          updatedLigneData.montantDisponible = baseDispo.minus(newModif).toNumber();
        }

        if (typeAvenant === 'PROLONGATION_DUREE' && nouvelleDateExpiration) {
          updatedLigneData.dateExpiration = new Date(nouvelleDateExpiration);
        } else if (avenantExistant.typeAvenant === 'PROLONGATION_DUREE' && typeAvenant !== 'PROLONGATION_DUREE') {
          // Cas complexe non géré ici
        }

        if (Object.keys(updatedLigneData).length > 0) {
          await tx.ligneGarantie.update({
            where: { id: Id },
            data: updatedLigneData,
          });
        }
      }
      return avenantMaj;
    });

      return NextResponse.json(updatedAvenant);
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour de l\'avenant:', error);
      // Error handling: use correct PrismaClientKnownRequestError import
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          return NextResponse.json({ error: 'Avenant non trouvé pour la mise à jour.' }, { status: 404 });
        }
      }
      return NextResponse.json({ error: 'Erreur serveur lors de la mise à jour de l\'avenant', details: error.message }, { status: 500 });
    }
  });
}

// DELETE /api/lignes-garantie/[Id]/avenants/[avenantId]
// Supprimer un avenant spécifique
export async function DELETE(request: Request, { params }: { params: { Id: string, avenantId: string } }) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !['Administrateur', 'GestionnaireGesGar', 'AnalysteFinancier'].includes(session.user?.role as string)) {
    return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
  }

  const Id = parseInt(resolvedParams.Id);
  const avenantId = parseInt(resolvedParams.avenantId);

  if (isNaN(Id) || isNaN(avenantId)) {
    return NextResponse.json({ error: 'ID de ligne de garantie ou d\'avenant invalide' }, { status: 400 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip');
  const userAgentHeader = headersList.get('user-agent');
  const deleterId = session.user?.id ? parseInt(session.user.id) : undefined;

  return auditContext.run({ userId: deleterId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      // Récupérer l'avenant avant suppression pour ajuster la ligne de garantie
      const avenantASupprimer = await prisma.avenantLigneGarantie.findUnique({
        where: { id: avenantId },
      });

      if (!avenantASupprimer) {
        return NextResponse.json({ error: 'Avenant non trouvé pour la suppression' }, { status: 404 });
      }

      // Guard ownership avant transaction
      if (avenantASupprimer.ligneGarantieId !== Id) {
        return NextResponse.json({ error: "Incohérence entre l'avenant et la ligne de garantie (ownership)" }, { status: 400 });
      }

    // Regrouper la suppression de l'avenant et la mise à jour de la ligne de garantie dans une transaction atomique
    await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      await tx.avenantLigneGarantie.delete({
        where: { id: avenantId },
      });

      // TODO: Implémenter la logique d'annulation de l'effet de l'avenant sur la ligne de garantie.
      // Similaire à la mise à jour, cela peut être complexe.
      // Il faut annuler l'impact de cet avenant (ex: si c'était une augmentation, il faut la réduire).
      const ligneGarantie = await tx.ligneGarantie.findUnique({ where: { id: avenantASupprimer.ligneGarantieId } });
      if (ligneGarantie && avenantASupprimer) {
        // Ownership check (redondant mais sécurité)
        if (avenantASupprimer.ligneGarantieId !== Id) {
          throw new Error("Incohérence entre l'avenant et la ligne de garantie (ownership)");
        }
        const updatedLigneData: { montantInitial?: number, montantDisponible?: number } = {};
        if (avenantASupprimer.typeAvenant === 'AUGMENTATION_MONTANT' && avenantASupprimer.montantModification) {
          const oldModif = new Decimal(avenantASupprimer.montantModification.toString());
          updatedLigneData.montantInitial = new Decimal(Number(ligneGarantie.montantInitial)).minus(oldModif).toNumber();
          updatedLigneData.montantDisponible = new Decimal(Number(ligneGarantie.montantDisponible)).minus(oldModif).toNumber();
        } else if (avenantASupprimer.typeAvenant === 'REDUCTION_MONTANT' && avenantASupprimer.montantModification) {
          const oldModif = new Decimal(avenantASupprimer.montantModification.toString());
          updatedLigneData.montantInitial = new Decimal(Number(ligneGarantie.montantInitial)).plus(oldModif).toNumber();
          updatedLigneData.montantDisponible = new Decimal(Number(ligneGarantie.montantDisponible)).plus(oldModif).toNumber();
        }
        // Pour la date d'expiration, la logique d'annulation est plus complexe et dépendrait de l'historique des avenants.
        // Une approche simplifiée n'est pas idéale ici. Il faudrait recalculer la date d'expiration en ignorant cet avenant.
        // Pour l'instant, on ne modifie pas la date d'expiration lors de la suppression d'un avenant de prolongation.

        if (Object.keys(updatedLigneData).length > 0) {
          // S'assurer que les montants ne deviennent pas négatifs
          if (
            updatedLigneData.montantInitial !== undefined &&
            Number(updatedLigneData.montantInitial) < 0
          ) {
            updatedLigneData.montantInitial = 0;
          }
          if (
            updatedLigneData.montantDisponible !== undefined &&
            Number(updatedLigneData.montantDisponible) < 0
          ) {
            updatedLigneData.montantDisponible = 0;
          }
          await tx.ligneGarantie.update({
            where: { id: avenantASupprimer.ligneGarantieId },
            data: updatedLigneData,
          });
        }
      }
    });

      return NextResponse.json({ message: 'Avenant supprimé avec succès' }, { status: 200 });
    } catch (error: any) {
      console.error('Erreur lors de la suppression de l\'avenant:', error);
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          return NextResponse.json({ error: 'Avenant non trouvé pour la suppression.' }, { status: 404 });
        }
      }
      return NextResponse.json({ error: 'Erreur serveur lors de la suppression de l\'avenant', details: error.message }, { status: 500 });
    }
  });
}