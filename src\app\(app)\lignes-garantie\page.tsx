// src/app/(app)/lignes-garantie/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { getLigneGarantieColumns, LigneGarantieColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { ErrorDialog } from "@/components/ui/error-dialog";
import { LigneGarantieFormValues, LigneGarantieSchema } from "@/lib/schemas/ligne-garantie.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { AlertDialog, /* ... */ } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";
import type { SubmitHandler } from "react-hook-form";
import { formatNumberWithThousandsSeparator } from "@/lib/utils";
import { StatutLigneGarantie } from "@/types/statut-ligne-garantie";

type SelectOption = { value: string; label: string };
const statutLigneOptions: SelectOption[] = Object.values(StatutLigneGarantie).map((val) => ({
  value: val,
  label: val.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase()).trim()
}));

function getAutresInfos(autresInformations: any): Record<string, any> {
  if (!autresInformations) return {};
  if (typeof autresInformations === 'string') {
    try { return JSON.parse(autresInformations); } catch { return {}; }
  }
  return autresInformations;
}

export default function LignesGarantiePage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [lignes, setLignes] = useState<LigneGarantieColumn[]>([]);
  const [bailleursOptions, setBailleursOptions] = useState<SelectOption[]>([]);
  const [pageStatus, setPageStatus] = useState<'loading'|'loaded'|'error'|'unauthorized'>('loading');
  const [dataFetched, setDataFetched] = useState(false); // Added to prevent fetch loop
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingLigne, setEditingLigne] = useState<LigneGarantieColumn | null>(null);
  const [ligneToDelete, setLigneToDelete] = useState<LigneGarantieColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedLigne, setSelectedLigne] = useState<LigneGarantieColumn | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [filterStatut, setFilterStatut] = useState<string>("");
  const [filterBailleur, setFilterBailleur] = useState<string>("");
  const [filterText, setFilterText] = useState<string>("");
  
  // État pour le popup d'erreur
  const [errorDialog, setErrorDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    type: "error" | "warning" | "info";
  }>({
    open: false,
    title: "",
    description: "",
    type: "error"
  });

  const form = useForm({
    resolver: zodResolver(LigneGarantieSchema),
    defaultValues: {
      nom: "",
      bailleurId: "",
      referenceConvention: "",
      description: "",
      montantInitialStr: "",
      dateOuverture: new Date(),
      dateExpiration: new Date(),
      devise: "XOF",
      statut: StatutLigneGarantie.EnAttenteValidation,
      autreInfoObjectifLigne: "",
      autreInfoConditionsSpecifiques: "",
      autreInfoIndicateurPerf: "",
    },
  });

  // fetch groupé
  const fetchDataForPage = useCallback(async () => {
    setPageStatus("loading");
    try {
      const bailleursRes = await fetch("/api/configuration/bailleurs");
      if (!bailleursRes.ok) {
        const errorText = await bailleursRes.text();
        console.error("API Bailleurs Erreur:", bailleursRes.status, errorText);
        
        // Afficher un popup d'erreur détaillé pour l'erreur 403
        if (bailleursRes.status === 403) {
          let errorMessage = "Accès non autorisé à l'API des bailleurs";
          try {
            const errorData = JSON.parse(errorText);
            if (errorData.message) {
              errorMessage = `${errorData.message}`;
            }
          } catch (parseError) {
            errorMessage = errorText || "Accès refusé";
          }
          
          console.log("Affichage du popup d'erreur 403 pour bailleurs:", errorMessage);
          
          // Afficher le popup d'erreur
          setErrorDialog({
            open: true,
            title: "Erreur d'autorisation - API Bailleurs",
            description: `Erreur 403: ${errorMessage}\n\nVeuillez vérifier vos droits d'accès ou contacter l'administrateur système.`,
            type: "error"
          });
          
          // Ne pas lancer l'erreur immédiatement pour permettre au popup de s'afficher
          setPageStatus("error");
          return; // Sortir de la fonction sans continuer
        }
        
        throw new Error("Échec du chargement des bailleurs de fonds.");
      }
      const bailleursData = await bailleursRes.json();
      // Validate the response structure before using it
      if (Array.isArray(bailleursData)) {
        setBailleursOptions(
          bailleursData
            .filter(b => b.id)
            .map(b => ({
              value: b.id.toString(),
              label: b.nom || 'Unknown'
            }))
        );
      } else {
        throw new Error("Invalid bailleurs data structure");
      }

      const lignesRes = await fetch("/api/lignes-garantie");
      if (!lignesRes.ok) {
        console.error("API Lignes Erreur:", lignesRes.status, await lignesRes.text());
        throw new Error("Échec du chargement des lignes de garantie.");
      }
      const lignesData = await lignesRes.json();
      setLignes(lignesData);

      setPageStatus("loaded");
    } catch (error: any) {
      toast({ title: "Erreur de chargement", description: error.message, variant: "destructive" });
      setPageStatus("error");
    }
  }, [toast]);

  useEffect(() => {
    if (sessionStatus === "loading") {
      setPageStatus("loading");
      return;
    }
    if (sessionStatus === "unauthenticated") {
      router.replace("/auth/connexion?callbackUrl=/lignes-garantie");
      return;
    }
    if (sessionStatus === "authenticated") {
      if (!["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(session.user?.role as string)) {
        toast({ title: "Accès refusé", variant: "destructive", description: "Droits insuffisants." });
        router.replace("/");
        setPageStatus("unauthorized");
        return;
      }
      if (!dataFetched) {
        fetchDataForPage();
        setDataFetched(true);
      } else if (pageStatus === 'error') {
        // Données non chargées, l'utilisateur peut utiliser refreshData pour réessayer
      }
    }
  }, [sessionStatus, session, router, toast, fetchDataForPage, dataFetched, pageStatus]); // pageStatus is kept here to react to manual refresh setting it to loading

  const refreshData = useCallback(async () => { await fetchDataForPage(); }, [fetchDataForPage]);

  const handleEdit = useCallback((ligne: LigneGarantieColumn) => {
    setEditingLigne(ligne);
    const autresInfos = ligne.autresInformations as {
      objectif_ligne?: string;
      conditions_specifiques?: string;
      indicateur_performance_cle?: string;
    } | null;
    form.reset({
      nom: ligne.nom,
      bailleurId: ligne.bailleur.id.toString(),
      referenceConvention: ligne.referenceConvention || "",
      description: ligne.description || "",
      montantInitialStr: ligne.montantInitial.toString().replace('.',','),
      dateOuverture: new Date(ligne.dateOuverture),
      dateExpiration: new Date(ligne.dateExpiration),
      devise: ligne.devise,
      statut: ligne.statut,
      autreInfoObjectifLigne: autresInfos?.objectif_ligne || "",
      autreInfoConditionsSpecifiques: autresInfos?.conditions_specifiques || "",
      autreInfoIndicateurPerf: autresInfos?.indicateur_performance_cle || "",
    });
    setIsFormOpen(true);
  }, [form]);

  const handleDeleteConfirm = useCallback((ligne: LigneGarantieColumn) => { /* ... */ }, []);

  const onSubmit: SubmitHandler<LigneGarantieFormValues> = useCallback(async (values) => {
    const isEditing = editingLigne?.id;
    const url = isEditing ? `/api/lignes-garantie/${editingLigne.id}` : "/api/lignes-garantie";
    const method = isEditing ? "PUT" : "POST";
    try {
      const response = await fetch(url, { method, headers: { "Content-Type": "application/json" }, body: JSON.stringify(values) });
      if (response.ok) {
        setIsFormOpen(false);
        await refreshData();
      } else {
        const error = await response.json();
        console.error(error);
        toast({ title: "Erreur", description: error.message || "Erreur inconnue", variant: "destructive" });
        if (error.errors) {
          Object.entries(error.errors).forEach(([field, messages]) => {
            form.setError(field as any, { type: "server", message: (messages as string[]).join(", ") });
          });
          console.error("Erreurs de validation:", error.errors);
        }
      }
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      // Ajoute ce log pour voir l'erreur complète
      console.error("Erreur submit ligne garantie:", error);
    }
  }, [editingLigne, form, toast, refreshData]);

  const columns = useMemo(() => getLigneGarantieColumns({
    onEdit: handleEdit,
    onDelete: handleDeleteConfirm,
    onView: (ligne) => setSelectedLigne(ligne),
  }), [handleEdit, handleDeleteConfirm]);

  // Filtrage local
  const filteredLignes = useMemo(() => {
    return lignes.filter(ligne => {
      const matchStatut = filterStatut ? ligne.statut === filterStatut : true;
      const matchBailleur = filterBailleur ? ligne.bailleur.id.toString() === filterBailleur : true;
      const matchText = filterText
        ? [ligne.nom, ligne.referenceConvention, ligne.description, ligne.bailleur.nom]
            .filter(Boolean)
            .join(" ")
            .toLowerCase()
            .includes(filterText.toLowerCase())
        : true;
      return matchStatut && matchBailleur && matchText;
    });
  }, [lignes, filterStatut, filterBailleur, filterText]);

  // Fonction de test pour déclencher manuellement l'erreur 403
  const testError403 = useCallback(() => {
    console.log("Test manuel de l'erreur 403");
    setErrorDialog({
      open: true,
      title: "Erreur d'autorisation - API Bailleurs (TEST)",
      description: `Erreur 403: Non autorisé\n\nCeci est un test du popup d'erreur. L'erreur 403 réelle se produit lors du chargement des données des bailleurs.\n\nVeuillez vérifier vos droits d'accès ou contacter l'administrateur système.`,
      type: "error"
    });
  }, []);

  if (pageStatus === "loading") {
    return <div className="p-6 text-center">Chargement des lignes de garantie...</div>;
  }
  if (pageStatus === "unauthorized" || pageStatus === "error") {
    return <div className="p-6 text-center">{pageStatus === "unauthorized" ? "Accès non autorisé." : "Erreur lors du chargement des données."}</div>;
  }
  return (
    <div className="container mx-auto py-10 px-2 md:px-0">
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-2">
        <h1 className="text-3xl font-bold">Lignes de Garantie</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => { if(!open){setEditingLigne(null); form.reset();} setIsFormOpen(open);}}>
          <DialogTrigger asChild><Button onClick={() => { form.reset(); setEditingLigne(null); setIsFormOpen(true); }}><PlusCircle /> Ajouter Ligne</Button></DialogTrigger>
          <DialogContent className="sm:max-w-2xl lg:max-w-3xl">
            <DialogHeader>
              <DialogTitle>{editingLigne ? "Modifier" : "Ajouter"} Ligne de Garantie</DialogTitle>
              <DialogDescription>Remplissez les informations pour {editingLigne ? "modifier la" : "ajouter une nouvelle"} ligne de garantie.</DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4 max-h-[80vh] overflow-y-auto pr-3">
                <FormField control={form.control} name="nom" render={({ field }) => ( <FormItem><FormLabel>Nom de la Ligne <span className="text-red-500">*</span></FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem> )}/>
                <FormField control={form.control} name="bailleurId" render={({ field }) => (
                    <FormItem><FormLabel>Bailleur de Fonds <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir un bailleur..." /></SelectTrigger></FormControl>
                            <SelectContent>
                              {bailleursOptions.length > 0 ? (
                                bailleursOptions.filter(opt => opt.value !== '').map(opt => (
                                  <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                                ))
                              ) : (
                                <SelectItem value="no-bailleur" disabled>Aucun bailleur disponible</SelectItem>
                              )}
                            </SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="montantInitialStr" render={({ field }) => ( <FormItem><FormLabel>Montant Initial <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Ex: 1000000000" {...field} /></FormControl><FormDescription>Utiliser le point comme séparateur décimal si besoin.</FormDescription><FormMessage /></FormItem> )}/>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="dateOuverture" render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Date d'Ouverture <span className="text-red-500">*</span></FormLabel>
                          <DatePicker date={field.value} onDateChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                    )} />
                    <FormField control={form.control} name="dateExpiration" render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Date d'Expiration <span className="text-red-500">*</span></FormLabel>
                          <DatePicker date={field.value} onDateChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                    )} />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="devise" render={({ field }) => ( <FormItem><FormLabel>Devise <span className="text-red-500">*</span></FormLabel><FormControl><Input maxLength={3} placeholder="XOF" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="statut" render={({ field }) => (
                        <FormItem><FormLabel>Statut <span className="text-red-500">*</span></FormLabel>
                            <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir un statut..." /></SelectTrigger></FormControl>
                                <SelectContent>
                                  {statutLigneOptions.filter(opt => opt.value !== '').map(opt => (
                                    <SelectItem key={String(opt.value)} value={opt.value}>{opt.label}</SelectItem>
                                  ))}
                                </SelectContent>
                            </Select><FormMessage />
                        </FormItem>
                    )} />
                </div>
                <FormField control={form.control} name="referenceConvention" render={({ field }) => ( <FormItem><FormLabel>Référence Convention</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem> )}/>
                <FormField control={form.control} name="description" render={({ field }) => ( <FormItem><FormLabel>Description</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem> )}/>
                <h3 className="text-lg font-medium border-b pb-2 pt-3">Autres Informations Détaillées</h3>
                <FormField control={form.control} name="autreInfoObjectifLigne" render={({ field }) => (
                  <FormItem><FormLabel>Objectif de la Ligne</FormLabel><FormControl><Input placeholder="Ex: Financement PME secteur agricole" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name="autreInfoConditionsSpecifiques" render={({ field }) => (
                  <FormItem><FormLabel>Conditions Spécifiques</FormLabel><FormControl><Textarea rows={3} placeholder="Toute condition particulière liée à cette ligne..." {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name="autreInfoIndicateurPerf" render={({ field }) => (
                  <FormItem><FormLabel>Indicateur de Performance Clé</FormLabel><FormControl><Input placeholder="Ex: Nombre d'emplois créés" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <DialogFooter className="pt-4"><DialogClose asChild><Button variant="outline">Annuler</Button></DialogClose><Button type="submit" disabled={form.formState.isSubmitting}>Sauvegarder</Button></DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      {/* Filtres */}
      <div className="flex flex-col md:flex-row gap-2 mb-4 w-full">
        <div className="w-full md:w-1/4">
          <Input
            placeholder="Recherche (nom, référence, description, bailleur...)"
            value={filterText}
            onChange={e => setFilterText(e.target.value)}
            className=""
          />
        </div>
        <div className="w-full md:w-1/4">
          <Select value={filterStatut} onValueChange={setFilterStatut}>
            <SelectTrigger><SelectValue placeholder="Filtrer par statut" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">Tous statuts</SelectItem>
              {statutLigneOptions.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/4">
          <Select value={filterBailleur} onValueChange={setFilterBailleur}>
            <SelectTrigger><SelectValue placeholder="Filtrer par bailleur" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">Tous bailleurs</SelectItem>
              {bailleursOptions.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <DataTable
        columns={columns}
        data={filteredLignes}
        pagination={{
          page,
          pageSize,
          onPageChange: setPage,
          totalCount: filteredLignes.length,
        }}
      />
      <Dialog open={!!selectedLigne} onOpenChange={open => { if (!open) setSelectedLigne(null); }}>
        <DialogContent className="sm:max-w-2xl lg:max-w-3xl max-h-[80vh] overflow-y-auto pr-3">
          <DialogHeader>
            <DialogTitle>Détail Ligne de Garantie</DialogTitle>
            <DialogDescription>Consultation des informations détaillées de la ligne de garantie.</DialogDescription>
          </DialogHeader>
          {selectedLigne && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Nom de la Ligne</label>
                  <Input value={selectedLigne.nom} readOnly />
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Bailleur de Fonds</label>
                  <Input value={selectedLigne.bailleur?.nom || "-"} readOnly />
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Montant Initial</label>
                  <Input value={formatNumberWithThousandsSeparator(Number(selectedLigne.montantInitial))} readOnly />
                  <span className="text-xs text-muted-foreground">En {selectedLigne.devise}</span>
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Montant Disponible</label>
                  <Input value={formatNumberWithThousandsSeparator(Number(selectedLigne.montantDisponible))} readOnly />
                  <span className="text-xs text-muted-foreground">En {selectedLigne.devise}</span>
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Date d'Ouverture</label>
                  <Input value={new Date(selectedLigne.dateOuverture).toLocaleDateString("fr-FR") } readOnly />
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Date d'Expiration</label>
                  <Input value={new Date(selectedLigne.dateExpiration).toLocaleDateString("fr-FR") } readOnly />
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Devise</label>
                  <Input value={selectedLigne.devise} readOnly />
                </div>
                <div className="space-y-1">
                  <label className="block text-sm font-medium">Statut</label>
                  <Input value={selectedLigne.statut} readOnly />
                </div>
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium">Référence Convention</label>
                <Input value={selectedLigne.referenceConvention || "-"} readOnly />
              </div>
              <div className="space-y-1">
                <label className="block text-sm font-medium">Description</label>
                <Textarea value={selectedLigne.description || "-"} readOnly rows={2} />
              </div>

              <h3 className="text-lg font-medium border-b pb-2 pt-3">Autres Informations Détaillées</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1 md:col-span-2">
                  <label className="block text-sm font-medium">Objectif de la Ligne</label>
                  <Input value={getAutresInfos(selectedLigne.autresInformations).objectif_ligne || ""} readOnly placeholder="Non renseigné" />
                </div>
                <div className="space-y-1 md:col-span-2">
                  <label className="block text-sm font-medium">Conditions Spécifiques</label>
                  <Textarea value={getAutresInfos(selectedLigne.autresInformations).conditions_specifiques || ""} readOnly rows={2} placeholder="Non renseigné" />
                </div>
                <div className="space-y-1 md:col-span-2">
                  <label className="block text-sm font-medium">Indicateur de Performance Clé</label>
                  <Input value={getAutresInfos(selectedLigne.autresInformations).indicateur_performance_cle || ""} readOnly placeholder="Non renseigné" />
                </div>
              </div>

              <DialogClose asChild>
                <Button variant="outline" className="mt-4">Fermer</Button>
              </DialogClose>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Popup d'erreur pour les erreurs d'API */}
      <ErrorDialog
        open={errorDialog.open}
        onOpenChange={(open) => setErrorDialog(prev => ({ ...prev, open }))}
        title={errorDialog.title}
        description={errorDialog.description}
        type={errorDialog.type}
        actionLabel="Fermer"
      />
      
      {/* TODO: Add AlertDialog for ligneToDelete here if needed */}
    </div>
  );
}