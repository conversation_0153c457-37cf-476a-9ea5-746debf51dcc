import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Adjusted path

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }
  // Optional: Add role-specific checks if notifications are sensitive

  try {
    const notificationsData = [
      {
        id: 1,
        message: "Ligne de garantie 'AFD2022' arrive à expiration dans 15 jours.",
        type: "warning",
        iconName: "AlertTriangle", // Example Lucide icon name
        link: "/garanties/AFD2022",
      },
      {
        id: 2,
        message: "Nouveau contrat 'C00245' a été soumis pour approbation.",
        type: "info",
        iconName: "FileText",
        link: "/contrats/C00245",
      },
      {
        id: 3,
        message: "Paiement en retard pour la facilité 'FAC0098'.",
        type: "error",
        iconName: "CreditCard",
        link: "/facilites/FAC0098/paiements",
      },
      {
        id: 4,
        message: "Rapport mensuel de performance disponible.",
        type: "success",
        iconName: "CheckCircle",
        link: "/rapports/performance/mensuel-juin-2024",
      },
      {
        id: 5,
        message: "Demande de modification pour 'CONTRAT_XYZ' en attente de révision.",
        type: "info",
        iconName: "Edit3",
        link: "/demandes/CONTRAT_XYZ",
      }
    ];
    return NextResponse.json(notificationsData);
  } catch (error) {
    console.error("Error fetching notifications data:", error);
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}
