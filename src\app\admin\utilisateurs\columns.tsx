// src/app/admin/utilisateurs/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Utilisateur, RoleUtilisateur } from "@prisma/client";
import { ArrowUpDown, MoreHorizontal, Eye, EyeOff } from "lucide-react"; // Ajout de Eye, EyeOff
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

// Mettre à jour UserColumn pour inclure estActif
export type UserColumn = Pick<Utilisateur, "id" | "nomUtilisateur" | "email" | "nom" | "prenom" | "role" | "dateCreation" | "estActif">;

// Nouvelle interface pour les props de getColumns
interface GetColumnsProps {
  onEdit: (user: UserColumn) => void; // Pour l'action de modification future
  onDelete: (user: UserColumn) => void; // Pour l'action de suppression
}

// Définir une fonction pour gérer la suppression (sera passée en prop plus tard)
// type HandleDeleteUser = (userId: number) => void;

//export const getColumns = (/*handleDelete: HandleDeleteUser*/): ColumnDef<UserColumn>[] => [
export const getColumns = ({ onDelete, onEdit }: GetColumnsProps): ColumnDef<UserColumn>[] => [
  // ... (colonnes Prénom, Nom, Nom d'utilisateur, Email, Rôle, Date Création restent les mêmes) ...
  // Assurez-vous que la colonne Rôle est toujours là
  {
    accessorKey: "prenom",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Prénom <ArrowUpDown className="ml-2 h-4 w-4" /> </Button> ),
  },
  {
    accessorKey: "nom",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Nom <ArrowUpDown className="ml-2 h-4 w-4" /> </Button> ),
  },
  {
    accessorKey: "nomUtilisateur",
    header: "Nom d'utilisateur",
  },
  {
    accessorKey: "email",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Email <ArrowUpDown className="ml-2 h-4 w-4" /> </Button> ),
  },
  {
    accessorKey: "role",
    header: "Rôle",
    cell: ({ row }) => {
      const role = row.getValue("role") as RoleUtilisateur;
      let variant: "default" | "secondary" | "destructive" | "outline" = "secondary";
      if (role === RoleUtilisateur.Administrateur) variant = "destructive";
      if (role === RoleUtilisateur.GestionnaireGesGar) variant = "default";
      const roleDisplayNames: Record<RoleUtilisateur, string> = { Administrateur: "Administrateur", GestionnaireGesGar: "Gestionnaire GesGar", AnalysteFinancier: "Analyste Financier", Partenaire: "Partenaire", Bailleur: "Bailleur", Auditeur: "Auditeur",};
      return <Badge variant={variant}>{roleDisplayNames[role] || role}</Badge>;
    },
  },
  {
    accessorKey: "dateCreation",
    header: "Date Création",
    cell: ({ row }) => ( <div>{new Date(row.getValue("dateCreation")).toLocaleDateString("fr-FR")}</div>)
  },
  // Optionnel: Colonne pour le statut actif/inactif
  {
    accessorKey: "estActif",
    header: "Statut",
    cell: ({ row }) => {
      const estActif = row.getValue("estActif");
      return estActif ? (
        <Badge variant="default" className="bg-green-500 hover:bg-green-600">
          <Eye className="mr-1 h-3 w-3" /> Actif
        </Badge>
      ) : (
        <Badge variant="destructive">
          <EyeOff className="mr-1 h-3 w-3" /> Inactif
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Ouvrir menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link href={`/admin/utilisateurs/${user.id}/modifier`}>
                Modifier
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(user)}
              className="text-red-600 hover:bg-red-100 focus:bg-red-100 focus:text-red-700"
            >
              Supprimer (Désactiver)
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];