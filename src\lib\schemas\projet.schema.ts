// src/lib/schemas/projet.schema.ts
import { z } from "zod";

// Schéma pour la localisation (peut être affiné)
const LocalisationSchema = z.object({
  region: z.string().optional().or(z.literal('')),
  departement: z.string().optional().or(z.literal('')),
  commune: z.string().optional().or(z.literal('')),
  adressePrecise: z.string().optional().or(z.literal('')),
}).optional();

export const ProjetSchema = z.object({
  nom: z.string().min(3, "Le nom du projet doit contenir au moins 3 caractères.").max(200),
  description: z.string().min(10, "La description doit être d'au moins 10 caractères.").max(5000), // db.Text peut être long
  secteurActiviteId: z.string().min(1, "Veuillez sélectionner un secteur d'activité.") // Sera un ID (string car vient du form)
    .refine(val => !isNaN(parseInt(val)), { message: "ID de secteur d'activité invalide."}),
  clientBeneficiaireId: z.string().min(1, "Veuillez sélectionner un client bénéficiaire.") // Sera un ID
    .refine(val => !isNaN(parseInt(val)), { message: "ID de client bénéficiaire invalide."}),

  // Champs pour Localisation (structurés)
  localisationRegion: z.string().optional().or(z.literal('')),
  localisationDepartement: z.string().optional().or(z.literal('')),
  localisationCommune: z.string().optional().or(z.literal('')),
  localisationAdressePrecise: z.string().optional().or(z.literal('')),

  coutTotalProjetStr: z.string().optional().or(z.literal('')) // Sera converti en Decimal
    .refine(val => val === "" || val === null || val === undefined || !isNaN(parseFloat(val.replace(',', '.'))), {
        message: "Le coût total du projet doit être un nombre valide.",
    }),

  // nouveaux champs pour autres informations
  dateDebut: z.string().optional().or(z.literal('')),
  responsableProjet: z.string().optional().or(z.literal('')),
  emailContact: z.string().optional().or(z.literal('')),

  // autresInformationsStr n'est plus validé comme JSON ici, juste string
  autresInformationsStr: z.string().optional().or(z.literal('')),
});

export type ProjetFormValues = z.infer<typeof ProjetSchema>;