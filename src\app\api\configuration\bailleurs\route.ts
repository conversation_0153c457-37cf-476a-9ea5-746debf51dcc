// src/app/api/configuration/bailleurs/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { BailleurSchema } from "@/lib/schemas/bailleur.schema";
import { headers } from "next/headers";
import { auditContext } from "@/lib/prisma-audit.middleware";

// GET: Lister tous les bailleurs
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  console.log("API Bailleurs GET - Début");
  try {
    const bailleurs = await prisma.bailleur.findMany({
      orderBy: { nom: "asc" },
      include: {
        utilisateurCreation: { select: { nomUtilisateur: true } },
        utilisateurModification: { select: { nomUtilisateur: true } },
      },
    });
    console.log("API Bailleurs GET - Succès");
    return NextResponse.json(bailleurs);
  } catch (error) {
    console.error("API Bailleurs GET - Erreur:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// POST: Créer un nouveau bailleur
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(
      session.user?.role as string
    )
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress =
    headersList.get("x-forwarded-for") || headersList.get("x-real-ip") || null;
  const userAgentHeader = headersList.get("user-agent") || null;

  return auditContext.run(
    {
      userId: creatorId,
      ip: ipAddress ?? undefined,
      userAgent: userAgentHeader ?? undefined,
    },
    async () => {
      try {
        const body = await request.json();
        const validation = BailleurSchema.safeParse(body);

        if (!validation.success) {
          return NextResponse.json(
            {
              message: "Données invalides",
              errors: validation.error.formErrors.fieldErrors,
            },
            { status: 400 }
          );
        }

        const {
        nom,
        description,
        contactNomRepresentant,
        contactEmail,
        contactTelephone,
        autresInfoPaysOrigine, // NOUVEAU
        autresInfoTypeOrganisation, // NOUVEAU
        autresInfoSiteWeb, // NOUVEAU
      } = validation.data;

        const contactJson = {
          nomRepresentant: contactNomRepresentant,
          email: contactEmail,
          telephone: contactTelephone,
        };

        // Assembler l'objet autresInformations
      const autresInformationsJson: any = {}; // Utiliser 'any' temporairement ou un type plus strict
      if (autresInfoPaysOrigine) autresInformationsJson.pays_origine = autresInfoPaysOrigine;
      if (autresInfoTypeOrganisation) autresInformationsJson.type_organisation = autresInfoTypeOrganisation;
      if (autresInfoSiteWeb) autresInformationsJson.site_web = autresInfoSiteWeb;

        // Vérifier si le bailleur existe déjà

        const existingBailleur = await prisma.bailleur.findUnique({
          where: { nom },
        });
        if (existingBailleur) {
          return NextResponse.json(
            { message: "Un bailleur avec ce nom existe déjà." },
            { status: 409 }
          );
        }

        const data: any = {
          nom,
          description,
          contact: contactJson,
          autresInformations: Object.keys(autresInformationsJson).length > 0 ? autresInformationsJson : null,
        };
        if (creatorId !== undefined) {
          data.utilisateurCreationId = creatorId;
        }

        const newBailleur = await prisma.bailleur.create({
          data,
        });
        return NextResponse.json(newBailleur, { status: 201 });
      } catch (error) {
        console.error("Erreur POST /api/configuration/bailleurs:", error);
        return NextResponse.json(
          { message: "Erreur interne du serveur" },
          { status: 500 }
        );
      }
    }
  );
}
