// src/app/api/configuration/projets/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { ProjetSchema } from "@/lib/schemas/projet.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Prisma } from "@prisma/client"; // then use Prisma.Decimal // Pour le type Decimal

// GET: Lister tous les projets
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const projets = await prisma.projet.findMany({
        orderBy: { nom: "asc" },
        include: { // Inclure les noms pour l'affichage
          secteurActivite: { select: { id: true, nom: true } },
          clientBeneficiaire: { select: { id: true, nomOuRaisonSociale: true } },
          utilisateurCreation: { select: { nomUtilisateur: true }},
        }
      });
      return NextResponse.json(projets);
    } catch (error) {
      console.error("Erreur GET /api/configuration/projets:", error);
      return NextResponse.json({
        message: "Erreur interne du serveur",
        error: error instanceof Error ? error.message : (typeof error === 'string' ? error : JSON.stringify(error)),
      }, { status: 500 });
    }
  });
}

// POST: Créer un nouveau projet
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  if (creatorId === undefined || isNaN(creatorId)) {
    return NextResponse.json({ message: "Identifiant utilisateur de création manquant ou invalide." }, { status: 400 });
  }
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: creatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = ProjetSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        nom, description, secteurActiviteId, clientBeneficiaireId,
        localisationRegion, localisationDepartement, localisationCommune, localisationAdressePrecise,
        coutTotalProjetStr, autresInformationsStr
      } = validation.data;

      const secteurId = parseInt(secteurActiviteId);
      const clientId = parseInt(clientBeneficiaireId);

      // Vérifier l'existence du secteur et du client
      const secteurExists = await prisma.secteurActivite.findUnique({ where: { id: secteurId } });
      if (!secteurExists) return NextResponse.json({ message: "Secteur d'activité non valide." }, { status: 400 });
      const clientExists = await prisma.clientBeneficiaire.findUnique({ where: { id: clientId } });
      if (!clientExists) return NextResponse.json({ message: "Client bénéficiaire non valide." }, { status: 400 });


      const localisationJson = {
        region: localisationRegion,
        departement: localisationDepartement,
        commune: localisationCommune,
        adressePrecise: localisationAdressePrecise,
      };

      let coutTotalProjetDecimal: Prisma.Decimal | null = null;
      if (coutTotalProjetStr?.trim()) {
        const normalized = coutTotalProjetStr.replace(',', '.');
        if (!/^\d+(\.\d+)?$/.test(normalized)) {
          return NextResponse.json({ message: "Coût total invalide." }, { status: 400 });
        }
        coutTotalProjetDecimal = new Prisma.Decimal(normalized);
      }

      let autresInformationsJson: Record<string, any> = {};
      if (autresInformationsStr && autresInformationsStr.trim() !== "") {
        try {
          autresInformationsJson = JSON.parse(autresInformationsStr);
        } catch (e) {
          return NextResponse.json({ message: "Format JSON invalide pour 'Autres Informations'." }, { status: 400 });
        }
      }
      // Fusionner les champs individuels si présents (compatibilité ascendante)
      if (validation.data.dateDebut) autresInformationsJson.dateDebut = validation.data.dateDebut;
      if (validation.data.responsableProjet) autresInformationsJson.responsableProjet = validation.data.responsableProjet;
      if (validation.data.emailContact) autresInformationsJson.emailContact = validation.data.emailContact;

      const newProjet = await prisma.projet.create({
        data: {
          nom,
          description,
          secteurActiviteId: secteurId,
          clientBeneficiaireId: clientId,
          localisation: Object.values(localisationJson).some(v => v && v.trim() !== "") ? localisationJson : undefined,
          coutTotalProjet: coutTotalProjetDecimal,
          autresInformations: Object.keys(autresInformationsJson).length > 0 ? autresInformationsJson : undefined,
          utilisateurCreationId: creatorId,
        },
      });
      return NextResponse.json(newProjet, { status: 201 });
    } catch (error) {
      console.error("Erreur POST /api/configuration/projets:", error);
      return NextResponse.json({
        message: "Erreur interne du serveur",
        error: error instanceof Error ? error.message : (typeof error === 'string' ? error : JSON.stringify(error)),
      }, { status: 500 });
    }
  });
}