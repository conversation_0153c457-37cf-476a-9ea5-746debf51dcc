import { PrismaClient, RoleUtilisateur } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import * as process from 'process';

const prisma = new PrismaClient();

async function createAdminUser() {
  const args = process.argv.slice(2);
  if (args.length < 5) {
    console.error("Usage: ts-node scripts/create-admin.ts <username> <email> <password> <firstname> <lastname>");
    process.exit(1);
  }

  const [nomUtilisateur, email, password, prenom, nom] = args;

  try {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.utilisateur.findFirst({
      where: { OR: [{ email }, { nomUtilisateur }] }
    });

    if (existingUser) {
      console.error("Un utilisateur avec cet email ou nom d'utilisateur existe déjà");
      process.exit(1);
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10);

    // C<PERSON>er l'utilisateur admin
    const adminUser = await prisma.utilisateur.create({
      data: {
        nomUtilisateur,
        email,
        motDePasse: hashedPassword,
        nom,
        prenom,
        role: RoleUtilisateur.Administrateur,
      },
    });

    console.log("Admin créé avec succès:");
    console.log({
      id: adminUser.id,
      nomUtilisateur: adminUser.nomUtilisateur,
      email: adminUser.email,
      role: adminUser.role,
    });

  } catch (error) {
    console.error("Erreur lors de la création de l'admin:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();