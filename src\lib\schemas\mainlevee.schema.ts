import { z } from "zod";
import { TypeMainlevee, StatutMainlevee } from "@prisma/client";

// Helper pour les enums Prisma
const typeMainleveeValues = Object.values(TypeMainlevee) as [string, ...string[]];
const statutMainleveeValues = Object.values(StatutMainlevee) as [string, ...string[]];

// Schéma pour la création d'une demande de mainlevée
export const DemandeMainleveeSchema = z.object({
  // garantieId sera fourni par le contexte (ex: URL), non par le formulaire direct
  typeMainlevee: z.enum(typeMainleveeValues, {
    required_error: "Le type de mainlevée est requis.",
  }),
  dateDemande: z.date({
    required_error: "La date de demande est requise.",
    invalid_type_error: "Format de date de demande invalide.",
  }),
  commentairesDemande: z.string().max(1000, "Les commentaires ne doivent pas dépasser 1000 caractères.").optional().or(z.literal('')),
});

export type DemandeMainleveeFormValues = z.infer<typeof DemandeMainleveeSchema>;

// Schéma pour la décision sur une demande de mainlevée
export const DecisionMainleveeSchema = z.object({
  statut: z.string()
    .refine(val => val === StatutMainlevee.Accordee || val === StatutMainlevee.Refusee, {
      message: "Le statut de décision doit être 'Accordée' ou 'Refusée'."
    }),
  dateDecision: z.date({
    required_error: "La date de décision est requise.",
    invalid_type_error: "Format de date de décision invalide.",
  }),
  montantRecupereStr: z.string().optional().or(z.literal('')) // Optionnel, surtout si typeMainlevee n'est pas lié à un recouvrement partiel
    .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) >= 0), {
        message: "Le montant récupéré doit être un nombre positif ou nul valide.",
    }),
  commentairesDecision: z.string().max(1000).optional().or(z.literal('')),
  raisonRefus: z.string().max(1000).optional().or(z.literal('')),
})
// Validation conditionnelle : raisonRefus est requise si le statut est Refusee
.refine(data => {
    if (data.statut === StatutMainlevee.Refusee) {
        return data.raisonRefus !== undefined && data.raisonRefus.trim() !== "";
    }
    return true;
}, {
    message: "La raison du refus est requise si la mainlevée est refusée.",
    path: ["raisonRefus"], // Appliquer l'erreur au champ raisonRefus
});

export type DecisionMainleveeFormValues = z.infer<typeof DecisionMainleveeSchema>; 