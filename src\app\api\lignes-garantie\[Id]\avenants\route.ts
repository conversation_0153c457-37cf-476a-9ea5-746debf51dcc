// src/app/api/lignes-garantie/[Id]/avenants/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { AvenantLigneGarantieSchema, TypeAvenant } from '@/lib/schemas/avenant-ligne-garantie.schema';
import Decimal from 'decimal.js';
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET /api/lignes-garantie/[Id]/avenants
export async function GET(request: Request, { params }: { params: { Id: string } }): Promise<NextResponse> {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  
  if (!session || !['Administrateur', 'GestionnaireGesGar', 'AnalysteFinancier'].includes(session.user?.role as string)) {
    return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
  }

  const Id = parseInt(resolvedParams.Id);

  if (isNaN(Id)) {
    return NextResponse.json({ error: 'ID de ligne de garantie invalide' }, { status: 400 });
  }

  try {
    const avenants = await prisma.avenantLigneGarantie.findMany({
      where: { ligneGarantieId: Id },
      orderBy: { dateAvenant: 'desc' }, // Optionnel: trier par date
    });
    return NextResponse.json(avenants);
  } catch (error) {
    console.error('Erreur lors de la récupération des avenants:', error);
    return NextResponse.json({ error: 'Erreur serveur lors de la récupération des avenants' }, { status: 500 });
  }
}

// POST /api/lignes-garantie/[Id]/avenants
// Créer un nouvel avenant pour une ligne de garantie spécifique
export async function POST(request: Request, { params }: { params: { Id: string } }): Promise<NextResponse> {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !['Administrateur', 'GestionnaireGesGar', 'AnalysteFinancier'].includes(session.user?.role as string)) {
    return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
  }

  const Id = parseInt(resolvedParams.Id);
  if (isNaN(Id)) {
    return NextResponse.json({ error: 'ID de ligne de garantie invalide' }, { status: 400 });
  }

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip');
  const userAgentHeader = headersList.get('user-agent');
  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;

  return auditContext.run({ userId: creatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const rawBody = await request.json();
      const parsed = AvenantLigneGarantieSchema.parse({ ...rawBody, Id });
      const { typeAvenant, montantModificationStr, nouvelleDateExpiration, dateAvenant, raison, referenceDocument } = parsed;
      const typeAvenantEnum = typeAvenant as TypeAvenant;
      let montantModification = null;
      if (montantModificationStr) {
        try {
          montantModification = new Decimal(montantModificationStr);
        } catch (e) {
          return NextResponse.json({ error: "Le montant de modification est invalide (doit être au format ISO, ex: 1234.56)" }, { status: 400 });
        }
      }
      if (!typeAvenantEnum || !dateAvenant || !raison) {
        return NextResponse.json({ error: 'Champs requis manquants' }, { status: 400 });
      }

      // Validation conditionnelle des champs en fonction du typeAvenant
      if (
        ([TypeAvenant.AUGMENTATION_MONTANT, TypeAvenant.REDUCTION_MONTANT].includes(typeAvenantEnum) && (montantModification === null || montantModification.isNaN())) ||
        (typeAvenantEnum === TypeAvenant.PROLONGATION_DUREE && !nouvelleDateExpiration)
      ) {
        return NextResponse.json({ error: 'Champ manquant pour le type d\'avenant spécifié' }, { status: 400 });
      }

      // Vérifier si la ligne de garantie existe
      const ligneGarantie = await prisma.ligneGarantie.findUnique({
        where: { id: Id },
      });

      if (!ligneGarantie) {
        return NextResponse.json({ error: 'Ligne de garantie non trouvée' }, { status: 404 });
      }

      const utilisateurId = creatorId;

    const nouvelAvenant = await prisma.avenantLigneGarantie.create({
      data: {
        ligneGarantieId: Id,
        typeAvenant: typeAvenantEnum, // Ajout du champ requis
        montantModification: montantModification !== null ? montantModification.toString() : null,
        nouvelleDateExpiration: nouvelleDateExpiration ? new Date(nouvelleDateExpiration) : null,
        dateAvenant: new Date(dateAvenant),
        raison,
        referenceDocument,
        utilisateurCreationId: utilisateurId,
        utilisateurModificationId: utilisateurId, // Pour la création, on peut mettre le même
      },
    });

    // Logique de mise à jour de la ligne de garantie (montant, date d'expiration)
    // Ceci est un exemple simplifié. Une logique plus complexe pourrait être nécessaire
    const updatedLigneData: any = {};
    if (typeAvenantEnum === TypeAvenant.AUGMENTATION_MONTANT && montantModification !== null) {
      updatedLigneData.montantInitial = typeof (ligneGarantie.montantInitial as any).plus === 'function'
        ? (ligneGarantie.montantInitial as any).plus(new Decimal(montantModification))
        : Number(ligneGarantie.montantInitial) + Number(montantModification);
      updatedLigneData.montantDisponible = typeof (ligneGarantie.montantDisponible as any).plus === 'function'
        ? (ligneGarantie.montantDisponible as any).plus(new Decimal(montantModification))
        : Number(ligneGarantie.montantDisponible) + Number(montantModification);
    } else if (typeAvenantEnum === TypeAvenant.REDUCTION_MONTANT && montantModification !== null) {
 updatedLigneData.montantInitial = new Decimal(ligneGarantie.montantInitial as any)
   .minus(montantModification)
   .toString();
      updatedLigneData.montantDisponible = ligneGarantie.montantDisponible.toNumber
        ? ligneGarantie.montantDisponible.toNumber() - Number(montantModification)
        : Number(ligneGarantie.montantDisponible) - Number(montantModification);
      // Assurer que le montant ne devienne pas négatif
      if (updatedLigneData.montantInitial < 0) updatedLigneData.montantInitial = 0;
      if (updatedLigneData.montantDisponible < 0) updatedLigneData.montantDisponible = 0;
    }

    if (typeAvenantEnum === TypeAvenant.PROLONGATION_DUREE && nouvelleDateExpiration) {
      updatedLigneData.dateExpiration = new Date(nouvelleDateExpiration);
    }

    if (Object.keys(updatedLigneData).length > 0) {
      await prisma.ligneGarantie.update({
        where: { id: Id },
        data: { 
          ...updatedLigneData,
          utilisateurModificationId: utilisateurId,
        },
      });
    }

      return NextResponse.json(nouvelAvenant, { status: 201 });
    } catch (error: any) {
      console.error('Erreur lors de la création de l\'avenant:', error);
      if (error.code === 'P2002') { // Unique constraint violation
          return NextResponse.json({ error: 'Un avenant avec ces détails existe déjà.' }, { status: 409 });
      }
      return NextResponse.json({ error: 'Erreur serveur lors de la création de l\'avenant', details: error.message }, { status: 500 });
    }
  });
}
