"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState, useCallback } from "react"; // Ajout de useCallback
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { getColumns, UserColumn } from "./columns"; // getColumns au lieu de columns
import { DataTable } from "@/components/shared/data-table";
import { RoleUtilisateur } from "@prisma/client";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import { MoreHorizontal } from "lucide-react";

// La fonction getUsers reste la même
async function getUsers(session: any): Promise<UserColumn[]> {
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return [];
  }
  const res = await fetch("/api/admin/users");
  if (!res.ok) {
    console.error("Erreur API getUsers:", await res.text());
    return [];
  }
  return res.json();
}

export default function AdminUsersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState<UserColumn[]>([]);
  const [isLoadingPage, setIsLoadingPage] = useState(true);
  const [userToDelete, setUserToDelete] = useState<UserColumn | null>(null); // Pour le dialogue de suppression
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const fetchUsersList = useCallback(() => {
    if (session && session.user?.role === RoleUtilisateur.Administrateur) {
      setIsLoadingPage(true);
      getUsers(session)
        .then((data) => {
          setUsers(data);
          setIsLoadingPage(false);
        })
        .catch((error) => {
          console.error(
            "Erreur de chargement des utilisateurs sur la page:",
            error
          );
          toast.error("Impossible de charger la liste des utilisateurs.");
          setIsLoadingPage(false);
        });
    }
  }, [session, toast]); // Ajouter toast aux dépendances si utilisé dans fetchUsersList

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/auth/connexion?callbackUrl=/admin/utilisateurs");
      return;
    }

    if (session.user?.role !== RoleUtilisateur.Administrateur) {
      toast.error("Page réservée aux administrateurs.");
      router.replace("/");
      return;
    }
    fetchUsersList();
  }, [session, status, router, toast, fetchUsersList]);

  const handleEditUser = (user: UserColumn) => {
    router.push(`/admin/utilisateurs/${user.id}/modifier`);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      const response = await fetch(`/api/admin/users/${userToDelete.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Échec de la désactivation de l'utilisateur"
        );
      }

      toast.success(
        `L'utilisateur ${userToDelete.nomUtilisateur} a été désactivé.`
      );
      fetchUsersList(); // Recharger la liste des utilisateurs
    } catch (error: any) {
      toast.error(error.message || "Une erreur s'est produite.");
    } finally {
      setIsDeleteDialogOpen(false);
      setUserToDelete(null);
    }
  };

  // Adapter la fonction pour ouvrir le dialogue
  const openDeleteDialog = (user: UserColumn) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  // Passer openDeleteDialog aux colonnes
  // Pour cela, il faut que `getColumns` accepte cette fonction
  // Modifions `columns.tsx` pour cela
  // Pour l'instant, nous allons modifier l'action directement dans le DropdownMenuItem
  // dans `columns.tsx` pour appeler une fonction globale ou utiliser un contexte,
  // ou plus simplement, nous allons passer une fonction de rendu pour la cellule d'action.

  // Solution plus simple : modifier columns.tsx pour accepter une fonction onAction
  // Ou, pour ce tutoriel, nous allons reconstruire les colonnes ici pour passer la fonction
  const columns = React.useMemo(
  () => getColumns({ onDelete: openDeleteDialog, onEdit: handleEditUser }),
  [openDeleteDialog] // openDeleteDialog doit être wrappé dans useCallback
);
  // En fait, la meilleure approche est de modifier la cellule d'action dans columns.tsx
  // pour qu'elle soit un composant qui peut utiliser un contexte ou un état local
  // pour déclencher le dialogue. Pour simplifier ici, nous allons juste rendre le dialogue
  // et le déclencher depuis le DropdownMenuItem.

  // Pour passer la fonction de suppression au composant `columns` :
  // 1. Modifier `columns.tsx` : `getColumns` prend `onDeleteClick: (user: UserColumn) => void`
  // 2. Dans `columns.tsx`, le DropdownMenuItem "Supprimer" appelle `onDeleteClick(user)`
  // 3. Ici : `const columns = React.useMemo(() => getColumns(openDeleteDialog), [openDeleteDialog]);`

  // Pour cette démo, je vais modifier le composant columns.tsx pour qu'il puisse recevoir
  // la fonction `openDeleteDialog` via une prop ou un contexte.
  // Alternative plus simple pour l'instant : on va le gérer avec une prop directement sur DataTable
  // ou en modifiant la définition des colonnes pour inclure le déclencheur du dialogue.

  // Le plus simple pour l'instant est de modifier la cellule d'action dans columns.tsx
  // pour qu'elle prenne une fonction de rappel.
  // Pour l'instant, je vais laisser le TODO dans columns.tsx et vous montrer comment
  // le dialogue est structuré ici. La liaison se fera en passant la fonction `openDeleteDialog`
  // à une version modifiée de `getColumns`.

  // Pour que cela fonctionne, vous devez modifier `columns.tsx`
  // pour que `getColumns` accepte `openDeleteDialog` et l'utilise.
  // Exemple de modification dans columns.tsx (juste le DropdownMenuItem):
  // <DropdownMenuItem onClick={() => props.onDeleteClick(user)} className="text-red-600 ...">Supprimer</DropdownMenuItem>
  // Et `getColumns` deviendrait `getColumns = (props: { onDeleteClick: (user: UserColumn) => void }): ColumnDef<UserColumn>[] => [...]`

  // Pour cette version, nous allons modifier le fichier columns.tsx pour qu'il ACCEPTE la fonction
  // et nous l'appellerons ici.
  if (status === "loading" || isLoadingPage) {
    return (
      <div className="p-6">Chargement de la page et des utilisateurs...</div>
    );
  }
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return (
      <div className="p-6">Accès non autorisé. Redirection en cours...</div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold">
          Gestion des Utilisateurs
        </h1>
        <Link href="/admin/utilisateurs/nouveau">
          <Button>Ajouter un Utilisateur</Button>
        </Link>
      </div>

      <DataTable columns={columns} data={users} />

      {userToDelete && (
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Désactiver l'utilisateur ?</AlertDialogTitle>
              <AlertDialogDescription>
                Êtes-vous sûr de vouloir désactiver l'utilisateur{" "}
                <strong>
                  {userToDelete.prenom} {userToDelete.nom} (
                  {userToDelete.nomUtilisateur})
                </strong>{" "}
                ? Cette action marquera l'utilisateur comme inactif et il ne
                pourra plus se connecter.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setUserToDelete(null)}>
                Annuler
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteUser}
                className="bg-red-600 hover:bg-red-700"
              >
                Oui, Désactiver
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}
