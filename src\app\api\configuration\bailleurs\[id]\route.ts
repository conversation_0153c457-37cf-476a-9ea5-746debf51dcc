// src/app/api/configuration/bailleurs/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, Prisma } from "@prisma/client";
import { BailleurSchema } from "@/lib/schemas/bailleur.schema";
import { auditContext } from "@/lib/prisma-audit.middleware";
import { headers } from "next/headers";

interface RouteParams {
  params: { id: string };
}

// GET: Récupérer un bailleur spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(
      session.user?.role as string
    )
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id))
    return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const userId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress =
    headersList.get("x-forwarded-for") || headersList.get("x-real-ip") || null;
  const userAgentHeader = headersList.get("user-agent") || null;

  return auditContext.run(
    {
      userId,
      ip: ipAddress ?? undefined,
      userAgent: userAgentHeader ?? undefined,
    },
    async () => {
      try {
        const bailleur = await prisma.bailleur.findUnique({ where: { id } });
        if (!bailleur)
          return NextResponse.json(
            { message: "Bailleur non trouvé" },
            { status: 404 }
          );
        return NextResponse.json(bailleur);
      } catch (error) {
        console.error(`Erreur GET /api/configuration/bailleurs/${id}:`, error);
        return NextResponse.json(
          { message: "Erreur interne du serveur" },
          { status: 500 }
        );
      }
    }
  );
}

// PUT: Mettre à jour un bailleur
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(
      session.user?.role as string
    )
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id))
    return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress =
    headersList.get("x-forwarded-for") || headersList.get("x-real-ip") || null;
  const userAgentHeader = headersList.get("user-agent") || null;

  return auditContext.run(
    {
      userId: modifierId,
      ip: ipAddress ?? undefined,
      userAgent: userAgentHeader ?? undefined,
    },
    async () => {
      try {
        const body = await request.json();
        const validation = BailleurSchema.safeParse(body); // Le nom peut être modifié

        if (!validation.success) {
          return NextResponse.json(
            {
              message: "Données invalides",
              errors: validation.error.formErrors.fieldErrors,
            },
            { status: 400 }
          );
        }

        const {
          nom,
          description,
          contactNomRepresentant,
          contactEmail,
          contactTelephone,
          autresInfoPaysOrigine,
          autresInfoTypeOrganisation,
          autresInfoSiteWeb,
        } = validation.data;

        const contactJson = {
          nomRepresentant: contactNomRepresentant,
          email: contactEmail,
          telephone: contactTelephone,
        };
        // Regrouper les autres informations dans un objet si au moins une est présente
        let autresInformationsJson: {
          paysOrigine?: string;
          typeOrganisation?: string;
          siteWeb?: string;
        } | undefined = undefined;
        if (
          autresInfoPaysOrigine ||
          autresInfoTypeOrganisation ||
          autresInfoSiteWeb
        ) {
          autresInformationsJson = {
            paysOrigine: autresInfoPaysOrigine,
            typeOrganisation: autresInfoTypeOrganisation,
            siteWeb: autresInfoSiteWeb,
          };
        }

        // Vérifier si le nouveau nom existe déjà pour un autre bailleur
        const existingBailleurWithName = await prisma.bailleur.findFirst({
          where: { nom, id: { not: id } },
        });
        if (existingBailleurWithName) {
          return NextResponse.json(
            { message: "Un autre bailleur avec ce nom existe déjà." },
            { status: 409 }
          );
        }

        const updatedBailleur = await prisma.bailleur.update({
          where: { id },
           data: {
          nom,
          description,
          contact: contactJson,
          autresInformations: autresInformationsJson && Object.keys(autresInformationsJson).length > 0 ? autresInformationsJson : Prisma.JsonNull,
          utilisateurModificationId: modifierId,
        },
        });
        return NextResponse.json(updatedBailleur);
      } catch (error: any) {
        console.error(`Erreur PUT /api/configuration/bailleurs/${id}:`, error);
        if (error.code === "P2025")
          return NextResponse.json(
            { message: "Bailleur non trouvé" },
            { status: 404 }
          );
        return NextResponse.json(
          { message: "Erreur interne du serveur" },
          { status: 500 }
        );
      }
    }
  );
}

// DELETE: Supprimer un bailleur
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(
      session.user?.role as string
    )
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id))
    return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const deleterId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress =
    headersList.get("x-forwarded-for") || headersList.get("x-real-ip") || null;
  const userAgentHeader = headersList.get("user-agent") || null;

  return auditContext.run(
    {
      userId: deleterId,
      ip: ipAddress ?? undefined,
      userAgent: userAgentHeader ?? undefined,
    },
    async () => {
      try {
        // Vérifier si le bailleur est lié à des lignes de garantie
        const lignesGarantieCount = await prisma.ligneGarantie.count({
          where: { bailleurId: id },
        });
        if (lignesGarantieCount > 0) {
          return NextResponse.json(
            {
              message: `Impossible de supprimer ce bailleur car il est lié à ${lignesGarantieCount} ligne(s) de garantie.`,
            },
            { status: 400 }
          );
        }

        await prisma.bailleur.delete({ where: { id } });
        // Pour la suppression, l'entiteId dans AuditLog sera celui de l'enregistrement supprimé.
        // Le middleware Prisma devrait gérer cela en passant l'ID de l'enregistrement supprimé.
        // Si le middleware ne le fait pas automatiquement pour 'delete', il faudrait le logguer manuellement ici avant la suppression.
        // Cependant, notre middleware actuel loggue 'nouvellesValeurs' qui sera null pour delete.
        // Il faudrait l'adapter pour stocker l'objet supprimé dans 'ancienValeurs' pour 'DELETE'.
        // Pour l'instant, le log indiquera l'action DELETE sur l'entité Bailleur avec son ID.
        return NextResponse.json(
          { message: "Bailleur supprimé avec succès" },
          { status: 200 }
        );
      } catch (error: any) {
        console.error(
          `Erreur DELETE /api/configuration/bailleurs/${id}:`,
          error
        );
        if (error.code === "P2025")
          return NextResponse.json(
            { message: "Bailleur non trouvé" },
            { status: 404 }
          );
        // Gérer P2003: Foreign key constraint failed (si la vérification ci-dessus n'est pas suffisante)
        if (error.code === "P2003")
          return NextResponse.json(
            {
              message:
                "Impossible de supprimer ce bailleur car il est référencé ailleurs.",
            },
            { status: 400 }
          );
        return NextResponse.json(
          { message: "Erreur interne du serveur" },
          { status: 500 }
        );
      }
    }
  );
}
