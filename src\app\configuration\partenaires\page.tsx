// src/app/configuration/partenaires/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, Partenaire, TypePartenaire } from "@prisma/client";
import { FormDescription } from "@/components/ui/form";
import { getPartenaireColumns, PartenaireColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose
} from "@/components/ui/dialog";
import { PartenaireFormValues, PartenaireSchema } from "@/lib/schemas/partenaire.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";

// Options pour le Select TypePartenaire
const typePartenaireOptions = Object.values(TypePartenaire).map(val => ({
    value: val,
    label: val.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim(), // Améliorer le libellé
}));


export default function PartenairesPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [partenaires, setPartenaires] = useState<PartenaireColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPartenaire, setEditingPartenaire] = useState<PartenaireColumn | null>(null);
  const [partenaireToDelete, setPartenaireToDelete] = useState<PartenaireColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const form = useForm<PartenaireFormValues>({
    resolver: zodResolver(PartenaireSchema),
    defaultValues: {
      nom: "",
      typePartenaire: undefined, // Pour que le placeholder du Select s'affiche
      description: "",
      convention: "",
      contactNomRepresentant: "",
      contactAdresse: "",
      contactEmail: "",
      contactTelephone: "",
      numero_agrement: "",
      date_creation_entite: "",
    },
  });

  const fetchPartenairesData = useCallback(async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    try {
      const response = await fetch("/api/configuration/partenaires");
      if (!response.ok) throw new Error("Échec de la récupération des partenaires");
      const data = await response.json();
      setPartenaires(data);
      if (showLoading) setInitialDataLoaded(true);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      if (showLoading) setInitialDataLoaded(true);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    if (sessionStatus === "loading") { setIsLoading(true); return; }
    if (!session?.user?.role || ![RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar].includes(session.user.role as "Administrateur" | "GestionnaireGesGar")) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/");
      setIsLoading(false);
      return;
    }
    if (session && !initialDataLoaded) {
      fetchPartenairesData();
    } else if (session && initialDataLoaded) {
      setIsLoading(false);
    }
  }, [session, sessionStatus, initialDataLoaded, router, toast, fetchPartenairesData]);

  const refreshData = useCallback(async () => { await fetchPartenairesData(false); }, [fetchPartenairesData]);

  const handleEdit = useCallback((partenaire: PartenaireColumn) => {
    setEditingPartenaire(partenaire);
    const contact = partenaire.contact as { nomRepresentant?: string, adresse?: string, email?: string, telephone?: string } | null;
    form.reset({
      nom: partenaire.nom,
      typePartenaire: partenaire.typePartenaire,
      description: partenaire.description || "",
      convention: partenaire.convention || "",
      contactNomRepresentant: contact?.nomRepresentant || "",
      contactAdresse: contact?.adresse || "",
      contactEmail: contact?.email || "",
      contactTelephone: contact?.telephone || "",
      numero_agrement: (typeof partenaire.autresInformations === "object" && partenaire.autresInformations !== null && "numero_agrement" in partenaire.autresInformations)
        ? (partenaire.autresInformations as { numero_agrement?: string }).numero_agrement || ""
        : "",
      date_creation_entite: (typeof partenaire.autresInformations === "object" && partenaire.autresInformations !== null && "date_creation_entite" in partenaire.autresInformations)
        ? (partenaire.autresInformations as { date_creation_entite?: string }).date_creation_entite || ""
        : "",
    });
    setIsFormOpen(true);
  }, [form]);

  const handleDeleteConfirm = useCallback((partenaire: PartenaireColumn) => {
    setPartenaireToDelete(partenaire);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async () => {
    if (!partenaireToDelete) return;
    try {
      const response = await fetch(`/api/configuration/partenaires/${partenaireToDelete.id}`, { method: "DELETE" });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec de la suppression");
      }
      toast({ title: "Partenaire Supprimé", description: `Le partenaire ${partenaireToDelete.nom} a été supprimé.` });
      await refreshData();
    } catch (error: any) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } finally {
      setIsDeleteDialogOpen(false);
      setPartenaireToDelete(null);
    }
  }, [partenaireToDelete, toast, refreshData]);

  const onSubmit = useCallback(async (values: PartenaireFormValues) => {
    const url = editingPartenaire ? `/api/configuration/partenaires/${editingPartenaire.id}` : "/api/configuration/partenaires";
    const method = editingPartenaire ? "PUT" : "POST";
    try {
      const response = await fetch(url, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.errors) {
            Object.keys(errorData.errors).forEach((key) => {
                form.setError(key as keyof PartenaireFormValues, { type: "server", message: errorData.errors[key].join(', ') });
            });
        }
        throw new Error(errorData.message || (editingPartenaire ? "Échec de la mise à jour" : "Échec de la création"));
      }
      toast({ title: editingPartenaire ? "Partenaire Mis à Jour" : "Partenaire Créé" });
      setIsFormOpen(false);
      setEditingPartenaire(null);
      form.reset();
      await refreshData();
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [editingPartenaire, form, toast, refreshData]);

  const columns = useMemo(() => getPartenaireColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }), [handleEdit, handleDeleteConfirm]);

  if (isLoading) return <div className="p-6 text-center">Chargement des partenaires...</div>;
  if (sessionStatus !== "loading" && (!session?.user?.role || !["Administrateur", "GestionnaireGesGar"].includes(session.user.role as string))) {
    return <div className="p-6 text-center">Accès non autorisé. Redirection...</div>;
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des Partenaires Financiers</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => {
            setIsFormOpen(open);
            if (!open) { setEditingPartenaire(null); form.reset(); }
        }}>
          <DialogTrigger asChild>
            <Button onClick={() => { setEditingPartenaire(null); form.reset(); setIsFormOpen(true); }}>
                <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Partenaire
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl"> {/* Un peu plus large pour plus de champs */}
            <DialogHeader>
              <DialogTitle>{editingPartenaire ? "Modifier le Partenaire" : "Ajouter un Partenaire"}</DialogTitle>
              {editingPartenaire && <DialogDescription>Modification du partenaire : {editingPartenaire.nom}</DialogDescription>}
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4 max-h-[75vh] overflow-y-auto pr-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="nom" render={({ field }) => (
                        <FormItem> <FormLabel>Nom du Partenaire <span className="text-red-500">*</span></FormLabel> <FormControl><Input placeholder="Nom officiel du partenaire" {...field} /></FormControl> <FormMessage /> </FormItem>
                    )} />
                    <FormField control={form.control} name="typePartenaire" render={({ field }) => (
                        <FormItem> <FormLabel>Type de Partenaire <span className="text-red-500">*</span></FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir un type" /></SelectTrigger></FormControl>
                                <SelectContent>
                                    {typePartenaireOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                                </SelectContent>
                            </Select> <FormMessage />
                        </FormItem>
                    )} />
                </div>
                <FormField control={form.control} name="description" render={({ field }) => (
                    <FormItem> <FormLabel>Description</FormLabel> <FormControl><Textarea placeholder="Courte description du partenaire..." {...field} /></FormControl> <FormMessage /> </FormItem>
                )} />
                <FormField control={form.control} name="convention" render={({ field }) => (
                    <FormItem> <FormLabel>Référence Convention</FormLabel> <FormControl><Input placeholder="Réf. de la convention avec le Fonds" {...field} /></FormControl> <FormMessage /> </FormItem>
                )} />

                <h3 className="text-lg font-medium border-b pb-2 pt-3">Informations de Contact</h3>
                <FormField control={form.control} name="contactNomRepresentant" render={({ field }) => (
                    <FormItem><FormLabel>Nom du Représentant</FormLabel><FormControl><Input placeholder="Prénom Nom" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={form.control} name="contactAdresse" render={({ field }) => (
                    <FormItem><FormLabel>Adresse Postale</FormLabel><FormControl><Textarea placeholder="Adresse complète du siège ou agence principale" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="contactEmail" render={({ field }) => (
                        <FormItem><FormLabel>Email de Contact</FormLabel><FormControl><Input type="email" placeholder="<EMAIL>" {...field} /></FormControl><FormMessage /></FormItem>
                    )} />
                    <FormField control={form.control} name="contactTelephone" render={({ field }) => (
                        <FormItem><FormLabel>Téléphone de Contact</FormLabel><FormControl><Input placeholder="+XXX XX XXX XXX" {...field} /></FormControl><FormMessage /></FormItem>
                    )} />
                </div>

                <h3 className="text-lg font-medium border-b pb-2 pt-3">Autres Informations</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="numero_agrement" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Numéro d'Agrément</FormLabel>
                      <FormControl><Input placeholder="Ex: AGR-123" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="date_creation_entite" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date de Création de l'Entité</FormLabel>
                      <FormControl><Input type="date" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>

                <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={form.formState.isSubmitting}>
                        {form.formState.isSubmitting ? "Sauvegarde..." : (editingPartenaire ? "Mettre à Jour" : "Créer le Partenaire")}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={partenaires} />

      {partenaireToDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Supprimer le Partenaire ?</AlertDialogTitle>
                    <AlertDialogDescription>
                        Êtes-vous sûr de vouloir supprimer le partenaire <span className="font-semibold">{partenaireToDelete.nom}</span> ?
                        Cette action est irréversible et ne sera possible que si le partenaire n'est lié à aucune allocation.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setPartenaireToDelete(null)}>Annuler</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">Oui, Supprimer</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}