// src/lib/schemas/allocation-ligne-partenaire.schema.ts
import { z } from "zod";
import { StatutAllocation, Periodicite } from "@prisma/client";

// Helper pour convertir les enums Prisma en tableaux de chaînes pour Zod
const statutAllocationValues = Object.values(StatutAllocation) as [string, ...string[]];
const periodiciteValues = Object.values(Periodicite) as [string, ...string[]];

// Fonction de validation pour les chaînes représentant des nombres décimaux positifs
const positiveNumericString = (errorMessage: string = "Doit être un nombre positif valide.") =>
  z.string().min(1, "Ce champ est requis.")
    .refine(val => /^\d+([.,]\d+)?$/.test(val.trim()) && parseFloat(val.replace(',', '.')) > 0, {
        message: errorMessage,
    });

// Fonction de validation pour les chaînes représentant des pourcentages (0-100)
const percentageString = (errorMessage: string = "Doit être un pourcentage valide (0-100).") =>
  z.string().optional().or(z.literal(''))
    .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) >= 0 && parseFloat(val.replace(',', '.')) <= 100), {
        message: errorMessage,
    });

// Fonction de validation pour les chaînes représentant des nombres décimaux >= 0
const nonNegativeNumericString = (errorMessage: string = "Doit être un nombre positif ou nul valide.") =>
  z.string().optional().or(z.literal(''))
    .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseFloat(val.replace(',', '.'))) && parseFloat(val.replace(',', '.')) >= 0), {
        message: errorMessage,
    });


export const AllocationLignePartenaireSchema = z.object({
  ligneGarantieId: z.string().min(1, "Veuillez sélectionner une ligne de garantie.")
    .refine(val => !isNaN(parseInt(val)), { message: "ID de ligne de garantie invalide." }),
  partenaireId: z.string().min(1, "Veuillez sélectionner un partenaire.")
    .refine(val => !isNaN(parseInt(val)), { message: "ID de partenaire invalide." }),
  referenceConvention: z.string().max(100).optional().or(z.literal('')),
  montantAlloueStr: positiveNumericString("Le montant alloué est requis et doit être un nombre positif."),
  dateAllocation: z.preprocess((v) => typeof v === 'string' ? new Date(v) : v, z.date({
     required_error: "La date d'allocation est requise.",
     invalid_type_error: "Format de date d'allocation invalide.",
  })),
  dateExpiration: z.date({
    invalid_type_error: "Format de date d'expiration invalide.",
  }).optional().nullable(), // Optionnelle et peut être null
  statut: z.enum(statutAllocationValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un statut valide." }),
  }),
  tauxCouvertureMaxStr: percentageString("Le taux de couverture maximum doit être un pourcentage valide (0-100)."), // Ex: "75" pour 75%
  tauxInteretStr: nonNegativeNumericString("Le taux d'intérêt doit être un pourcentage valide (>=0)."), // Ex: "5" pour 5%
  tauxCommissionStr: nonNegativeNumericString("Le taux de commission doit être un pourcentage valide (>=0)."), // Ex: "1.5" pour 1.5%
  periodicitePaiementInteret: z.enum(periodiciteValues).optional().nullable(),
  periodicitePaiementCommission: z.enum(periodiciteValues).optional().nullable(),
  commentaires: z.string().max(2000).optional().or(z.literal('')),
})
.refine(data => {
    // Si dateExpiration est fournie, elle doit être postérieure à dateAllocation
    if (data.dateExpiration && data.dateAllocation) {
        return data.dateExpiration > data.dateAllocation;
    }
    return true;
}, {
    message: "La date d'expiration doit être postérieure à la date d'allocation.",
    path: ["dateExpiration"],
});

export type AllocationLignePartenaireFormValues = z.infer<typeof AllocationLignePartenaireSchema>;