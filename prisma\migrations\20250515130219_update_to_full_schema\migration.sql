/*
  Warnings:

  - A unique constraint covering the columns `[email]` on the table `Utilisateur` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `dateModification` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.
  - Added the required column `email` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.
  - Added the required column `motDePasse` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.
  - Added the required column `nom` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.
  - Added the required column `prenom` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.
  - Added the required column `role` to the `Utilisateur` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "RoleUtilisateur" AS ENUM ('Administrateur', 'GestionnaireGesGar', 'AnalysteFinancier', 'Partenaire', 'Bailleur', 'Auditeur');

-- CreateEnum
CREATE TYPE "TypeAvenant" AS ENUM ('AUGMENTATION_MONTANT', 'REDUCTION_MONTANT', 'PROLONGATION_DUREE', 'MODIFICATION_CONDITIONS', 'AUTRE');

-- CreateEnum
CREATE TYPE "StatutLigneGarantie" AS ENUM ('Active', 'Epuisee', 'Suspendue', 'Expiree', 'Cloturee', 'Renouvelee', 'EnAttenteValidation');

-- CreateEnum
CREATE TYPE "TypePartenaire" AS ENUM ('Banque', 'IMF', 'SFD', 'AutreFinancier', 'Technique', 'Institutionnel');

-- CreateEnum
CREATE TYPE "StatutAllocation" AS ENUM ('Active', 'Epuisee', 'Suspendue', 'Expiree', 'Cloturee', 'Renouvelee', 'Reaffectee', 'EnAttenteValidation');

-- CreateEnum
CREATE TYPE "Periodicite" AS ENUM ('Mensuelle', 'Trimestrielle', 'Semestrielle', 'Annuelle', 'Unique');

-- CreateEnum
CREATE TYPE "TypeClient" AS ENUM ('PME', 'PMI', 'GE', 'GIE', 'Cooperative', 'Association', 'PersonnePhysique');

-- CreateEnum
CREATE TYPE "GenreClient" AS ENUM ('Masculin', 'Feminin', 'Mixte', 'NonSpecifie');

-- CreateEnum
CREATE TYPE "TypeGarantie" AS ENUM ('Individuelle', 'Portefeuille', 'Cautionnement');

-- CreateEnum
CREATE TYPE "StatutGarantie" AS ENUM ('EnInstruction', 'Echue', 'Active', 'MainleveeDemandee', 'MainleveeAccordee', 'MainleveeRefusee', 'Transferree', 'EnSouffrance', 'MiseEnJeuDemandee', 'MiseEnJeuAcceptee', 'MiseEnJeuPayee', 'MiseEnJeuRefusee');

-- CreateEnum
CREATE TYPE "OperateurRegle" AS ENUM ('EQUAL', 'NOT_EQUAL', 'GREATER_THAN', 'LESS_THAN', 'GREATER_THAN_OR_EQUAL', 'LESS_THAN_OR_EQUAL', 'IN', 'NOT_IN', 'CONTAINS', 'STARTS_WITH', 'ENDS_WITH', 'IS_NULL', 'IS_NOT_NULL');

-- CreateEnum
CREATE TYPE "TypeMainlevee" AS ENUM ('RemboursementTotalCredit', 'RemboursementPartielAvecRecouvrement', 'PourTransfertGarantie', 'AutreMotif');

-- CreateEnum
CREATE TYPE "StatutMainlevee" AS ENUM ('Demandee', 'EnCoursApprobation', 'Accordee', 'Refusee');

-- CreateEnum
CREATE TYPE "StatutMiseEnJeu" AS ENUM ('Demandee', 'EnCoursInstruction', 'ApprouveePartiellement', 'ApprouveeTotalement', 'Refusee', 'Payee', 'EnAttentePaiement');

-- CreateEnum
CREATE TYPE "TypePaiementInteretCommission" AS ENUM ('Interet', 'Commission');

-- AlterTable
ALTER TABLE "Utilisateur" ADD COLUMN     "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "dateModification" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "email" TEXT NOT NULL,
ADD COLUMN     "motDePasse" TEXT NOT NULL,
ADD COLUMN     "nom" TEXT NOT NULL,
ADD COLUMN     "photoUrl" TEXT,
ADD COLUMN     "prenom" TEXT NOT NULL,
ADD COLUMN     "role" "RoleUtilisateur" NOT NULL,
ADD COLUMN     "utilisateurCreationId" INTEGER,
ADD COLUMN     "utilisateurModificationId" INTEGER;

-- CreateTable
CREATE TABLE "Bailleur" (
    "id" SERIAL NOT NULL,
    "nom" TEXT NOT NULL,
    "description" TEXT,
    "contact" JSONB,
    "autresInformations" JSONB,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "Bailleur_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LigneGarantie" (
    "id" SERIAL NOT NULL,
    "bailleurId" INTEGER NOT NULL,
    "nom" TEXT NOT NULL,
    "referenceConvention" TEXT,
    "description" TEXT,
    "montantInitial" DECIMAL(18,2) NOT NULL,
    "montantDisponible" DECIMAL(18,2) NOT NULL,
    "dateOuverture" DATE NOT NULL,
    "dateExpiration" DATE NOT NULL,
    "devise" TEXT NOT NULL DEFAULT 'XOF',
    "statut" "StatutLigneGarantie" NOT NULL,
    "autresInformations" JSONB,
    "ligneGarantiePrecedenteId" INTEGER,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "LigneGarantie_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AvenantLigneGarantie" (
    "id" SERIAL NOT NULL,
    "ligneGarantieId" INTEGER NOT NULL,
    "typeAvenant" "TypeAvenant" NOT NULL,
    "montantModification" DECIMAL(18,2),
    "nouvelleDateExpiration" DATE,
    "dateAvenant" DATE NOT NULL,
    "raison" TEXT NOT NULL,
    "referenceDocument" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "AvenantLigneGarantie_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Partenaire" (
    "id" SERIAL NOT NULL,
    "typePartenaire" "TypePartenaire" NOT NULL,
    "nom" TEXT NOT NULL,
    "description" TEXT,
    "contact" JSONB,
    "convention" TEXT,
    "autresInformations" JSONB,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "Partenaire_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AllocationLignePartenaire" (
    "id" SERIAL NOT NULL,
    "ligneGarantieId" INTEGER NOT NULL,
    "partenaireId" INTEGER NOT NULL,
    "referenceConvention" TEXT,
    "montantAlloue" DECIMAL(18,2) NOT NULL,
    "montantDisponible" DECIMAL(18,2) NOT NULL,
    "dateAllocation" DATE NOT NULL,
    "dateExpiration" DATE,
    "statut" "StatutAllocation" NOT NULL,
    "tauxCouvertureMax" DECIMAL(5,2),
    "tauxInteret" DECIMAL(5,2),
    "tauxCommission" DECIMAL(5,2),
    "periodicitePaiementInteret" "Periodicite",
    "periodicitePaiementCommission" "Periodicite",
    "commentaires" TEXT,
    "allocationPrecedenteId" INTEGER,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "AllocationLignePartenaire_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AvenantAllocation" (
    "id" SERIAL NOT NULL,
    "allocationId" INTEGER NOT NULL,
    "typeAvenant" "TypeAvenant" NOT NULL,
    "montantModification" DECIMAL(18,2),
    "nouvelleDateExpiration" DATE,
    "dateAvenant" DATE NOT NULL,
    "raison" TEXT NOT NULL,
    "referenceDocument" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "AvenantAllocation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClientBeneficiaire" (
    "id" SERIAL NOT NULL,
    "typeClient" "TypeClient" NOT NULL,
    "nomOuRaisonSociale" TEXT NOT NULL,
    "identifiantUnique" TEXT,
    "age" INTEGER,
    "genre" "GenreClient",
    "informationsContact" JSONB NOT NULL,
    "secteurActivitePrincipalId" INTEGER,
    "autresInformations" JSONB,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "ClientBeneficiaire_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SecteurActivite" (
    "id" SERIAL NOT NULL,
    "code" TEXT,
    "nom" TEXT NOT NULL,
    "description" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "SecteurActivite_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Projet" (
    "id" SERIAL NOT NULL,
    "nom" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "secteurActiviteId" INTEGER NOT NULL,
    "clientBeneficiaireId" INTEGER NOT NULL,
    "localisation" JSONB,
    "coutTotalProjet" DECIMAL(18,2),
    "autresInformations" JSONB,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "Projet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Garantie" (
    "id" SERIAL NOT NULL,
    "referenceGarantie" TEXT NOT NULL,
    "ligneGarantieId" INTEGER NOT NULL,
    "allocationId" INTEGER NOT NULL,
    "partenaireId" INTEGER NOT NULL,
    "projetId" INTEGER NOT NULL,
    "clientBeneficiaireId" INTEGER NOT NULL,
    "typeGarantie" "TypeGarantie" NOT NULL,
    "montantCredit" DECIMAL(18,2) NOT NULL,
    "tauxCouvertureApplique" DECIMAL(5,2) NOT NULL,
    "montantGarantie" DECIMAL(18,2) NOT NULL,
    "dateOctroiCredit" DATE NOT NULL,
    "dateDemandeGarantie" DATE NOT NULL,
    "dateAccordGarantie" DATE NOT NULL,
    "dateEffetGarantie" DATE NOT NULL,
    "dateEcheanceInitialeCredit" DATE NOT NULL,
    "dateEcheanceGarantie" DATE NOT NULL,
    "statut" "StatutGarantie" NOT NULL,
    "conditionsParticulieres" TEXT,
    "identifiantCreditPartenaire" TEXT NOT NULL,
    "dateDernierRemboursementClient" DATE,
    "montantRestantDuCredit" DECIMAL(18,2),
    "nombreEcheancesImpayees" INTEGER DEFAULT 0,
    "delaiMiseEnJeu" INTEGER NOT NULL DEFAULT 90,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "Garantie_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RegleEligibilite" (
    "id" SERIAL NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "groupeRegleId" INTEGER,
    "ligneGarantieId" INTEGER,
    "champConcerne" TEXT NOT NULL,
    "operateur" "OperateurRegle" NOT NULL,
    "valeurReference" TEXT NOT NULL,
    "messageErreur" TEXT,
    "estActive" BOOLEAN NOT NULL DEFAULT true,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "RegleEligibilite_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GroupeRegleEligibilite" (
    "id" SERIAL NOT NULL,
    "nom" TEXT NOT NULL,
    "description" TEXT,
    "estActif" BOOLEAN NOT NULL DEFAULT true,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "GroupeRegleEligibilite_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Mainlevee" (
    "id" SERIAL NOT NULL,
    "garantieId" INTEGER NOT NULL,
    "typeMainlevee" "TypeMainlevee" NOT NULL,
    "dateDemande" DATE NOT NULL,
    "dateDecision" DATE,
    "montantRecupere" DECIMAL(18,2),
    "commentairesDemande" TEXT,
    "commentairesDecision" TEXT,
    "statut" "StatutMainlevee" NOT NULL,
    "raisonRefus" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "Mainlevee_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MiseEnJeu" (
    "id" SERIAL NOT NULL,
    "garantieId" INTEGER NOT NULL,
    "dateDemande" DATE NOT NULL,
    "dateDecision" DATE,
    "montantDemande" DECIMAL(18,2) NOT NULL,
    "montantApprouve" DECIMAL(18,2),
    "montantPaye" DECIMAL(18,2),
    "datePaiement" DATE,
    "motifDemande" TEXT NOT NULL,
    "statut" "StatutMiseEnJeu" NOT NULL,
    "commentairesDecision" TEXT,
    "referencePaiement" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "MiseEnJeu_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransfertGarantie" (
    "id" SERIAL NOT NULL,
    "garantieOrigineId" INTEGER NOT NULL,
    "garantieDestinationId" INTEGER NOT NULL,
    "dateTransfert" DATE NOT NULL,
    "mainleveeOrigineId" INTEGER NOT NULL,
    "commentaires" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "TransfertGarantie_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PaiementInteretCommission" (
    "id" SERIAL NOT NULL,
    "allocationId" INTEGER NOT NULL,
    "datePaiement" DATE NOT NULL,
    "montantInteret" DECIMAL(18,2),
    "montantCommission" DECIMAL(18,2),
    "typePaiement" "TypePaiementInteretCommission" NOT NULL,
    "periodeConcerneeDebut" DATE,
    "periodeConcerneeFin" DATE,
    "referencePaiement" TEXT,
    "commentaires" TEXT,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "utilisateurCreationId" INTEGER NOT NULL,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "PaiementInteretCommission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" SERIAL NOT NULL,
    "nomFichier" TEXT NOT NULL,
    "cheminStockage" TEXT NOT NULL,
    "typeMime" TEXT NOT NULL,
    "taille" BIGINT NOT NULL,
    "dateUpload" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "description" TEXT,
    "categorieDocument" TEXT,
    "ligneGarantieId" INTEGER,
    "partenaireId" INTEGER,
    "allocationId" INTEGER,
    "clientBeneficiaireId" INTEGER,
    "projetId" INTEGER,
    "garantieId" INTEGER,
    "mainleveeId" INTEGER,
    "miseEnJeuId" INTEGER,
    "transfertGarantieId" INTEGER,
    "paiementId" INTEGER,
    "utilisateurUploadId" INTEGER NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Bailleur_nom_key" ON "Bailleur"("nom");

-- CreateIndex
CREATE UNIQUE INDEX "LigneGarantie_nom_key" ON "LigneGarantie"("nom");

-- CreateIndex
CREATE UNIQUE INDEX "LigneGarantie_ligneGarantiePrecedenteId_key" ON "LigneGarantie"("ligneGarantiePrecedenteId");

-- CreateIndex
CREATE UNIQUE INDEX "Partenaire_nom_key" ON "Partenaire"("nom");

-- CreateIndex
CREATE UNIQUE INDEX "AllocationLignePartenaire_allocationPrecedenteId_key" ON "AllocationLignePartenaire"("allocationPrecedenteId");

-- CreateIndex
CREATE UNIQUE INDEX "ClientBeneficiaire_identifiantUnique_key" ON "ClientBeneficiaire"("identifiantUnique");

-- CreateIndex
CREATE UNIQUE INDEX "SecteurActivite_code_key" ON "SecteurActivite"("code");

-- CreateIndex
CREATE UNIQUE INDEX "SecteurActivite_nom_key" ON "SecteurActivite"("nom");

-- CreateIndex
CREATE UNIQUE INDEX "Garantie_referenceGarantie_key" ON "Garantie"("referenceGarantie");

-- CreateIndex
CREATE UNIQUE INDEX "RegleEligibilite_code_key" ON "RegleEligibilite"("code");

-- CreateIndex
CREATE UNIQUE INDEX "GroupeRegleEligibilite_nom_key" ON "GroupeRegleEligibilite"("nom");

-- CreateIndex
CREATE UNIQUE INDEX "Mainlevee_garantieId_key" ON "Mainlevee"("garantieId");

-- CreateIndex
CREATE UNIQUE INDEX "TransfertGarantie_garantieDestinationId_key" ON "TransfertGarantie"("garantieDestinationId");

-- CreateIndex
CREATE UNIQUE INDEX "TransfertGarantie_mainleveeOrigineId_key" ON "TransfertGarantie"("mainleveeOrigineId");

-- CreateIndex
CREATE UNIQUE INDEX "Utilisateur_email_key" ON "Utilisateur"("email");

-- AddForeignKey
ALTER TABLE "Utilisateur" ADD CONSTRAINT "Utilisateur_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Utilisateur" ADD CONSTRAINT "Utilisateur_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Bailleur" ADD CONSTRAINT "Bailleur_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bailleur" ADD CONSTRAINT "Bailleur_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_bailleurId_fkey" FOREIGN KEY ("bailleurId") REFERENCES "Bailleur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_ligneGarantiePrecedenteId_fkey" FOREIGN KEY ("ligneGarantiePrecedenteId") REFERENCES "LigneGarantie"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LigneGarantie" ADD CONSTRAINT "LigneGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantLigneGarantie" ADD CONSTRAINT "AvenantLigneGarantie_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantLigneGarantie" ADD CONSTRAINT "AvenantLigneGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantLigneGarantie" ADD CONSTRAINT "AvenantLigneGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Partenaire" ADD CONSTRAINT "Partenaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_partenaireId_fkey" FOREIGN KEY ("partenaireId") REFERENCES "Partenaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_allocationPrecedenteId_fkey" FOREIGN KEY ("allocationPrecedenteId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AllocationLignePartenaire" ADD CONSTRAINT "AllocationLignePartenaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantAllocation" ADD CONSTRAINT "AvenantAllocation_allocationId_fkey" FOREIGN KEY ("allocationId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantAllocation" ADD CONSTRAINT "AvenantAllocation_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AvenantAllocation" ADD CONSTRAINT "AvenantAllocation_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_secteurActivitePrincipalId_fkey" FOREIGN KEY ("secteurActivitePrincipalId") REFERENCES "SecteurActivite"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientBeneficiaire" ADD CONSTRAINT "ClientBeneficiaire_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SecteurActivite" ADD CONSTRAINT "SecteurActivite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SecteurActivite" ADD CONSTRAINT "SecteurActivite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_secteurActiviteId_fkey" FOREIGN KEY ("secteurActiviteId") REFERENCES "SecteurActivite"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_clientBeneficiaireId_fkey" FOREIGN KEY ("clientBeneficiaireId") REFERENCES "ClientBeneficiaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Projet" ADD CONSTRAINT "Projet_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_allocationId_fkey" FOREIGN KEY ("allocationId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_partenaireId_fkey" FOREIGN KEY ("partenaireId") REFERENCES "Partenaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_projetId_fkey" FOREIGN KEY ("projetId") REFERENCES "Projet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_clientBeneficiaireId_fkey" FOREIGN KEY ("clientBeneficiaireId") REFERENCES "ClientBeneficiaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Garantie" ADD CONSTRAINT "Garantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_groupeRegleId_fkey" FOREIGN KEY ("groupeRegleId") REFERENCES "GroupeRegleEligibilite"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegleEligibilite" ADD CONSTRAINT "RegleEligibilite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GroupeRegleEligibilite" ADD CONSTRAINT "GroupeRegleEligibilite_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GroupeRegleEligibilite" ADD CONSTRAINT "GroupeRegleEligibilite_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Mainlevee" ADD CONSTRAINT "Mainlevee_garantieId_fkey" FOREIGN KEY ("garantieId") REFERENCES "Garantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Mainlevee" ADD CONSTRAINT "Mainlevee_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Mainlevee" ADD CONSTRAINT "Mainlevee_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MiseEnJeu" ADD CONSTRAINT "MiseEnJeu_garantieId_fkey" FOREIGN KEY ("garantieId") REFERENCES "Garantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MiseEnJeu" ADD CONSTRAINT "MiseEnJeu_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MiseEnJeu" ADD CONSTRAINT "MiseEnJeu_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_garantieOrigineId_fkey" FOREIGN KEY ("garantieOrigineId") REFERENCES "Garantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_garantieDestinationId_fkey" FOREIGN KEY ("garantieDestinationId") REFERENCES "Garantie"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_mainleveeOrigineId_fkey" FOREIGN KEY ("mainleveeOrigineId") REFERENCES "Mainlevee"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TransfertGarantie" ADD CONSTRAINT "TransfertGarantie_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaiementInteretCommission" ADD CONSTRAINT "PaiementInteretCommission_allocationId_fkey" FOREIGN KEY ("allocationId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaiementInteretCommission" ADD CONSTRAINT "PaiementInteretCommission_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PaiementInteretCommission" ADD CONSTRAINT "PaiementInteretCommission_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_ligneGarantieId_fkey" FOREIGN KEY ("ligneGarantieId") REFERENCES "LigneGarantie"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_partenaireId_fkey" FOREIGN KEY ("partenaireId") REFERENCES "Partenaire"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_allocationId_fkey" FOREIGN KEY ("allocationId") REFERENCES "AllocationLignePartenaire"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_clientBeneficiaireId_fkey" FOREIGN KEY ("clientBeneficiaireId") REFERENCES "ClientBeneficiaire"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_projetId_fkey" FOREIGN KEY ("projetId") REFERENCES "Projet"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_garantieId_fkey" FOREIGN KEY ("garantieId") REFERENCES "Garantie"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_mainleveeId_fkey" FOREIGN KEY ("mainleveeId") REFERENCES "Mainlevee"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_miseEnJeuId_fkey" FOREIGN KEY ("miseEnJeuId") REFERENCES "MiseEnJeu"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_transfertGarantieId_fkey" FOREIGN KEY ("transfertGarantieId") REFERENCES "TransfertGarantie"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_paiementId_fkey" FOREIGN KEY ("paiementId") REFERENCES "PaiementInteretCommission"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_utilisateurUploadId_fkey" FOREIGN KEY ("utilisateurUploadId") REFERENCES "Utilisateur"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
