// src/app/api/configuration/clients-beneficiaires/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeClient, GenreClient } from "@prisma/client";
import { ClientBeneficiaireSchema } from "@/lib/schemas/client-beneficiaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET: Lister tous les clients bénéficiaires
export async function GET(request: Request) {
  console.log("API ClientsBeneficiaires GET appelée");
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const clients = await prisma.clientBeneficiaire.findMany({
        orderBy: { nomOuRaisonSociale: "asc" },
        include: {
          secteurActivitePrincipal: { select: { id: true, nom: true } },
          utilisateurCreation: { select: { nomUtilisateur: true }},
        }
      });
      return NextResponse.json(clients);
    } catch (error) {
      console.error("Erreur GET /api/configuration/clients-beneficiaires:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// POST: Créer un nouveau client bénéficiaire
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const body = await request.json();
      const validation = ClientBeneficiaireSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        nomOuRaisonSociale, typeClient, identifiantUnique,
        contactAdressePhysique, contactEmail, contactTelephone1, contactTelephone2,
        secteurActivitePrincipalId, age, genre, autresInformations
      } = validation.data;

      const informationsContactJson = {
        adressePhysique: contactAdressePhysique ?? "",
        email: contactEmail ?? "",
        telephone1: contactTelephone1 ?? "",
        telephone2: contactTelephone2 ?? "",
      };

      // Vérifier unicité identifiantUnique s'il est fourni
      if (identifiantUnique && identifiantUnique.trim() !== "") {
        const existingIdentifiant = await prisma.clientBeneficiaire.findUnique({ where: { identifiantUnique } });
        if (existingIdentifiant) {
          return NextResponse.json({ message: "Cet identifiant unique est déjà utilisé." }, { status: 409 });
        }
      }
      // Vérifier existence secteur activité si fourni
      let secteurId: number | null = null;
      if (secteurActivitePrincipalId && secteurActivitePrincipalId.trim() !== "") {
        secteurId = parseInt(secteurActivitePrincipalId);
        const secteurExists = await prisma.secteurActivite.findUnique({ where: { id: secteurId } });
        if (!secteurExists) return NextResponse.json({ message: "Secteur d'activité principal non valide." }, { status: 400 });
      }

      // Compatibilité migration : assemble autresInformations si absent
      let autresInformationsFinal = autresInformations;
      if (!autresInformationsFinal) {
        autresInformationsFinal = {
          dateCreationEntreprise: validation.data.dateCreationEntreprise || undefined,
          formeJuridique: validation.data.formeJuridique || undefined,
          numeroIdentificationFiscale: validation.data.numeroIdentificationFiscale || undefined,
        };
      }

      const data: any = {
        nomOuRaisonSociale,
        typeClient: typeClient as TypeClient,
        identifiantUnique: (identifiantUnique && identifiantUnique.trim() !== "") ? identifiantUnique : null,
        age: (age && age.trim() !== "") ? parseInt(age) : null,
        genre: genre ? genre as GenreClient : null,
        informationsContact: informationsContactJson,
        secteurActivitePrincipalId: secteurId,
        autresInformations: autresInformationsFinal,
      };
      // Retire les champs individuels du data si présents
      delete data.dateCreationEntreprise;
      delete data.formeJuridique;
      delete data.numeroIdentificationFiscale;
      if (creatorId !== undefined) {
        data.utilisateurCreationId = creatorId;
      }

      const newClient = await prisma.clientBeneficiaire.create({
        data,
      });
      return NextResponse.json(newClient, { status: 201 });
    } catch (error) {
      console.error("Erreur POST /api/configuration/clients-beneficiaires:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}