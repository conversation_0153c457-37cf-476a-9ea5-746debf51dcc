import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Adjusted path

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }
  // Optional: Add role-specific checks if KPIs are sensitive, e.g.:
  // if (session.user?.role !== RoleUtilisateur.Administrateur && session.user?.role !== RoleUtilisateur.GestionnaireGesGar) {
  //   return NextResponse.json({ message: "Accès non autorisé" }, { status: 403 });
  // }

  try {
    const kpiData = [
      {
        title: "Lignes de Garantie Actives",
        value: "1.2 Milliards XOF",
        description: "Montant total des lignes actives",
        iconName: "Landmark", // Example Lucide icon name
        bgColorClass: "bg-blue-100 dark:bg-blue-900",
        textColorClass: "text-blue-600 dark:text-blue-400",
      },
      {
        title: "Utilisation Moyenne",
        value: "65%",
        description: "Taux d'utilisation moyen des lignes",
        iconName: "PieChart",
        bgColorClass: "bg-green-100 dark:bg-green-900",
        textColorClass: "text-green-600 dark:text-green-400",
      },
      {
        title: "Contrats en Attente",
        value: "12",
        description: "Nombre de contrats en attente d'approbation",
        iconName: "Clock",
        bgColorClass: "bg-yellow-100 dark:bg-yellow-900",
        textColorClass: "text-yellow-600 dark:text-yellow-400",
      },
      {
        title: "Incidents Référencés",
        value: "3",
        description: "Nombre d'incidents ouverts ce mois-ci",
        iconName: "ShieldAlert",
        bgColorClass: "bg-red-100 dark:bg-red-900",
        textColorClass: "text-red-600 dark:text-red-400",
      },
    ];
    return NextResponse.json(kpiData);
  } catch (error) {
    console.error("Error fetching KPI data:", error);
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    );
  }
}
