// src/app/api/admin/audit-log/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, AuditLog } from "@prisma/client";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: "Non authentifié" }, { status: 401 });
  }
  // Seuls les administrateurs et les auditeurs peuvent voir les logs d'audit
  if (
    session.user?.role !== RoleUtilisateur.Administrateur &&
    session.user?.role !== RoleUtilisateur.Auditeur
  ) {
    return NextResponse.json({ message: "Accès non autorisé" }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "15"); // Nombre d'éléments par page
  const utilisateurIdFilter = searchParams.get("utilisateurId");
  const actionFilter = searchParams.get("action");
  const entiteFilter = searchParams.get("entite");
  const dateDebutFilter = searchParams.get("dateDebut");
  const dateFinFilter = searchParams.get("dateFin");

  const skip = (page - 1) * limit;

  const whereClause: any = {};
  if (utilisateurIdFilter && !isNaN(parseInt(utilisateurIdFilter))) {
    whereClause.utilisateurId = parseInt(utilisateurIdFilter);
  }
  if (actionFilter) {
    whereClause.action = { contains: actionFilter, mode: 'insensitive' };
  }
  if (entiteFilter) {
    whereClause.entite = { contains: entiteFilter, mode: 'insensitive' };
  }
  if (dateDebutFilter) {
    whereClause.timestamp = { ...whereClause.timestamp, gte: new Date(dateDebutFilter) };
  }
  if (dateFinFilter) {
    // Ajouter 1 jour à la date de fin pour inclure toute la journée
    const endDate = new Date(dateFinFilter);
    endDate.setDate(endDate.getDate() + 1);
    whereClause.timestamp = { ...whereClause.timestamp, lt: endDate };
  }


  try {
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        ...whereClause,
        utilisateurId: utilisateurIdFilter ?
          { equals: parseInt(utilisateurIdFilter) } :
          undefined
      },
      include: {
        utilisateur: {
          select: {
            id: true,
            nomUtilisateur: true,
            nom: true,
            prenom: true,
          },
        },
      },
      orderBy: {
        timestamp: "desc", // Les plus récents en premier
      },
      skip: skip,
      take: limit,
    });

    const totalLogs = await prisma.auditLog.count({
      where: {
        ...whereClause,
        utilisateurId: utilisateurIdFilter ?
          { equals: parseInt(utilisateurIdFilter) } :
          undefined
      }
    });

    return NextResponse.json({
      data: auditLogs,
      totalPages: Math.ceil(totalLogs / limit),
      currentPage: page,
      totalRecords: totalLogs,
    });

  } catch (error) {
    console.error("Erreur lors de la récupération des logs d'audit:", error);
    return NextResponse.json(
      { message: "Erreur interne du serveur lors de la récupération des logs d'audit" },
      { status: 500 }
    );
  }
}