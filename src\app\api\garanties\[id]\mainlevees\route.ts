import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutGarantie, StatutMainlevee, TypeMainlevee } from "@prisma/client";
import { DemandeMainleveeSchema } from "@/lib/schemas/mainlevee.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

interface RouteParams {
  params: { id: string };
}

// POST: Créer une demande de mainlevée pour une garantie
export async function POST(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);

  // Permissions : Partenaire, GestionnaireGesGar, Administrateur
  if (!session || !["Administrateur", "GestionnaireGesGar", "Partenaire"].includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé à demander une mainlevée" }, { status: 403 });
  }

  const idNum = parseInt(resolvedParams.id);
  if (isNaN(idNum)) {
    return NextResponse.json({ message: "ID de garantie invalide." }, { status: 400 });
  }

  const demandeurId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: demandeurId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      // Convertir dateDemande en objet Date si elle arrive en string
      if (body.dateDemande) body.dateDemande = new Date(body.dateDemande);

      const validation = DemandeMainleveeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de demande de mainlevée invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }
      const { typeMainlevee, dateDemande, commentairesDemande } = validation.data;

      // --- Début de la logique transactionnelle ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Vérifier la garantie existante et son statut
        const garantie = await tx.garantie.findUnique({
          where: { id: idNum },
        });
        if (!garantie) {
          throw new Error("Garantie non trouvée.");
        }

        // Si l'utilisateur est un partenaire, il ne peut demander que sur ses propres garanties
        if (session.user.role === "Partenaire") {
          const partenaire = await tx.partenaire.findFirst({
            where: { utilisateurCreationId: demandeurId }
          });
          if (!partenaire || garantie.partenaireId !== partenaire.id) {
            throw new Error("Vous n'êtes pas autorisé à demander une mainlevée sur cette garantie.");
          }
        }

        // Vérification du statut de la garantie
        const statutsPermettantDemandeMainlevee: StatutGarantie[] = [
          StatutGarantie.Active,
          StatutGarantie.EnSouffrance,
          StatutGarantie.Echue,
          StatutGarantie.Validee,
          StatutGarantie.MiseEnJeuAcceptee,
          StatutGarantie.MiseEnJeuPayee,
        ];
        if (!statutsPermettantDemandeMainlevee.includes(garantie.statut)) {
          throw new Error(`Impossible de demander une mainlevée pour une garantie au statut '${garantie.statut}'.`);
        }

        // 2. Vérifier qu'il n'y a pas déjà une demande de mainlevée non refusée pour cette garantie
        const existingMainlevee = await tx.mainlevee.findUnique({
          where: { garantieId: idNum },
        });
        if (existingMainlevee && existingMainlevee.statut !== StatutMainlevee.Refusee) {
          throw new Error(`Une demande de mainlevée existe déjà (Statut: ${existingMainlevee.statut}) ou a été traitée pour cette garantie.`);
        }

        // 3. Créer la demande de mainlevée
        const newMainlevee = await tx.mainlevee.create({
          data: {
            garantieId: idNum,
            typeMainlevee: typeMainlevee as TypeMainlevee,
            dateDemande,
            commentairesDemande,
            statut: StatutMainlevee.Demandee, // Statut initial
            utilisateurCreationId: demandeurId,
          },
        });

        // 4. Mettre à jour le statut de la garantie parente
        await tx.garantie.update({
          where: { id: idNum },
          data: {
            statut: StatutGarantie.MainleveeDemandee,
            utilisateurModificationId: demandeurId,
          },
        });

        return newMainlevee;
      });
      // --- Fin de la logique transactionnelle ---

      return NextResponse.json(result, { status: 201 });

    } catch (error: any) {
      console.error(`Erreur POST /api/garanties/${idNum}/mainlevees:`, error);
      if (error.message && (error.message.includes("non trouvée") || error.message.includes("Impossible de demander") || error.message.includes("existe déjà"))) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      // Gérer l'erreur d'unicité Prisma si on essaie de créer une Mainlevee pour une garantieId qui en a déjà une (non Refusee)
      if (error.code === 'P2002' && error.meta?.target?.includes('garantieId')) {
        return NextResponse.json({ message: "Une demande de mainlevée active ou en cours existe déjà pour cette garantie." }, { status: 409 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la création de la demande de mainlevée." }, { status: 500 });
    }
  });
} 