// src/app/configuration/bailleurs/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur } from "@prisma/client";
import { getBailleurColumns, BailleurColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  BailleurFormValues,
  BailleurSchema,
} from "@/lib/schemas/bailleur.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";

export default function BailleursPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [bailleurs, setBailleurs] = useState<BailleurColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false); // Nouvel état

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingBailleur, setEditingBailleur] = useState<BailleurColumn | null>(
    null
  );
  const [bailleurToDelete, setBailleurToDelete] =
    useState<BailleurColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const form = useForm<BailleurFormValues>({
    resolver: zodResolver(BailleurSchema),
    defaultValues: {
      nom: "",
      description: "",
      contactNomRepresentant: "",
      contactEmail: "",
      contactTelephone: "",
      autresInfoPaysOrigine: "",
      autresInfoTypeOrganisation: "",
      autresInfoSiteWeb: "",
      autresInformationsStr: "",
    },
  });

  const fetchBailleursData = useCallback(
    async (showLoading = true) => {
      if (showLoading) setIsLoading(true);
      try {
        const response: Response = await fetch("/api/configuration/bailleurs");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || "Échec de la récupération des bailleurs"
          );
        }
        const data = await response.json();
        setBailleurs(data);
        setInitialDataLoaded(true); // Marquer que les données initiales sont chargées
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.message,
          variant: "destructive",
        });
        if (showLoading) setInitialDataLoaded(true); // Marquer même en cas d'erreur pour éviter les boucles
      } finally {
        if (showLoading) setIsLoading(false);
      }
    },
    [toast]
  );

  useEffect(() => {
    // Gérer l'état de chargement global basé sur la session
    if (status === "loading") {
      setIsLoading(true);
      return; // Attendre que la session soit chargée
    }

    // Si la session n'est pas chargée (unauthenticated) ou pas le bon rôle
    if (
      !session ||
      (session.user?.role !== RoleUtilisateur.Administrateur &&
        session.user?.role !== RoleUtilisateur.GestionnaireGesGar)
    ) {
      if (status !== "unauthenticated") {
        // N'afficher le toast que si ce n'est pas juste un état initial non authentifié
        toast({ title: "Accès refusé", variant: "destructive" });
      }
      router.replace("/");
      setIsLoading(false); // Arrêter le chargement si redirection
      return;
    }

    // Si la session est chargée, l'utilisateur a les droits, ET les données initiales n'ont pas encore été chargées
    if (session && !initialDataLoaded) {
      fetchBailleursData(); // setIsLoading est géré dans fetchBailleursData
    } else if (session && initialDataLoaded) {
      // Si la session est là et les données déjà chargées, s'assurer que isLoading est false
      // Cela peut arriver si la session change après le chargement initial des données
      setIsLoading(false);
    }
  }, [session, status, initialDataLoaded, router, toast, fetchBailleursData]);

  const refreshData = useCallback(async () => {
    await fetchBailleursData(false); // Ne pas montrer l'indicateur de chargement global pour un simple rafraîchissement
  }, [fetchBailleursData]);

  const handleEdit = useCallback(
    (bailleur: BailleurColumn) => {
      const autresInfos = bailleur.autresInformations as {
        pays_origine?: string;
        type_organisation?: string;
        site_web?: string;
      } | null;
      form.reset({
        nom: bailleur.nom ?? "",
        description: bailleur.description ?? "",
        contactNomRepresentant:
          bailleur.contact &&
          typeof bailleur.contact === "object" &&
          "nomRepresentant" in bailleur.contact
            ? (bailleur.contact as { nomRepresentant?: string })
                .nomRepresentant ?? ""
            : "",
        contactEmail:
          bailleur.contact &&
          typeof bailleur.contact === "object" &&
          "email" in bailleur.contact
            ? (bailleur.contact as { email?: string }).email ?? ""
            : "",
        contactTelephone:
          bailleur.contact &&
          typeof bailleur.contact === "object" &&
          "telephone" in bailleur.contact
            ? (bailleur.contact as { telephone?: string }).telephone ?? ""
            : "",
        autresInfoPaysOrigine: autresInfos?.pays_origine || "",
        autresInfoTypeOrganisation: autresInfos?.type_organisation || "",
        autresInfoSiteWeb: autresInfos?.site_web || "",
      });
      setEditingBailleur(bailleur);
      setIsFormOpen(true);
    },
    [form]
  );

  const handleDeleteConfirm = useCallback((bailleur: BailleurColumn) => {
    setBailleurToDelete(bailleur);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async () => {
    if (!bailleurToDelete) return;
    // ... (logique de suppression) ...
    // À la fin, appeler refreshData() au lieu de fetchBailleursData() directement
    // pour un meilleur contrôle de l'indicateur de chargement.
    try {
      const response: Response = await fetch(
        `/api/configuration/bailleurs/${bailleurToDelete.id}`,
        {
          method: "DELETE",
        }
      );
      if (response.ok) {
        toast({
          title: "Bailleur Supprimé",
          description: `Le bailleur ${bailleurToDelete.nom} a été supprimé avec succès`,
          variant: "default",
        });
        await refreshData();
      } else {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Échec de la suppression du bailleur"
        );
      }
    } catch (error: any) {
      /* ... gestion erreur ... */
    }
    // ...
  }, [bailleurToDelete, toast, refreshData]);

  const onSubmit = useCallback(
    async (values: BailleurFormValues) => {
      try {
        const isEdit = !!editingBailleur;
        const url = isEdit
          ? `/api/configuration/bailleurs/${editingBailleur!.id}`
          : "/api/configuration/bailleurs";
        const method = isEdit ? "PUT" : "POST";
        const response: Response = await fetch(url, {
          method,
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(values),
        });

        if (response.ok) {
          toast({
            title: isEdit ? "Bailleur Mis à Jour" : "Bailleur Créé",
            description: isEdit
              ? `Le bailleur ${values.nom} a été mis à jour avec succès`
              : `Le nouveau bailleur ${values.nom} a été créé avec succès`,
            variant: "default",
          });
          form.reset();
          setIsFormOpen(false);
          await refreshData();
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || "Échec de l'opération");
        }
      } catch (error: any) {
        /* ... gestion erreur ... */
      }
      // ...
    },
    [editingBailleur, form, toast, refreshData]
  );

  const columns = useMemo(
    () =>
      getBailleurColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }),
    [handleEdit, handleDeleteConfirm]
  );

  if (isLoading) {
    return <div className="p-6 text-center">Chargement des bailleurs...</div>;
  }
  // Si la session n'est pas authentifiée après le chargement, ou rôle incorrect (double sécurité)
  if (
    status !== "loading" &&
    (!session ||
      !(
        session.user?.role === RoleUtilisateur.Administrateur ||
        session.user?.role === RoleUtilisateur.GestionnaireGesGar
      ))
  ) {
    return (
      <div className="p-6 text-center">Accès non autorisé. Redirection...</div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des Bailleurs de Fonds</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => {
            setIsFormOpen(open);
            if (!open) { setEditingBailleur(null); form.reset(); }
        }}>
          <DialogTrigger asChild>
            <Button onClick={() => { setEditingBailleur(null); form.reset(); setIsFormOpen(true); }}>
                <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Bailleur
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-xl">
            <DialogHeader>
              <DialogTitle>{editingBailleur ? "Modifier le Bailleur" : "Ajouter un Bailleur"}</DialogTitle>
              {editingBailleur && <DialogDescription>Modification du bailleur : {editingBailleur.nom}</DialogDescription>}
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
                {/* Nom et Description (identiques) */}
                <FormField control={form.control} name="nom" render={({ field }) => ( <FormItem> <FormLabel>Nom du Bailleur <span className="text-red-500">*</span></FormLabel> <FormControl><Input placeholder="Nom officiel du bailleur" {...field} /></FormControl> <FormMessage /> </FormItem> )} />
                <FormField control={form.control} name="description" render={({ field }) => ( <FormItem> <FormLabel>Description</FormLabel> <FormControl><Textarea placeholder="Courte description du bailleur, ses missions, etc." {...field} /></FormControl> <FormMessage /> </FormItem> )} />

                <h3 className="text-lg font-medium border-b pb-2 pt-2">Informations de Contact</h3>
                {/* Champs de contact (identiques) */}
                <FormField control={form.control} name="contactNomRepresentant" render={({ field }) => ( <FormItem><FormLabel>Nom du Représentant</FormLabel><FormControl><Input placeholder="Prénom Nom" {...field} /></FormControl><FormMessage /></FormItem> )} />
                <FormField control={form.control} name="contactEmail" render={({ field }) => ( <FormItem><FormLabel>Email de Contact</FormLabel><FormControl><Input type="email" placeholder="<EMAIL>" {...field} /></FormControl><FormMessage /></FormItem> )} />
                <FormField control={form.control} name="contactTelephone" render={({ field }) => ( <FormItem><FormLabel>Téléphone de Contact</FormLabel><FormControl><Input placeholder="+XXX XX XXX XXX" {...field} /></FormControl><FormMessage /></FormItem> )} />

                {/* MODIFICATION ICI pour Autres Informations */}
                <h3 className="text-lg font-medium border-b pb-2 pt-2">Autres Informations Détaillées</h3>
                <FormField control={form.control} name="autresInfoPaysOrigine" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Pays d'Origine</FormLabel>
                        <FormControl><Input placeholder="Ex: France, Sénégal" {...field} /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                 <FormField control={form.control} name="autresInfoTypeOrganisation" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Type d'Organisation</FormLabel>
                        {/* Vous pourriez utiliser un <Select> ici si vous avez une liste prédéfinie */}
                        <FormControl><Input placeholder="Ex: Agence de développement, Banque multilatérale" {...field} /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                 <FormField control={form.control} name="autresInfoSiteWeb" render={({ field }) => (
                    <FormItem>
                        <FormLabel>Site Web</FormLabel>
                        <FormControl><Input type="url" placeholder="https://www.example.com" {...field} /></FormControl>
                        <FormMessage />
                    </FormItem>
                )} />
                {/* L'ancien champ autresInformationsStr est supprimé du formulaire */}

                <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={form.formState.isSubmitting}>
                        {form.formState.isSubmitting ? "Sauvegarde..." : (editingBailleur ? "Mettre à Jour" : "Créer le Bailleur")}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={bailleurs} />
      {bailleurToDelete && (
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
              <AlertDialogDescription>
                Êtes-vous sûr de vouloir supprimer le bailleur "
                {bailleurToDelete.nom}" ?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Annuler</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>
                Supprimer
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}
