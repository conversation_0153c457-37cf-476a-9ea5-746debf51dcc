// src/lib/schemas/bailleur.schema.ts
import { z } from "zod";

// Schéma pour l'objet contact (peut être affiné)
const ContactSchema = z
  .object({
    nomRepresentant: z.string().optional().or(z.literal("")),
    email: z
      .string()
      .email({ message: "Email de contact invalide." })
      .optional()
      .or(z.literal("")),
    telephone: z.string().optional().or(z.literal("")),
  })
  .optional(); // Le contact lui-même est optionnel

// Schéma pour autresInformations (très générique, peut être affiné)
// Pour l'instant, on s'attend à un objet, mais on ne valide pas ses clés internes.
// Ou, si c'est juste du texte libre, utiliser z.string().optional()
const AutresInformationsSchema = z.record(z.any()).optional(); // Permet n'importe quel objet JSON
// Alternative si c'est juste du texte:
// const AutresInformationsSchema = z.string().optional();

export const BailleurSchema = z.object({
  nom: z
    .string()
    .min(2, "Le nom du bailleur doit contenir au moins 2 caractères.")
    .max(150),
  description: z.string().optional().or(z.literal("")),
  // Pour les champs JSON, nous allons les gérer comme des chaînes dans le formulaire
  // et les parser/stringifier au niveau de l'API ou du composant.
  // Ou, si vous avez des champs de formulaire dédiés pour chaque propriété de contact:
  contactNomRepresentant: z.string().optional().or(z.literal("")),
  contactEmail: z
    .string()
    .email({ message: "Email de contact invalide." })
    .optional()
    .or(z.literal("")),
  contactTelephone: z.string().optional().or(z.literal("")),
  // Pour 'autresInformations', si c'est un JSON complexe, un textarea est souvent utilisé.
  autresInfoPaysOrigine: z.string().optional().or(z.literal("")),
  autresInfoTypeOrganisation: z.string().optional().or(z.literal("")), // Pourrait être un z.enum si vous avez une liste fixe
  autresInfoSiteWeb: z
    .string()
    .url({ message: "URL du site web invalide." })
    .optional()
    .or(z.literal("")),
  autresInformationsStr: z.string().optional().or(z.literal("")), // Champ pour stocker les autres infos sous forme JSON
});

export type BailleurFormValues = z.infer<typeof BailleurSchema>;
