// src/app/configuration/secteurs-activite/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, SecteurActivite } from "@prisma/client";
import { getSecteurActiviteColumns, SecteurActiviteColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose
} from "@/components/ui/dialog";
import { SecteurActiviteFormValues, SecteurActiviteSchema } from "@/lib/schemas/secteur-activite.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";

export default function SecteursActivitePage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [secteurs, setSecteurs] = useState<SecteurActiviteColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingSecteur, setEditingSecteur] = useState<SecteurActiviteColumn | null>(null);
  const [secteurToDelete, setSecteurToDelete] = useState<SecteurActiviteColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const form = useForm<SecteurActiviteFormValues>({
    resolver: zodResolver(SecteurActiviteSchema),
    defaultValues: { nom: "", code: "", description: "" },
  });

  const fetchSecteursData = useCallback(async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    try {
      const response = await fetch("/api/configuration/secteurs-activite");
      if (!response.ok) throw new Error("Échec de la récupération des secteurs");
      const data = await response.json();
      setSecteurs(data);
      if (showLoading) setInitialDataLoaded(true);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      if (showLoading) setInitialDataLoaded(true);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    if (sessionStatus === "loading") { setIsLoading(true); return; }
    if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/");
      setIsLoading(false);
      return;
    }
    if (session && !initialDataLoaded) { fetchSecteursData(); }
    else if (session && initialDataLoaded) { setIsLoading(false); }
  }, [session, sessionStatus, initialDataLoaded, router, toast, fetchSecteursData]);

  const refreshData = useCallback(async () => { await fetchSecteursData(false); }, [fetchSecteursData]);

  const handleEdit = useCallback((secteur: SecteurActiviteColumn) => {
    setEditingSecteur(secteur);
    form.reset({
      nom: secteur.nom,
      code: secteur.code || "",
      description: secteur.description || "",
    });
    setIsFormOpen(true);
  }, [form]);

  const handleDeleteConfirm = useCallback((secteur: SecteurActiviteColumn) => {
    setSecteurToDelete(secteur);
    setIsDeleteDialogOpen(true);
  }, []);

  const handleDelete = useCallback(async () => {
    if (!secteurToDelete) return;
    try {
      const response = await fetch(`/api/configuration/secteurs-activite/${secteurToDelete.id}`, { method: "DELETE" });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Échec de la suppression");
      }
      toast({ title: "Secteur Supprimé", description: `Le secteur ${secteurToDelete.nom} a été supprimé.` });
      await refreshData();
    } catch (error: any) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } finally {
      setIsDeleteDialogOpen(false);
      setSecteurToDelete(null);
    }
  }, [secteurToDelete, toast, refreshData]);

  const onSubmit = useCallback(async (values: SecteurActiviteFormValues) => {
    const url = editingSecteur ? `/api/configuration/secteurs-activite/${editingSecteur.id}` : "/api/configuration/secteurs-activite";
    const method = editingSecteur ? "PUT" : "POST";
    try {
      const response = await fetch(url, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.errors) {
            Object.keys(errorData.errors).forEach((key) => {
                form.setError(key as keyof SecteurActiviteFormValues, { type: "server", message: errorData.errors[key].join(', ') });
            });
        }
        throw new Error(errorData.message || (editingSecteur ? "Échec de la mise à jour" : "Échec de la création"));
      }
      toast({ title: editingSecteur ? "Secteur Mis à Jour" : "Secteur Créé" });
      setIsFormOpen(false);
      setEditingSecteur(null);
      form.reset();
      await refreshData();
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [editingSecteur, form, toast, refreshData]);

  const columns = useMemo(() => getSecteurActiviteColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }), [handleEdit, handleDeleteConfirm]);

  if (isLoading) return <div className="p-6 text-center">Chargement des secteurs d'activité...</div>;
  if (sessionStatus !== "loading" && (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string))) {
    return <div className="p-6 text-center">Accès non autorisé. Redirection...</div>;
  }

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des Secteurs d'Activité</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => {
            setIsFormOpen(open);
            if (!open) { setEditingSecteur(null); form.reset(); }
        }}>
          <DialogTrigger asChild>
            <Button onClick={() => { setEditingSecteur(null); form.reset(); setIsFormOpen(true); }}>
                <PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Secteur
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>{editingSecteur ? "Modifier le Secteur" : "Ajouter un Secteur d'Activité"}</DialogTitle>
              {editingSecteur && <DialogDescription>Modification du secteur : {editingSecteur.nom}</DialogDescription>}
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
                <FormField control={form.control} name="nom" render={({ field }) => (
                    <FormItem> <FormLabel>Nom du Secteur <span className="text-red-500">*</span></FormLabel> <FormControl><Input placeholder="Ex: Agriculture, Élevage" {...field} /></FormControl> <FormMessage /> </FormItem>
                )} />
                <FormField control={form.control} name="code" render={({ field }) => (
                    <FormItem> <FormLabel>Code (Optionnel)</FormLabel> <FormControl><Input placeholder="Ex: AGRI, ELEV" {...field} /></FormControl> <FormDescription>Code normalisé du secteur.</FormDescription> <FormMessage /> </FormItem>
                )} />
                <FormField control={form.control} name="description" render={({ field }) => (
                    <FormItem> <FormLabel>Description</FormLabel> <FormControl><Textarea placeholder="Description détaillée du secteur..." {...field} /></FormControl> <FormMessage /> </FormItem>
                )} />
                <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={form.formState.isSubmitting}>
                        {form.formState.isSubmitting ? "Sauvegarde..." : (editingSecteur ? "Mettre à Jour" : "Créer le Secteur")}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable columns={columns} data={secteurs} />

      {secteurToDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Supprimer le Secteur ?</AlertDialogTitle>
                    <AlertDialogDescription>
                        Êtes-vous sûr de vouloir supprimer le secteur <span className="font-semibold">{secteurToDelete.nom}</span> ?
                        Cette action est irréversible et ne sera possible que si le secteur n'est lié à aucun projet ou client.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setSecteurToDelete(null)}>Annuler</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">Oui, Supprimer</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}