// Script de diagnostic pour le bouton de mainlevée
// À exécuter dans la console du navigateur sur la page des garanties

console.log("=== DIAGNOSTIC BOUTON MAINLEVÉE ===");

// 1. Vérifier les statuts des garanties dans le tableau
const garantieRows = document.querySelectorAll('[data-testid="data-table"] tbody tr');
console.log(`Nombre de garanties trouvées: ${garantieRows.length}`);

garantieRows.forEach((row, index) => {
  const statutCell = row.querySelector('td:nth-child(7)'); // Colonne statut
  const actionsCell = row.querySelector('td:last-child'); // Colonne actions
  const mainleveeButton = actionsCell?.querySelector('[title="Demander Mainlevée"]');
  
  if (statutCell) {
    const statut = statutCell.textContent?.trim();
    console.log(`Garantie ${index + 1}:`);
    console.log(`  - Statut: ${statut}`);
    console.log(`  - Bouton mainlevée présent: ${mainleveeButton ? 'OUI' : 'NON'}`);
    
    // Vérifier si le statut permet la mainlevée
    const statutsPermettantMainlevee = [
      'Active',
      'EnSouffrance', 
      'Echue',
      'Validee',
      'MiseEnJeuAcceptee',
      'MiseEnJeuPayee'
    ];
    
    const peutDemanderMainlevee = statutsPermettantMainlevee.includes(statut) 
      && statut !== 'MainleveeDemandee' 
      && statut !== 'MainleveeAccordee';
      
    console.log(`  - Devrait avoir bouton mainlevée: ${peutDemanderMainlevee ? 'OUI' : 'NON'}`);
    
    if (peutDemanderMainlevee && !mainleveeButton) {
      console.warn(`  ⚠️ PROBLÈME: Garantie ${index + 1} devrait avoir un bouton mainlevée mais ne l'a pas!`);
    }
  }
});

// 2. Vérifier la session utilisateur
console.log("\n=== VÉRIFICATION SESSION ===");
console.log("Session utilisateur:", window.next?.router?.query || "Non accessible depuis la console");

// 3. Vérifier les erreurs JavaScript
console.log("\n=== VÉRIFICATION ERREURS ===");
console.log("Vérifiez la console pour d'éventuelles erreurs JavaScript qui pourraient empêcher le rendu des boutons.");

// 4. Instructions pour l'utilisateur
console.log("\n=== INSTRUCTIONS ===");
console.log("1. Vérifiez que vous êtes connecté en tant qu'Administrateur");
console.log("2. Vérifiez qu'au moins une garantie a le statut 'Active'");
console.log("3. Si le problème persiste, partagez les résultats de ce diagnostic");