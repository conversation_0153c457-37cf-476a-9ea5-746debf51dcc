// src/app/api/admin/system-settings/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeParametre } from "@prisma/client";
import { SystemSettingSchema, validateSettingValue } from "@/lib/schemas/system-setting.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

interface RouteParams { params: { id: string } }

// GET: Récupérer un paramètre spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  // ... (logique GET similaire à celle des utilisateurs, mais pour ParametreSysteme)
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const adminId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: adminId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const setting = await prisma.parametreSysteme.findUnique({ where: { id }});
      if (!setting) return NextResponse.json({ message: "Paramètre non trouvé" }, { status: 404 });
      return NextResponse.json(setting);
    } catch (error) {
      console.error(`Erreur GET /api/admin/system-settings/${id}:`, error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}


// PUT: Mettre à jour un paramètre système
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) {
    return NextResponse.json({ message: "ID de paramètre invalide" }, { status: 400 });
  }

  const adminId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: adminId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      // Pour la mise à jour, la clé n'est généralement pas modifiable, donc on peut l'exclure de la validation
      // ou utiliser un schéma Zod différent pour la mise à jour.
      const validation = SystemSettingSchema.omit({ cle: true }).safeParse(body); // Omettre 'cle' pour la validation de mise à jour

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { valeur, description, typeValeur, estModifiable } = validation.data;

      // Validation supplémentaire de la valeur en fonction du type
      const valueValidationResult = validateSettingValue(valeur, typeValeur as TypeParametre);
      if (typeof valueValidationResult === 'string') {
          return NextResponse.json({ message: "Données invalides", errors: { valeur: [valueValidationResult] } }, { status: 400 });
      }

      const settingToUpdate = await prisma.parametreSysteme.findUnique({ where: { id } });
      if (!settingToUpdate) {
        return NextResponse.json({ message: "Paramètre non trouvé." }, { status: 404 });
      }
      if (!settingToUpdate.estModifiable) {
        return NextResponse.json({ message: "Ce paramètre n'est pas modifiable." }, { status: 403 });
      }

      const updatedSetting = await prisma.parametreSysteme.update({
        where: { id },
        data: {
          valeur,
          description,
          typeValeur: typeValeur as TypeParametre,
          estModifiable,
          utilisateurModificationId: adminId, // Si vous tracez
        },
      });
      return NextResponse.json(updatedSetting);
    } catch (error: any) {
      console.error(`Erreur PUT /api/admin/system-settings/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Paramètre non trouvé" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// DELETE: Supprimer un paramètre système (si autorisé)
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || session.user?.role !== RoleUtilisateur.Administrateur) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) {
    return NextResponse.json({ message: "ID de paramètre invalide" }, { status: 400 });
  }

  const adminId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: adminId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const settingToDelete = await prisma.parametreSysteme.findUnique({ where: { id } });
      if (!settingToDelete) {
        return NextResponse.json({ message: "Paramètre non trouvé." }, { status: 404 });
      }
      // Optionnel: Vérifier si le paramètre est essentiel et ne peut pas être supprimé
      // if (!settingToDelete.estModifiable) { // Ou un autre champ 'estSupprimable'
      //   return NextResponse.json({ message: "Ce paramètre ne peut pas être supprimé." }, { status: 403 });
      // }

      await prisma.parametreSysteme.delete({ where: { id } });
      return NextResponse.json({ message: "Paramètre supprimé avec succès" }, { status: 200 }); // ou 204
    } catch (error: any) {
      console.error(`Erreur DELETE /api/admin/system-settings/${id}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Paramètre non trouvé" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}