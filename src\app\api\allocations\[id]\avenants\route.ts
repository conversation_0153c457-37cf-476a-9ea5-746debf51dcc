// src/app/api/allocations/[id]/avenants/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeAvenant, StatutLigneGarantie, StatutAllocation, StatutGarantie } from "@prisma/client";
import { AvenantAllocationSchema } from "@/lib/schemas/avenant-allocation.schema";
import { auditContext } from "@/lib/prisma-audit.middleware";
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams { params: { id: string } }

// POST: Créer un nouvel avenant pour une allocation
export async function POST(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const allocationIdNum = parseInt(resolvedParams.id);
  if (isNaN(allocationIdNum)) {
    return NextResponse.json({ message: "ID d'allocation invalide." }, { status: 400 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  
  // Headers pour audit
  const headersList = await headers();
  const ip = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';

  return auditContext.run({ userId: creatorId, ip, userAgent }, async () => {
    try {
      const body = await request.json();
      if (body.dateAvenant) body.dateAvenant = new Date(body.dateAvenant);
      if (body.nouvelleDateExpiration) body.nouvelleDateExpiration = new Date(body.nouvelleDateExpiration);

      const validation = AvenantAllocationSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données d'avenant d'allocation invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        typeAvenant, montantModificationStr, nouvelleDateExpiration,
        dateAvenant, raison, referenceDocument
      } = validation.data;

      // --- Début Transaction ---
      const newAvenant = await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'allocation parente et sa ligne de garantie grand-parente
        const allocation = await tx.allocationLignePartenaire.findUnique({
          where: { id: allocationIdNum },
          include: {
            ligneGarantie: true,
            garanties: { select: { montantGarantie: true, statut: true } } // Pour recalculer montantDisponible de l'allocation
          }
        });
        if (!allocation) throw new Error("Allocation parente non trouvée.");
        if (!allocation.ligneGarantie) throw new Error("Ligne de garantie grand-parente non trouvée.");

        // Vérifier le statut de l'allocation et de la ligne
        if (allocation.statut === StatutAllocation.Cloturee || allocation.statut === StatutAllocation.Expiree) {
            throw new Error(`Impossible d'ajouter un avenant à une allocation au statut '${allocation.statut}'.`);
        }
        if (allocation.ligneGarantie.statut === StatutLigneGarantie.Cloturee || allocation.ligneGarantie.statut === StatutLigneGarantie.Expiree) {
            throw new Error(`Impossible d'ajouter un avenant car la ligne de garantie parente est '${allocation.ligneGarantie.statut}'.`);
        }

        // 2. Préparer les données de mise à jour pour l'allocation et la ligne
        const allocationUpdateData: any = {};
        const ligneGarantieUpdateData: any = {};
        let montantModificationDecimal: Decimal | null = null;

        if (typeAvenant === TypeAvenant.AUGMENTATION_MONTANT || typeAvenant === TypeAvenant.REDUCTION_MONTANT) {
          if (!montantModificationStr) throw new Error("Montant de modification requis.");
          montantModificationDecimal = new Decimal(montantModificationStr.replace(',', '.'));

          const newMontantAlloueAllocation = allocation.montantAlloue.add(
            typeAvenant === TypeAvenant.AUGMENTATION_MONTANT ? montantModificationDecimal : montantModificationDecimal.negated()
          );
          if (newMontantAlloueAllocation.isNegative()) throw new Error("Le montant alloué de l'allocation ne peut pas devenir négatif.");

          // Vérifier la disponibilité sur la ligne de garantie grand-parente
          if (typeAvenant === TypeAvenant.AUGMENTATION_MONTANT) {
            if (montantModificationDecimal.greaterThan(allocation.ligneGarantie.montantDisponible)) {
              throw new Error(`Augmentation (${montantModificationDecimal.toFixed(2)}) dépasse le disponible (${allocation.ligneGarantie.montantDisponible.toFixed(2)}) sur la ligne de garantie.`);
            }
            ligneGarantieUpdateData.montantDisponible = allocation.ligneGarantie.montantDisponible.sub(montantModificationDecimal);
          } else { // REDUCTION_MONTANT sur l'allocation restitue à la ligne
            ligneGarantieUpdateData.montantDisponible = allocation.ligneGarantie.montantDisponible.add(montantModificationDecimal.abs());
          }
          allocationUpdateData.montantAlloue = newMontantAlloueAllocation;
        }

        if (typeAvenant === TypeAvenant.PROLONGATION_DUREE && nouvelleDateExpiration) {
          if (allocation.dateExpiration && nouvelleDateExpiration <= allocation.dateExpiration) throw new Error("La nouvelle date d'expiration doit être postérieure à la date actuelle.");
          if (nouvelleDateExpiration <= dateAvenant) throw new Error("La nouvelle date d'expiration doit être postérieure à la date de l'avenant.");
          // Vérifier aussi par rapport à la date d'expiration de la ligne de garantie parente
          if (nouvelleDateExpiration > allocation.ligneGarantie.dateExpiration) {
              throw new Error("La nouvelle date d'expiration de l'allocation ne peut pas dépasser celle de la ligne de garantie parente.");
          }
          allocationUpdateData.dateExpiration = nouvelleDateExpiration;
        }

        // 3. Recalculer le montant disponible de l'allocation
        const totalGarantiesActivesSurAllocation = allocation.garanties
          .filter(g => ["Active", "EnSouffrance", "MiseEnJeuDemandee", "MiseEnJeuAcceptee"].includes(g.statut))
          .reduce((sum, g) => sum.add(g.montantGarantie), new Decimal(0));

        const montantAllouePourCalculDispo = allocationUpdateData.montantAlloue || allocation.montantAlloue;
        allocationUpdateData.montantDisponible = montantAllouePourCalculDispo.sub(totalGarantiesActivesSurAllocation);

        if (allocationUpdateData.montantDisponible && allocationUpdateData.montantDisponible.isNegative()) {
          throw new Error("La réduction du montant alloué rendrait le disponible de l'allocation négatif à cause des garanties existantes. Veuillez d'abord ajuster ou clôturer les garanties actives.");
        }

        // 4. Créer l'avenant d'allocation
        const createdAvenant = await tx.avenantAllocation.create({
          data: {
            allocationId: allocationIdNum,
            typeAvenant: typeAvenant as TypeAvenant,
            montantModification: montantModificationDecimal,
            nouvelleDateExpiration,
            dateAvenant,
            raison,
            referenceDocument,
            utilisateurCreationId: creatorId,
          },
        });

        // 5. Mettre à jour l'allocation parente
        if (Object.keys(allocationUpdateData).length > 0) {
            await tx.allocationLignePartenaire.update({
                where: { id: allocationIdNum },
                data: { ...allocationUpdateData, utilisateurModificationId: creatorId },
            });
        }

        // 6. Mettre à jour la ligne de garantie grand-parente (si son montant disponible a changé)
        if (Object.keys(ligneGarantieUpdateData).length > 0) {
            await tx.ligneGarantie.update({
                where: { id: allocation.ligneGarantieId },
                data: { ...ligneGarantieUpdateData, utilisateurModificationId: creatorId },
            });
        }
        return createdAvenant;
      });
      // --- Fin Transaction ---
      return NextResponse.json(newAvenant, { status: 201 });

    } catch (error: any) {
      console.error(`Erreur POST /api/allocations/${allocationIdNum}/avenants:`, error);
      if (error.message.includes("non trouvée") || error.message.includes("dépasse le disponible") || error.message.includes("ne peut pas devenir négatif") || error.message.includes("doit être postérieure") || error.message.includes("n'est pas dans un statut permettant")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la création de l'avenant d'allocation." }, { status: 500 });
    }
  });
}

// TODO: GET pour lister les avenants d'une allocation
// export async function GET(request: Request, { params }: RouteParams) {
//   const resolvedParams = await params;
//   // ... implementation
// }