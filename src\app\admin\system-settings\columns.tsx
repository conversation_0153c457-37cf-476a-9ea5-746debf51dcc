// src/app/admin/system-settings/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ParametreSysteme, TypeParametre, Utilisateur } from "@prisma/client";
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox"; // Pour estModifiable

export type SystemSettingColumn = ParametreSysteme & {
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
    utilisateurModification?: Pick<Utilisateur, "nomUtilisateur"> | null;
}; // Type pour les données enrichies

interface SystemSettingsColumnsProps {
    onEdit: (setting: SystemSettingColumn) => void;
    onDelete: (setting: SystemSettingColumn) => void;
}

export const getSystemSettingsColumns = ({ onEdit, onDelete }: SystemSettingsColumnsProps): ColumnDef<SystemSettingColumn>[] => [
  {
    accessorKey: "cle",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Clé <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => <span className="font-mono">{row.getValue("cle")}</span>,
  },
  {
    accessorKey: "valeur",
    header: "Valeur",
    cell: ({ row }) => {
        const valeur = row.getValue("valeur") as string;
        const type = row.original.typeValeur;
        if (type === TypeParametre.BOOLEAN) {
            return valeur.toLowerCase() === 'true' ? <CheckCircle className="h-5 w-5 text-green-600" /> : <XCircle className="h-5 w-5 text-red-600" />;
        }
        if (type === TypeParametre.TEXT || type === TypeParametre.JSON) {
            return <span className="text-xs truncate block max-w-xs" title={valeur}>{valeur.substring(0,60) + (valeur.length > 60 ? "..." : "")}</span>;
        }
        return <span className="truncate block max-w-xs">{valeur}</span>;
    }
  },
  {
    accessorKey: "typeValeur",
    header: "Type",
    cell: ({ row }) => <Badge variant="outline">{row.getValue("typeValeur")}</Badge>,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => <span className="text-sm text-muted-foreground truncate block max-w-md">{row.getValue("description") || "-"}</span>,
  },
  {
    accessorKey: "estModifiable",
    header: "Modifiable",
    cell: ({ row }) => (
        <div className="flex items-center justify-center">
            <Checkbox checked={row.getValue("estModifiable")} disabled className="cursor-default" />
        </div>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const setting = row.original;
      return (
        <div className="flex space-x-2 justify-end">
          <Button variant="ghost" size="icon" onClick={() => onEdit(setting)} title="Modifier">
            <Edit className="h-4 w-4" />
          </Button>
          {setting.estModifiable && ( // Ou une autre condition pour la suppression
            <Button variant="ghost" size="icon" onClick={() => onDelete(setting)} className="text-red-600 hover:text-red-700" title="Supprimer">
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      );
    },
  },
];