# Diagnostic et Correction du Bouton de Mainlevée

## Problème Identifié
Le bouton "Demander Mainlevée" n'apparaissait pas dans le tableau des garanties.

## Causes Identifiées

### 1. Désynchronisation des Enums TypeScript et Prisma
- L'enum `StatutGarantie` dans `/src/types/enums.ts` n'était pas synchronisé avec l'enum Prisma
- Le statut `Validee` était référencé mais n'existait pas dans le schéma Prisma
- Le statut `Transferree` existait dans Prisma mais pas dans l'enum TypeScript

### 2. Statuts Manquants
- Plusieurs statuts du schéma Prisma n'étaient pas inclus dans l'enum TypeScript

## Corrections Apportées

### 1. Mise à Jour de l'Enum TypeScript (`src/types/enums.ts`)
```typescript
export enum StatutGarantie {
  EnInstruction = "EnInstruction",
  Echue = "Echue", 
  Active = "Active",
  MainleveeDemandee = "MainleveeDemandee",
  MainleveeAccordee = "MainleveeAccordee",
  MainleveeRefusee = "MainleveeRefusee",
  Transferree = "Transferree",
  EnSouffrance = "EnSouffrance",
  MiseEnJeuDemandee = "MiseEnJeuDemandee",
  MiseEnJeuAcceptee = "MiseEnJeuAcceptee",
  MiseEnJeuPayee = "MiseEnJeuPayee",
  MiseEnJeuRefusee = "MiseEnJeuRefusee",
  ClotureeAnormalement = "ClotureeAnormalement",
  Radiee = "Radiee",
  Supprimee = "Supprimee",
}
```

### 2. Correction des Statuts Autorisés pour Mainlevée
Dans `src/app/garanties/columns.tsx` et `src/app/garanties/page.tsx` :
```typescript
const statutsPermettantDemandeMainlevee = [
  StatutGarantie.Active,
  StatutGarantie.EnSouffrance,
  StatutGarantie.Echue,
  StatutGarantie.MiseEnJeuAcceptee,
  StatutGarantie.MiseEnJeuPayee,
  StatutGarantie.Transferree,
];
```

### 3. Amélioration du Debug
Ajout de logs détaillés dans `src/app/garanties/columns.tsx` pour diagnostiquer les problèmes :
- Affichage du statut brut et de son type
- Vérification de l'inclusion dans les statuts autorisés
- Résultat final de la condition

## Scripts de Diagnostic Créés

### 1. `debug-mainlevee.js`
Script basique pour vérifier la présence des boutons dans le DOM.

### 2. `test-mainlevee-button.js`
Script complet pour :
- Tester la logique de détermination du bouton
- Analyser les garanties réelles dans le tableau
- Fournir un rapport détaillé

## Comment Utiliser les Scripts de Diagnostic

1. **Ouvrir la page des garanties** : `http://localhost:3000/garanties`
2. **Ouvrir la console du navigateur** (F12)
3. **Copier-coller le contenu de `test-mainlevee-button.js`**
4. **Analyser les résultats**

## Conditions pour que le Bouton Apparaisse

1. **Statut de la garantie** doit être l'un des suivants :
   - `Active`
   - `EnSouffrance`
   - `Echue`
   - `MiseEnJeuAcceptee`
   - `MiseEnJeuPayee`
   - `Transferree`

2. **Statut de la garantie** ne doit PAS être :
   - `MainleveeDemandee`
   - `MainleveeAccordee`

3. **Utilisateur connecté** doit avoir les permissions appropriées

## Test de Fonctionnement

1. **Créer une garantie** avec le statut `Active`
2. **Vérifier** que le bouton mainlevée (icône poignée de main verte) apparaît
3. **Cliquer** sur le bouton pour ouvrir le formulaire de demande de mainlevée
4. **Tester** la soumission du formulaire

## Fichiers Modifiés

- `src/types/enums.ts` - Synchronisation des enums
- `src/app/garanties/columns.tsx` - Correction des statuts et amélioration du debug
- `src/app/garanties/page.tsx` - Correction des statuts
- `debug-mainlevee.js` - Script de diagnostic basique
- `test-mainlevee-button.js` - Script de diagnostic complet

## Prochaines Étapes

1. Tester le bouton de mainlevée avec une garantie ayant le statut `Active`
2. Vérifier que le formulaire de demande de mainlevée s'ouvre correctement
3. Tester la soumission du formulaire
4. Vérifier que la demande est bien enregistrée en base de données