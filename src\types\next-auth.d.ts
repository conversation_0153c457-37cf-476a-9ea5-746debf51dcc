import { RoleUtilisateur } from "@prisma/client";
import <PERSON><PERSON><PERSON>, { DefaultSession, DefaultUser, JWT as DefaultJWT } from "next-auth";

declare module "next-auth" {
  /**
   * Returned by `authOptions.callbacks.session`
   */
  interface Session extends DefaultSession {
    user: {
      id: string; // User ID from Prisma (converted to string)
      role: string; // RoleUtilisateur from Prisma, ensured to be string for session
    } & DefaultSession["user"]; // Extends the default session user (name, email, image)
  }

  /**
   * The `user` object passed to the `jwt` callback and returned by the `authorize` callback.
   */
  interface User extends DefaultUser {
    id: string; // User ID from Prisma (converted to string)
    role: RoleUtilisateur; // Role from Prisma
    // name and email are already part of DefaultUser if returned by authorize
  }
}

declare module "next-auth/jwt" {
  /**
   * Returned by the `jwt` callback and used as the `token` in the `session` callback.
   */
  interface JWT extends DefaultJWT {
    id: string; // User ID from Prisma (converted to string)
    role: RoleUtilisateur; // Role from Prisma
    // name, email, picture are common fields in JWT
  }
}
