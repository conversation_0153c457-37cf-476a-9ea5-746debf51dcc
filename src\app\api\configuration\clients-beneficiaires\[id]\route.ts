// src/app/api/configuration/clients-beneficiaires/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeClient, GenreClient } from "@prisma/client";
import { ClientBeneficiaireSchema } from "@/lib/schemas/client-beneficiaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

interface RouteParams { params: { id: string } }

// GET: Récupérer un client spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  // ... (similaire à GET pour les autres entités, inclure secteurActivitePrincipal)
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) { /* ... */ }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });
  try {
    const client = await prisma.clientBeneficiaire.findUnique({
        where: { id },
        include: { secteurActivitePrincipal: { select: { id: true, nom: true } } }
    });
    if (!client) return NextResponse.json({ message: "Client non trouvé" }, { status: 404 });
    return NextResponse.json(client);
  } catch (error) { /* ... */ }
}

// PUT: Mettre à jour un client
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) { /* ... */ }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });
  const modifierId = session && session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const body = await request.json();
      const validation = ClientBeneficiaireSchema.safeParse(body);
      if (!validation.success) return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });

      const {
        nomOuRaisonSociale, typeClient, identifiantUnique,
        contactAdressePhysique, contactEmail, contactTelephone1, contactTelephone2,
        secteurActivitePrincipalId, age, genre, autresInformationsStr
      } = validation.data;

      const informationsContactJson = { /* ... (comme dans POST) ... */ };
      const autresInformationsJson = null; /* ... (comme dans POST) ... */
      if (autresInformationsStr && autresInformationsStr.trim() !== "") { /* ... */ }


      if (identifiantUnique && identifiantUnique.trim() !== "") {
        const existingIdentifiant = await prisma.clientBeneficiaire.findFirst({ where: { identifiantUnique, id: { not: id } } });
        if (existingIdentifiant) return NextResponse.json({ message: "Cet identifiant unique est déjà utilisé." }, { status: 409 });
      }
      let secteurId: number | null = null;
      if (secteurActivitePrincipalId && secteurActivitePrincipalId.trim() !== "") {
        secteurId = parseInt(secteurActivitePrincipalId);
        const secteurExists = await prisma.secteurActivite.findUnique({ where: { id: secteurId } });
        if (!secteurExists) return NextResponse.json({ message: "Secteur d'activité principal non valide." }, { status: 400 });
      }


      const updatedClient = await prisma.clientBeneficiaire.update({
        where: { id },
        data: {
          nomOuRaisonSociale,
          typeClient: typeClient as TypeClient,
          identifiantUnique: (identifiantUnique && identifiantUnique.trim() !== "") ? identifiantUnique : null,
          age: (age && age.trim() !== "") ? parseInt(age) : null,
          genre: genre ? genre as GenreClient : null,
          informationsContact: informationsContactJson,
          secteurActivitePrincipalId: secteurId, // Peut être null si la chaîne est vide
          autresInformations: autresInformationsJson ?? undefined,
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedClient);
    } catch (error: any) {
      // ... (gestion erreur P2025, etc.)
    }
  });
}

// DELETE: Supprimer un client
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) { /* ... */ }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });
  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      // Vérifier si le client est lié à des Projets ou Garanties
      const projetsCount = await prisma.projet.count({ where: { clientBeneficiaireId: id } });
      if (projetsCount > 0) return NextResponse.json({ message: `Impossible de supprimer: lié à ${projetsCount} projet(s).` }, { status: 400 });
      const garantiesCount = await prisma.garantie.count({ where: { clientBeneficiaireId: id } });
      if (garantiesCount > 0) return NextResponse.json({ message: `Impossible de supprimer: lié à ${garantiesCount} garantie(s).` }, { status: 400 });

      await prisma.clientBeneficiaire.delete({ where: { id } });
      return NextResponse.json({ message: "Client bénéficiaire supprimé avec succès" }, { status: 200 });
    } catch (error: any) {
      // ... (gestion erreur P2025, P2003, etc.)
    }
  });
}