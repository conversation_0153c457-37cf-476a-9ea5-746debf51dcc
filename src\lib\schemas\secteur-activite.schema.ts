// src/lib/schemas/secteur-activite.schema.ts
import { z } from "zod";

export const SecteurActiviteSchema = z.object({
  nom: z.string().min(2, "Le nom du secteur doit contenir au moins 2 caractères.").max(150),
  code: z.string().max(50, "Le code ne doit pas dépasser 50 caractères.").optional().or(z.literal('')), // Optionnel
  description: z.string().optional().or(z.literal('')),
});

export type SecteurActiviteFormValues = z.infer<typeof SecteurActiviteSchema>;