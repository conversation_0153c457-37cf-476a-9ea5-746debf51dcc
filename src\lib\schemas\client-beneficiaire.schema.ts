// src/lib/schemas/client-beneficiaire.schema.ts
import { z } from "zod";
import { TypeClient, GenreClient } from "@prisma/client";

const typeClientValues = Object.values(TypeClient) as [string, ...string[]];
const genreClientValues = Object.values(GenreClient) as [string, ...string[]];

export const ClientBeneficiaireSchema = z.object({
  nomOuRaisonSociale: z.string().min(2, "Le nom ou la raison sociale est requis.").max(200),
  typeClient: z.enum(typeClientValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un type de client valide." }),
  }),
  identifiantUnique: z.string().max(100, "L'identifiant unique ne doit pas dépasser 100 caractères.").optional().or(z.literal('')),

  // Champs pour informationsContact (obligatoire en BDD, donc au moins des valeurs par défaut ici)
  contactAdressePhysique: z.string().min(1, "L'adresse physique est requise.").max(255),
  contactEmail: z.string().email({ message: "Email de contact invalide." }).max(100),
  contactTelephone1: z.string().min(1, "Le téléphone principal est requis.").max(50),
  contactTelephone2: z.string().max(50).optional().or(z.literal('')),

  secteurActivitePrincipalId: z.string().optional().or(z.literal('')) // Sera un ID (string car vient du form)
    .refine(val => val === "" || val === null || val === undefined || !isNaN(parseInt(val)), { message: "ID de secteur d'activité invalide."}),


  // Champs spécifiques si PersonnePhysique
  age: z.string().optional().or(z.literal('')) // Sera converti en Int
    .refine(val => val === "" || val === null || val === undefined || (!isNaN(parseInt(val)) && parseInt(val) > 0 && parseInt(val) < 150), {
        message: "L'âge doit être un nombre valide entre 1 et 149.",
    }),
  genre: z.enum(genreClientValues).optional(),


  // Pour 'autresInformations', on utilise maintenant des champs individuels
  dateCreationEntreprise: z.string().optional().or(z.literal('')),
  formeJuridique: z.string().optional().or(z.literal('')),
  numeroIdentificationFiscale: z.string().optional().or(z.literal('')),

  autresInformations: z.record(z.any()).optional(),
})
// Logique conditionnelle pour rendre age/genre requis si typeClient est PersonnePhysique
.refine(data => {
    if (data.typeClient === TypeClient.PersonnePhysique) {
        return data.age !== undefined && data.age !== "" && data.genre !== undefined;
    }
    return true;
}, {
    message: "L'âge et le genre sont requis pour une personne physique.",
    path: ["age"], // Ou un path plus général si besoin
})
.refine(data => { // Seconde refine pour le genre si la première cible l'âge
    if (data.typeClient === TypeClient.PersonnePhysique) {
        return data.genre !== undefined;
    }
    return true;
}, {
    message: "Le genre est requis pour une personne physique.",
    path: ["genre"],
});


export type ClientBeneficiaireFormValues = z.infer<typeof ClientBeneficiaireSchema>;