# Solution : Popup d'Erreur 403 pour l'Interface

## Problème Initial
L'erreur 403 "Non autorisé" de l'API Bailleurs était seulement affichée dans la console :
```
Error: API Bailleurs Erreur: 403 "{\"message\":\"Non autorisé\"}"
```

## Solution Implémentée

### 1. Création du Composant ErrorDialog
**Fichier:** `src/components/ui/error-dialog.tsx`

- Composant React réutilisable pour afficher des popups d'erreur
- Support pour différents types d'erreurs (error, warning, info)
- Interface utilisateur avec icônes appropriées
- Bouton de fermeture personnalisable

### 2. Modification de la Page Lignes de Garantie
**Fichier:** `src/app/(app)/lignes-garantie/page.tsx`

#### Ajouts :
- Import du composant `ErrorDialog`
- État `errorDialog` pour gérer l'affichage du popup
- Logique de détection des erreurs 403 dans `fetchDataForPage`
- Composant `ErrorDialog` dans le JSX

#### Fonctionnalités :
- **Détection automatique** des erreurs 403 pour les APIs :
  - `/api/configuration/bailleurs`
  - `/api/lignes-garantie`
- **Parsing intelligent** du message d'erreur JSON
- **Popup modal** avec informations détaillées :
  - Titre spécifique selon l'API concernée
  - Message d'erreur complet avec code 403
  - Instructions pour l'utilisateur
  - Icône d'erreur visuelle

### 3. Améliorations Apportées

#### Interface Utilisateur
- ✅ Popup modal visible au lieu d'erreur console uniquement
- ✅ Icône d'erreur rouge pour attirer l'attention
- ✅ Message d'erreur clair et informatif
- ✅ Instructions pour résoudre le problème

#### Expérience Développeur
- ✅ Composant réutilisable pour d'autres pages
- ✅ Gestion d'erreur robuste avec try/catch
- ✅ Logging console maintenu pour le débogage
- ✅ Code TypeScript typé

## Utilisation

### Quand l'erreur 403 se produit :
1. L'erreur est loggée dans la console (comme avant)
2. **NOUVEAU :** Un popup s'affiche automatiquement avec :
   - Titre : "Erreur d'autorisation - API [Nom de l'API]"
   - Description : "Erreur 403: [Message détaillé]"
   - Instructions : "Veuillez vérifier vos droits d'accès..."
   - Bouton "Fermer"

### Pour réutiliser le composant ErrorDialog :
```tsx
import { ErrorDialog } from "@/components/ui/error-dialog";

// Dans votre composant
const [errorDialog, setErrorDialog] = useState({
  open: false,
  title: "",
  description: "",
  type: "error" as "error" | "warning" | "info"
});

// Pour afficher une erreur
setErrorDialog({
  open: true,
  title: "Titre de l'erreur",
  description: "Description détaillée",
  type: "error"
});

// Dans le JSX
<ErrorDialog
  open={errorDialog.open}
  onOpenChange={(open) => setErrorDialog(prev => ({ ...prev, open }))}
  title={errorDialog.title}
  description={errorDialog.description}
  type={errorDialog.type}
/>
```

## Test
Le script `test-error-popup.js` simule l'erreur et valide l'implémentation.

## Résultat
L'utilisateur voit maintenant un popup d'erreur informatif au lieu de devoir consulter la console du navigateur pour comprendre le problème d'autorisation.