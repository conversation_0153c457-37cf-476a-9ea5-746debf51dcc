// Script de test simple pour l'API de paiement
// Ce script peut être exécuté dans la console du navigateur pour tester l'API

const testPaiementAPI = async () => {
  try {
    // Test avec des données valides
    const testData = {
      montantPayeStr: "1000,00",
      datePaiement: new Date().toISOString(),
      referencePaiement: "PAY-TEST-" + Date.now()
    };

    console.log("Test de l'API de paiement avec les données:", testData);

    // Configuration du test
    const miseEnJeuId = prompt("Entrez l'ID de la mise en jeu à tester:", "1") || "1";
    const response = await fetch(`/api/mises-en-jeu/${miseEnJeuId}/paiement`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    console.log("Réponse de l'API:", result);
    console.log("Status:", response.status);

    if (response.ok) {
      console.log("✅ Test réussi - Paiement enregistré");
    } else {
      console.log("❌ Test échoué:", result.message);
    }

  } catch (error) {
    console.error("Erreur lors du test:", error);
  }
};

// Pour exécuter le test, appelez: testPaiementAPI()
console.log("Script de test chargé. Appelez testPaiementAPI() pour tester l'API.");