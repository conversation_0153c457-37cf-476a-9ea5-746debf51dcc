// src/app/configuration/clients-beneficiaires/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ClientBeneficiaire, TypeClient, Utilisateur, SecteurActivite } from "@prisma/client";
import { <PERSON><PERSON>pDown, Edit, Trash2, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export type ClientBeneficiaireColumn = ClientBeneficiaire & {
    secteurActivitePrincipal?: Pick<SecteurActivite, "id" | "nom"> | null;
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface ClientBeneficiaireColumnsProps {
    onEdit: (client: ClientBeneficiaireColumn, readOnly: boolean) => void;
    onDelete: (client: ClientBeneficiaireColumn) => void;
}

const getTypeClientLabel = (type: TypeClient) => { /* ... (fonction similaire à celle pour TypePartenaire) ... */
    switch (type) {
        case TypeClient.PME: return "PME";
        case TypeClient.PMI: return "PMI";
        case TypeClient.GE: return "Grande Entreprise";
        case TypeClient.GIE: return "GIE";
        case TypeClient.Cooperative: return "Coopérative";
        case TypeClient.Association: return "Association";
        case TypeClient.PersonnePhysique: return "Personne Physique";
        default: return type;
    }
};


export const getClientBeneficiaireColumns = ({ onEdit, onDelete }: ClientBeneficiaireColumnsProps): ColumnDef<ClientBeneficiaireColumn>[] => [
  {
    accessorKey: "nomOuRaisonSociale",
    header: ({ column }) => ( <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}> Nom / Raison Sociale <ArrowUpDown className="ml-2 h-4 w-4" /> </Button> ),
  },
  {
    accessorKey: "typeClient",
    header: "Type",
    cell: ({ row }) => <Badge variant="secondary">{getTypeClientLabel(row.getValue("typeClient"))}</Badge>,
  },
  {
    accessorKey: "identifiantUnique",
    header: "ID Unique",
    cell: ({ row }) => row.getValue("identifiantUnique") || "-",
  },
  {
    accessorKey: "informationsContact",
    header: "Contact Principal",
    cell: ({ row }) => {
        const contact = row.getValue("informationsContact") as { email?: string, telephone1?: string } | null;
        if (!contact) return "-";
        return (
            <div className="text-xs">
                {contact.email && <div className="text-blue-600">{contact.email}</div>}
                {contact.telephone1 && <div>{contact.telephone1}</div>}
            </div>
        );
    }
  },
  {
    accessorKey: "secteurActivitePrincipal.nom",
    header: "Secteur Principal",
    cell: ({ row }) => row.original.secteurActivitePrincipal?.nom || <span className="text-muted-foreground">-</span>,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const client = row.original;
      return (
        <div className="flex gap-2">
          <Button size="icon" variant="ghost" title="Visualiser" onClick={() => onEdit(client, true)}>
            <Eye className="w-4 h-4" />
          </Button>
          <Button size="icon" variant="ghost" title="Modifier" onClick={() => onEdit(client, false)}>
            <Edit className="w-4 h-4" />
          </Button>
          <Button size="icon" variant="ghost" title="Supprimer" onClick={() => onDelete(client)}>
            <Trash2 className="w-4 h-4 text-red-500" />
          </Button>
        </div>
      );
    },
  },
];