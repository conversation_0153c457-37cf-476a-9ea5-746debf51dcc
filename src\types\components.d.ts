declare module '@/components/ui/use-toast' {
  export function useToast(): {
    toast: (props: {
      title?: string;
      description?: string;
      variant?: 'default' | 'destructive';
    }) => void;
    dismiss: (id: string) => void;
  };
}
declare module '@/components/shared/pagination-controls' {
  import { ReactNode } from 'react';

  export interface PaginationControlsProps {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
  }

  const PaginationControls: (props: PaginationControlsProps) => ReactNode;
  export default PaginationControls;
}