// src/app/api/configuration/partenaires/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypePartenaire } from "@prisma/client"; // Importer TypePartenaire
import { PartenaireSchema } from "@/lib/schemas/partenaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';

// GET: Lister tous les partenaires
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const userId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const partenaires = await prisma.partenaire.findMany({
        orderBy: { nom: "asc" },
        include: {
          utilisateurCreation: { select: { nomUtilisateur: true }},
          utilisateurModification: { select: { nomUtilisateur: true }},
        }
      });
      return NextResponse.json(partenaires);
    } catch (error) {
      console.error("Erreur GET /api/configuration/partenaires:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// POST: Créer un nouveau partenaire
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const creatorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: creatorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      const validation = PartenaireSchema.safeParse(body);

      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        nom,
        typePartenaire,
        description,
        convention,
        contactNomRepresentant,
        contactAdresse,
        contactEmail,
        contactTelephone,
        autresInformationsStr,
      } = validation.data;

      const contactJson = {
        nomRepresentant: contactNomRepresentant,
        adresse: contactAdresse,
        email: contactEmail,
        telephone: contactTelephone,
      };

      let autresInformationsJson = null;
      if (autresInformationsStr && autresInformationsStr.trim() !== "") {
        try {
          autresInformationsJson = JSON.parse(autresInformationsStr);
        } catch (e) {
          return NextResponse.json({ message: "Format JSON invalide pour 'Autres Informations'." }, { status: 400 });
        }
      }

      const existingPartenaire = await prisma.partenaire.findUnique({ where: { nom } });
      if (existingPartenaire) {
        return NextResponse.json({ message: "Un partenaire avec ce nom existe déjà." }, { status: 409 });
      }

      const partenaireData: any = {
        nom,
        typePartenaire: typePartenaire as TypePartenaire, // Cast car Zod enum est string
        description,
        convention,
        contact: contactJson,
        autresInformations: autresInformationsJson,
      };
      if (creatorId !== undefined) {
        partenaireData.utilisateurCreationId = creatorId;
      }

      const newPartenaire = await prisma.partenaire.create({
        data: partenaireData,
      });
      return NextResponse.json(newPartenaire, { status: 201 });
    } catch (error) {
      console.error("Erreur POST /api/configuration/partenaires:", error);
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}