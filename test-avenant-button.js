// Script de diagnostic pour le bouton Avenant
// Ce script teste les conditions d'affichage du bouton "Ajouter un Avenant"

console.log("=== DIAGNOSTIC BOUTON AVENANT ===");

// Simulation des conditions d'affichage du bouton
function testAvenantButtonVisibility(userRole, sessionStatus) {
    console.log(`\nTest pour rôle: ${userRole}, statut session: ${sessionStatus}`);
    
    // Conditions d'accès global (pour voir la page)
    const isAutoriseGlobale = sessionStatus === "authenticated" && 
        ["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(userRole);
    
    // Conditions pour ajouter un avenant (bouton visible)
    const canAddAvenant = sessionStatus === "authenticated" && 
        ["Administrateur", "GestionnaireGesGar"].includes(userRole);
    
    console.log(`  - Accès à la page: ${isAutoriseGlobale ? "✅ OUI" : "❌ NON"}`);
    console.log(`  - Bouton Avenant visible: ${canAddAvenant ? "✅ OUI" : "❌ NON"}`);
    
    if (isAutoriseGlobale && !canAddAvenant) {
        console.log(`  ⚠️  PROBLÈME IDENTIFIÉ: L'utilisateur peut voir la page mais pas le bouton Avenant`);
        console.log(`     Raison: Le rôle "${userRole}" n'est pas autorisé à créer des avenants`);
        console.log(`     Rôles autorisés pour les avenants: Administrateur, GestionnaireGesGar`);
    }
    
    return { isAutoriseGlobale, canAddAvenant };
}

// Test avec différents rôles
const roles = [
    "Administrateur",
    "GestionnaireGesGar", 
    "AnalysteFinancier",
    "Partenaire",
    "Bailleur",
    "Auditeur"
];

console.log("\n=== TESTS PAR RÔLE ===");
roles.forEach(role => {
    testAvenantButtonVisibility(role, "authenticated");
});

console.log("\n=== RÉSUMÉ DES PERMISSIONS ===");
console.log("📋 Accès à la page des lignes de garantie:");
console.log("   ✅ Administrateur");
console.log("   ✅ GestionnaireGesGar");
console.log("   ✅ AnalysteFinancier");
console.log("   ❌ Partenaire");
console.log("   ❌ Bailleur");
console.log("   ❌ Auditeur");

console.log("\n🔧 Bouton 'Ajouter un Avenant' visible:");
console.log("   ✅ Administrateur");
console.log("   ✅ GestionnaireGesGar");
console.log("   ❌ AnalysteFinancier (PROBLÈME POTENTIEL)");
console.log("   ❌ Partenaire");
console.log("   ❌ Bailleur");
console.log("   ❌ Auditeur");

console.log("\n=== SOLUTIONS POSSIBLES ===");
console.log("1. 🔧 Ajouter 'AnalysteFinancier' aux rôles autorisés pour les avenants");
console.log("2. 📝 Modifier la logique métier si les analystes ne doivent pas créer d'avenants");
console.log("3. 🎯 Créer un rôle spécifique pour la gestion des avenants");

console.log("\n=== VÉRIFICATIONS À EFFECTUER ===");
console.log("1. ✅ Vérifier le rôle de l'utilisateur connecté");
console.log("2. ✅ Vérifier que la session est bien authentifiée");
console.log("3. ✅ Vérifier que la ligne de garantie a le statut 'Active'");
console.log("4. ✅ Vérifier les logs de la console du navigateur");
console.log("5. ✅ Vérifier les permissions côté API");

// Fonction pour vérifier les conditions spécifiques d'une ligne de garantie
function checkLigneGarantieConditions(ligneGarantie, userRole) {
    console.log("\n=== VÉRIFICATION LIGNE DE GARANTIE ===");
    console.log(`Statut de la ligne: ${ligneGarantie.statut}`);
    console.log(`Rôle utilisateur: ${userRole}`);
    
    const isActive = ligneGarantie.statut === "Active";
    const canAddAvenant = ["Administrateur", "GestionnaireGesGar"].includes(userRole);
    
    console.log(`Ligne active: ${isActive ? "✅ OUI" : "❌ NON"}`);
    console.log(`Rôle autorisé: ${canAddAvenant ? "✅ OUI" : "❌ NON"}`);
    
    if (isActive && canAddAvenant) {
        console.log("✅ Le bouton Avenant DEVRAIT être visible");
    } else {
        console.log("❌ Le bouton Avenant ne sera PAS visible");
        if (!isActive) console.log("   Raison: La ligne de garantie n'est pas active");
        if (!canAddAvenant) console.log("   Raison: Rôle utilisateur non autorisé");
    }
}

// Exemple de test avec une ligne de garantie active
const exempleLineGarantie = {
    id: 1,
    nom: "Ligne Test",
    statut: "Active",
    montantInitial: 1000000,
    montantDisponible: 800000
};

checkLigneGarantieConditions(exempleLineGarantie, "AnalysteFinancier");

console.log("\n=== FIN DU DIAGNOSTIC ===");