// src/app/configuration/bailleurs/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Bailleur, Utilisateur } from "@prisma/client";
import { ArrowUpDown, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";

export type BailleurColumn = Bailleur & {
  // Enrichir si besoin avec infos utilisateurCreation/Modif
  utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
  utilisateurModification?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface BailleurColumnsProps {
  onEdit: (bailleur: BailleurColumn) => void;
  onDelete: (bailleur: BailleurColumn) => void;
}

export const getBailleurColumns = ({
  onEdit,
  onDelete,
}: BailleurColumnsProps): ColumnDef<BailleurColumn>[] => [
  {
    accessorKey: "nom",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Nom <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <span className="text-sm text-muted-foreground truncate block max-w-md">
        {row.getValue("description") || "-"}
      </span>
    ),
  },
  {
    accessorKey: "contact",
    header: "Contact Principal",
    cell: ({ row }) => {
      const contact = row.getValue("contact") as {
        nomRepresentant?: string;
        email?: string;
        telephone?: string;
      } | null;
      if (!contact || Object.values(contact).every((v) => !v))
        return <span className="text-muted-foreground">-</span>;
      return (
        <div className="text-xs">
          {contact.nomRepresentant && <div>{contact.nomRepresentant}</div>}
          {contact.email && (
            <div className="text-blue-600">{contact.email}</div>
          )}
          {contact.telephone && <div>{contact.telephone}</div>}
        </div>
      );
    },
  },
  {
    accessorKey: "autresInformations",
    header: "Infos Compl.",
    cell: ({ row }) => {
      const autresInfos = row.getValue("autresInformations") as {
        pays_origine?: string;
        type_organisation?: string;
        site_web?: string;
      } | null;
      if (!autresInfos || Object.values(autresInfos).every((v) => !v))
        return <span className="text-muted-foreground">-</span>;
      return (
        <ul className="list-disc list-inside text-xs">
          {autresInfos.pays_origine && (
            <li>Pays: {autresInfos.pays_origine}</li>
          )}
          {autresInfos.type_organisation && (
            <li>Type: {autresInfos.type_organisation}</li>
          )}
          {autresInfos.site_web && (
            <li>
              Web:{" "}
              <a
                href={autresInfos.site_web}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Lien
              </a>
            </li>
          )}
        </ul>
      );
    },
  },
  {
    accessorKey: "dateCreation",
    header: "Créé le",
    cell: ({ row }) =>
      new Date(row.getValue("dateCreation")).toLocaleDateString("fr-FR"),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const bailleur = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEdit(bailleur)}
            title="Modifier"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onDelete(bailleur)}
            className="text-red-600 hover:text-red-700"
            title="Supprimer"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
