import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutMainlevee, StatutGarantie } from "@prisma/client";
import { DecisionMainleveeSchema } from "@/lib/schemas/mainlevee.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams { params: { id: string } }

export async function PUT(request: Request, context: RouteParams) {
  const { params } = context;
  const session = await getServerSession(authOptions);
  const allowedRoles = ["Administrateur", "GestionnaireGesGar"];
  if (!session || !allowedRoles.includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé à traiter les mainlevées" }, { status: 403 });
  }

  const resolvedParams = await params;
  const mainleveeId = parseInt(resolvedParams.id);
  if (isNaN(mainleveeId)) {
    return NextResponse.json({ message: "ID de mainlevée invalide" }, { status: 400 });
  }

  const processorId = session.user?.id ? parseInt(session.user.id) : undefined;

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const body = await request.json();
      if (body.dateDecision) body.dateDecision = new Date(body.dateDecision);

      const validation = DecisionMainleveeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de décision invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { statut, dateDecision, montantRecupereStr, commentairesDecision, raisonRefus } = validation.data;
      let montantRecupereDecimal = null;
      if (montantRecupereStr && montantRecupereStr.trim() !== "") {
        try {
          montantRecupereDecimal = new Decimal(montantRecupereStr.replace(',', '.'));
        } catch (e) {
          return NextResponse.json({ message: "Montant récupéré invalide" }, { status: 400 });
        }
      }

      const result = await prisma.$transaction(async (tx) => {
        const mainlevee = await tx.mainlevee.findUnique({
          where: { id: mainleveeId },
          include: { garantie: { include: { allocation: true } } }
        });

        if (!mainlevee) throw new Error("Demande de mainlevée non trouvée.");
        if (!mainlevee.garantie) throw new Error("Garantie associée à la mainlevée non trouvée.");
        if (!mainlevee.garantie.allocation) throw new Error("Allocation associée à la garantie non trouvée.");

        if (mainlevee.statut !== StatutMainlevee.Demandee && mainlevee.statut !== StatutMainlevee.EnCoursApprobation) {
            throw new Error(`Cette demande de mainlevée a déjà été traitée (Statut: ${mainlevee.statut}).`);
        }

        const updatedMainlevee = await tx.mainlevee.update({
          where: { id: mainleveeId },
          data: {
            statut: statut as StatutMainlevee,
            dateDecision,
            montantRecupere: montantRecupereDecimal,
            commentairesDecision,
            raisonRefus: statut === StatutMainlevee.Refusee ? raisonRefus : null,
            utilisateurModificationId: processorId,
          },
        });

        if (statut === StatutMainlevee.Accordee) {
          await tx.garantie.update({
            where: { id: mainlevee.garantieId },
            data: {
              statut: StatutGarantie.MainleveeAccordee,
              utilisateurModificationId: processorId,
            },
          });

          const allocation = mainlevee.garantie.allocation;
          const nouveauMontantDisponibleAlloc = allocation.montantDisponible.add(mainlevee.garantie.montantGarantie);

          await tx.allocationLignePartenaire.update({
            where: { id: allocation.id },
            data: {
              montantDisponible: nouveauMontantDisponibleAlloc,
              utilisateurModificationId: processorId,
            },
          });
        } else if (statut === StatutMainlevee.Refusee) {
          let statutGarantieApresRefus = mainlevee.garantie.statut;
          if (mainlevee.garantie.statut === StatutGarantie.MainleveeDemandee) {
              statutGarantieApresRefus = StatutGarantie.Active;
          }
          await tx.garantie.update({
            where: { id: mainlevee.garantieId },
            data: {
              statut: statutGarantieApresRefus,
              utilisateurModificationId: processorId,
            },
          });
        }
        return updatedMainlevee;
      });
      return NextResponse.json(result);

    } catch (error: any) {
      console.error(`Erreur PUT /api/mainlevees/${mainleveeId}:`, error);
      if (error.message.includes("non trouvée") || error.message.includes("déjà été traitée")) {
        return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
      }
      // Ajout d'une gestion d'erreur générique si besoin
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}