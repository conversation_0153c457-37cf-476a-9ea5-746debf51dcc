import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutMainlevee } from "@prisma/client";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  const allowedRoles = ["Administrateur", "GestionnaireGesGar"];
  if (!session || !allowedRoles.includes(session.user?.role)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const statutFilter = searchParams.get("statut");
  const page = Math.max(1, parseInt(searchParams.get("page") || "1") || 1);
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get("limit") || "15") || 15));
  const skip = (page - 1) * limit;

  import { Prisma } from "@prisma/client";
  const whereClause: Prisma.MainleveeWhereInput = {};
  if (statutFilter) {
    const statutsArray = statutFilter.split(',').map(s => s.trim()).filter(s => Object.values(StatutMainlevee).includes(s as StatutMainlevee));
    if (statutsArray.length > 0) {
      whereClause.statut = { in: statutsArray as StatutMainlevee[] };
    }
  } else {
    whereClause.statut = { in: [StatutMainlevee.Demandee, StatutMainlevee.EnCoursApprobation] };
  }

  try {
    const mainlevees = await prisma.mainlevee.findMany({
      where: whereClause,
      orderBy: { dateDemande: "asc" },
      skip: skip,
      take: limit,
      include: {
        garantie: {
          select: {
            id: true,
            referenceGarantie: true,
            projet: { select: { clientBeneficiaire: { select: { nomOuRaisonSociale: true } } } },
            allocation: { select: { partenaire: { select: { nom: true } } } },
          }
        },
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } },
      }
    });

    const totalMainlevees = await prisma.mainlevee.count({ where: whereClause });

    return NextResponse.json({
      data: mainlevees,
      totalPages: Math.ceil(totalMainlevees / limit),
      currentPage: page,
      totalRecords: totalMainlevees,
    });

  } catch (error) {
    console.error("Erreur GET /api/mainlevees:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
} 