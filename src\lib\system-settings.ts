// src/lib/system-settings.ts
import prisma from "./prisma";
import { TypeParametre } from "@prisma/client";

// Cache simple en mémoire pour éviter les appels BDD répétitifs
const settingsCache = new Map<string, { valeur: any; timestamp: number }>();
const CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

export async function getSystemSetting<T = string | number | boolean | object>(
    cle: string,
    defaultValue?: T
): Promise<T | undefined> {
    const cached = settingsCache.get(cle);
    if (cached && (Date.now() - cached.timestamp < CACHE_DURATION_MS)) {
        return cached.valeur as T;
    }

    try {
        const setting = await prisma.parametreSysteme.findUnique({
            where: { cle },
        });

        if (!setting) {
            if (defaultValue !== undefined) settingsCache.set(cle, { valeur: defaultValue, timestamp: Date.now() });
            return defaultValue;
        }

        let parsedValue: any;
        switch (setting.typeValeur) {
            case TypeParametre.NUMBER:
                parsedValue = parseFloat(setting.valeur);
                if (isNaN(parsedValue)) {
                    console.warn(`Paramètre système '${cle}' de type NUMBER a une valeur non numérique: ${setting.valeur}`);
                    return defaultValue;
                }
                break;
            case TypeParametre.BOOLEAN:
                parsedValue = setting.valeur.toLowerCase() === 'true';
                break;
            case TypeParametre.JSON:
                try {
                    parsedValue = JSON.parse(setting.valeur);
                } catch (e) {
                    console.warn(`Paramètre système '${cle}' de type JSON a une valeur JSON invalide: ${setting.valeur}`);
                    return defaultValue;
                }
                break;
            case TypeParametre.STRING:
            case TypeParametre.TEXT:
            default:
                parsedValue = setting.valeur;
                break;
        }
        settingsCache.set(cle, { valeur: parsedValue, timestamp: Date.now() });
        return parsedValue as T;

    } catch (error) {
        console.error(`Erreur lors de la récupération du paramètre système '${cle}':`, error);
        return defaultValue;
    }
}

// Exemple d'utilisation :
// const delaiMiseEnJeu = await getSystemSetting<number>("DELAI_MISE_EN_JEU_DEFAUT", 90);
// const deviseDefaut = await getSystemSetting<string>("DEVISE_PAR_DEFAUT", "XOF");