// src/app/api/mises-en-jeu/[id]/paiement/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutMiseEnJeu, StatutGarantie } from "@prisma/client";
import { PaiementMiseEnJeuSchema } from "@/lib/schemas/mise-en-jeu.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams {
  params: { id: string }; // id de la MiseEnJeu
}

// POST: Enregistrer le paiement d'une mise en jeu approuvée
export async function POST(request: Request, { params }: RouteParams) {
  const session = await getServerSession(authOptions);
  // Seuls Admin et GestionnaireGesGar peuvent enregistrer un paiement
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé à enregistrer les paiements de mise en jeu" }, { status: 403 });
  }

  const resolvedParams = await params;
  const miseEnJeuId = parseInt(resolvedParams.id);
  if (isNaN(miseEnJeuId)) {
    return NextResponse.json({ message: "ID de mise en jeu invalide" }, { status: 400 });
  }

  const processorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: processorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      if (body.datePaiement) body.datePaiement = new Date(body.datePaiement);

      const validation = PaiementMiseEnJeuSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ 
          message: "Données de paiement invalides", 
          errors: validation.error.formErrors.fieldErrors 
        }, { status: 400 });
      }

      const { montantPayeStr, datePaiement, referencePaiement } = validation.data;
      const montantPayeDecimal = new Decimal(montantPayeStr.replace(',', '.'));

      if (montantPayeDecimal.isNegative() || montantPayeDecimal.isZero()) {
        return NextResponse.json({ message: "Le montant payé doit être positif." }, { status: 400 });
      }

      // --- Début Transaction ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer la miseEnJeu et sa garantie associée
        const miseEnJeu = await tx.miseEnJeu.findUnique({
          where: { id: miseEnJeuId },
          include: { garantie: true }
        });

        if (!miseEnJeu) {
          throw new Error("Demande de mise en jeu non trouvée.");
        }
        if (!miseEnJeu.garantie) {
          throw new Error("Garantie associée à la mise en jeu non trouvée.");
        }

        // 2. Vérifier si la mise en jeu est dans un statut permettant le paiement
        const statutsPermettantPaiement = [
          StatutMiseEnJeu.ApprouveeTotalement,
          StatutMiseEnJeu.ApprouveePartiellement,
          StatutMiseEnJeu.EnAttentePaiement
        ] as StatutMiseEnJeu[];
        
        if (!statutsPermettantPaiement.includes(miseEnJeu.statut)) {
          throw new Error(`Cette mise en jeu ne peut pas être payée dans son statut actuel (${miseEnJeu.statut}). Elle doit être approuvée au préalable.`);
        }

        // 3. Vérifier si la mise en jeu n'est pas déjà payée
        if (miseEnJeu.statut === StatutMiseEnJeu.Payee) {
          throw new Error("Cette mise en jeu a déjà été payée.");
        }

        // 4. Valider que le montant payé ne dépasse pas le montant approuvé
        if (!miseEnJeu.montantApprouve) {
          throw new Error("Aucun montant approuvé trouvé pour cette mise en jeu.");
        }

        if (montantPayeDecimal.greaterThan(miseEnJeu.montantApprouve)) {
          throw new Error(`Le montant payé (${montantPayeDecimal.toFixed(2)}) ne peut pas dépasser le montant approuvé (${miseEnJeu.montantApprouve.toFixed(2)}).`);
        }

        // 5. Vérifier l'unicité de la référence de paiement
        const existingPaiement = await tx.miseEnJeu.findFirst({
          where: {
            referencePaiement: referencePaiement,
            id: { not: miseEnJeuId }
          }
        });

        if (existingPaiement) {
          throw new Error(`La référence de paiement "${referencePaiement}" est déjà utilisée pour une autre mise en jeu.`);
        }

        // 6. Mettre à jour la MiseEnJeu avec les informations de paiement
        const updatedMiseEnJeu = await tx.miseEnJeu.update({
          where: { id: miseEnJeuId },
          data: {
            montantPaye: montantPayeDecimal,
            datePaiement,
            referencePaiement,
            statut: StatutMiseEnJeu.Payee,
            utilisateurModificationId: processorId,
          },
        });

        // 7. Mettre à jour le statut de la Garantie parente à MiseEnJeuPayee
        await tx.garantie.update({
          where: { id: miseEnJeu.garantieId },
          data: {
            statut: StatutGarantie.MiseEnJeuPayee,
            utilisateurModificationId: processorId,
          },
        });

        return updatedMiseEnJeu;
      });
      // --- Fin Transaction ---

      return NextResponse.json({
        message: "Paiement enregistré avec succès",
        data: result
      });

    } catch (error: any) {
      console.error(`Erreur POST /api/mises-en-jeu/${miseEnJeuId}/paiement:`, error);
      
      if (error.message.includes("non trouvée") || 
          error.message.includes("ne peut pas être payée") ||
          error.message.includes("déjà été payée") ||
          error.message.includes("ne peut pas dépasser") ||
          error.message.includes("Aucun montant approuvé") ||
          error.message.includes("déjà utilisée")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      
      if (error.code === 'P2025') {
        return NextResponse.json({ message: "Entité non trouvée pour la mise à jour" }, { status: 404 });
      }
      
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}