# Configuration MCP Context7

Ce répertoire contient la configuration pour le serveur MCP Context7 qui fournit de la documentation à jour pour les bibliothèques de développement.

## Configuration

Le fichier `mcp_settings.json` contient la configuration du serveur MCP Context7 :

```json
{
  "mcpServers": {
    "github.com/upstash/context7-mcp": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@upstash/context7-mcp"],
      "disabled": false,
      "autoApprove": [],
      "timeout": 60,
      "transportType": "stdio"
    }
  }
}
```

## Outils disponibles

### 1. resolve-library-id
Résout un nom de bibliothèque général en un ID compatible Context7.

**Paramètres :**
- `libraryName` (requis) : Le nom de la bibliothèque à rechercher

**Exemple :**
```json
{
  "libraryName": "Next.js"
}
```

### 2. get-library-docs
Récupère la documentation pour une bibliothèque en utilisant un ID compatible Context7.

**Paramètres :**
- `context7CompatibleLibraryID` (requis) : L'ID de la bibliothèque compatible Context7
- `topic` (optionnel) : Sujet spécifique pour filtrer la documentation (ex: "routing", "hooks")
- `tokens` (optionnel, défaut: 10000) : Nombre maximum de tokens à retourner

**Exemple :**
```json
{
  "context7CompatibleLibraryID": "/vercel/next.js",
  "topic": "routing",
  "tokens": 15000
}
```

## Utilisation

1. Assurez-vous que le serveur MCP est configuré dans votre client MCP (VS Code, Cursor, Claude Desktop, etc.)
2. Utilisez les outils via l'interface MCP de votre client
3. Pour obtenir de la documentation, commencez par `resolve-library-id` pour obtenir l'ID correct, puis utilisez `get-library-docs`

## Variables d'environnement

- `DEFAULT_MINIMUM_TOKENS` : Définit le nombre minimum de tokens pour la récupération de documentation (défaut: 10000)

## Dépannage

Si vous rencontrez des problèmes :

1. **ERR_MODULE_NOT_FOUND** : Essayez d'utiliser `bunx` au lieu de `npx`
2. **Problèmes de résolution ESM** : Ajoutez `--experimental-vm-modules` aux options Node
3. **Problèmes TLS/Certificat** : Utilisez `--experimental-fetch` avec `npx`
4. **Erreurs du client MCP** : Assurez-vous d'utiliser Node v18 ou supérieur

## Liens utiles

- [Documentation officielle Context7](https://context7.com)
- [Dépôt GitHub](https://github.com/upstash/context7-mcp)
- [Documentation MCP](https://modelcontextprotocol.io/)