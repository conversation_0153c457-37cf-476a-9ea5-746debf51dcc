-- CreateEnum
CREATE TYPE "TypeParametre" AS ENUM ('STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'TEXT');

-- CreateTable
CREATE TABLE "ParametreSysteme" (
    "id" SERIAL NOT NULL,
    "cle" TEXT NOT NULL,
    "valeur" TEXT NOT NULL,
    "description" TEXT,
    "typeValeur" "TypeParametre" NOT NULL DEFAULT 'STRING',
    "estModifiable" BOOLEAN NOT NULL DEFAULT true,
    "dateCreation" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dateModification" TIMESTAMP(3) NOT NULL,
    "utilisateurCreationId" INTEGER,
    "utilisateurModificationId" INTEGER,

    CONSTRAINT "ParametreSysteme_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ParametreSysteme_cle_key" ON "ParametreSysteme"("cle");

-- AddForeignKey
ALTER TABLE "ParametreSysteme" ADD CONSTRAINT "ParametreSysteme_utilisateurCreationId_fkey" FOREIGN KEY ("utilisateurCreationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ParametreSysteme" ADD CONSTRAINT "ParametreSysteme_utilisateurModificationId_fkey" FOREIGN KEY ("utilisateurModificationId") REFERENCES "Utilisateur"("id") ON DELETE SET NULL ON UPDATE NO ACTION;
