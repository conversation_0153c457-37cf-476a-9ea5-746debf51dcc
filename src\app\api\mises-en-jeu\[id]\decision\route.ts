// src/app/api/mises-en-jeu/[id]/decision/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez le chemin
import { RoleUtilisateur, StatutMiseEnJeu, StatutGarantie } from "@prisma/client";
import { DecisionMiseEnJeuSchema } from "@/lib/schemas/mise-en-jeu.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

// Les rôles autorisés à prendre une décision sur une mise en jeu
const MISE_EN_JEU_DECISION_ROLES = [RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar];

interface RouteParams {
  params: { id: string }; // id de la MiseEnJeu
}

// --- Custom Error Classes ---
class BusinessRuleError extends Error {
  code: string;
  constructor(message: string, code: string) {
    super(message);
    this.name = "BusinessRuleError";
    this.code = code;
  }
}

// PUT: Enregistrer la décision sur une demande de mise en jeu
export async function PUT(request: Request, { params }: RouteParams) {
  const session = await getServerSession(authOptions);
  // Seuls Admin et GestionnaireGesGar peuvent prendre une décision
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé à traiter les mises en jeu" }, { status: 403 });
  }

  const resolvedParams = await params;
  const miseEnJeuId = parseInt(resolvedParams.id);
  if (isNaN(miseEnJeuId)) {
    return NextResponse.json({ message: "ID de mise en jeu invalide" }, { status: 400 });
  }

  const processorId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || null;
  const userAgentHeader = headersList.get('user-agent') || null;

  return auditContext.run({ userId: processorId, ip: ipAddress ?? undefined, userAgent: userAgentHeader ?? undefined }, async () => {
    try {
      const body = await request.json();
      if (body.dateDecision) body.dateDecision = new Date(body.dateDecision);

      const validation = DecisionMiseEnJeuSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de décision invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { statut: nouveauStatutMiseEnJeu, dateDecision, montantApprouveStr, commentairesDecision } = validation.data;
      let montantApprouveDecimal: Decimal | null = null;

      if (nouveauStatutMiseEnJeu === StatutMiseEnJeu.ApprouveeTotalement || nouveauStatutMiseEnJeu === StatutMiseEnJeu.ApprouveePartiellement) {
        if (!montantApprouveStr || montantApprouveStr.trim() === "") {
          return NextResponse.json({ message: "Le montant approuvé est requis pour une approbation." }, { status: 400 });
        }
        montantApprouveDecimal = new Decimal(montantApprouveStr.replace(',', '.'));
        if (montantApprouveDecimal.isNegative()) {
            return NextResponse.json({ message: "Le montant approuvé ne peut pas être négatif." }, { status: 400 });
        }
      }

      // --- Début Transaction ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer la miseEnJeu et sa garantie associée
        const miseEnJeu = await tx.miseEnJeu.findUnique({
          where: { id: miseEnJeuId },
          include: { garantie: true }
        });

        if (!miseEnJeu) throw new Error("Demande de mise en jeu non trouvée.");
        if (!miseEnJeu.garantie) throw new Error("Garantie associée à la mise en jeu non trouvée.");

        // 2. Vérifier si la demande est traitable
        if (!["Demandee", "EnCoursInstruction"].includes(miseEnJeu.statut as string)) {
          throw new BusinessRuleError(
            `Cette demande de mise en jeu a déjà été traitée ou est dans un statut non modifiable (Statut actuel: ${miseEnJeu.statut}).`,
            "MISE_EN_JEU_STATUT_NON_MODIFIABLE"
          );
        }

        // 3. Valider le montant approuvé
        if (montantApprouveDecimal) {
          if (montantApprouveDecimal.greaterThan(miseEnJeu.montantDemande)) {
            throw new BusinessRuleError(
              `Le montant approuvé (${montantApprouveDecimal.toFixed(2)}) ne peut pas dépasser le montant demandé (${miseEnJeu.montantDemande.toFixed(2)}).`,
              "MONTANT_APPROUVE_SUPERIEUR_DEMANDE"
            );
          }
          if (montantApprouveDecimal.greaterThan(miseEnJeu.garantie.montantGarantie)) {
            throw new BusinessRuleError(
              `Le montant approuvé (${montantApprouveDecimal.toFixed(2)}) ne peut pas dépasser le montant garanti (${miseEnJeu.garantie.montantGarantie.toFixed(2)}).`,
              "MONTANT_APPROUVE_SUPERIEUR_GARANTIE"
            );
          }
        }
        if (nouveauStatutMiseEnJeu === StatutMiseEnJeu.ApprouveeTotalement && montantApprouveDecimal && !montantApprouveDecimal.equals(miseEnJeu.montantDemande)) {
          throw new BusinessRuleError(
            "Pour une approbation totale, le montant approuvé doit être égal au montant demandé.",
            "MONTANT_APPROUVE_DIFFERENT_DEMANDE_TOTAL"
          );
        }


        // 4. Mettre à jour la MiseEnJeu
        const updatedMiseEnJeu = await tx.miseEnJeu.update({
          where: { id: miseEnJeuId },
          data: {
            statut: nouveauStatutMiseEnJeu as StatutMiseEnJeu,
            dateDecision,
            montantApprouve: montantApprouveDecimal,
            commentairesDecision,
            utilisateurModificationId: processorId,
          },
        });

        // 5. Mettre à jour le statut de la Garantie parente
        let nouveauStatutGarantie: StatutGarantie;
        if (nouveauStatutMiseEnJeu === StatutMiseEnJeu.ApprouveeTotalement || nouveauStatutMiseEnJeu === StatutMiseEnJeu.ApprouveePartiellement) {
          nouveauStatutGarantie = StatutGarantie.MiseEnJeuAcceptee; // Ou EnAttentePaiement si c'est une étape distincte
          // Le montantDisponible de l'allocation n'est pas ré-impacté ici, il l'a été à l'activation de la garantie.
          // Le paiement effectif sera une sortie de trésorerie pour le Fonds.
        } else if (nouveauStatutMiseEnJeu === StatutMiseEnJeu.Refusee) {
          nouveauStatutGarantie = StatutGarantie.MiseEnJeuRefusee;
          // Faut-il remettre la garantie à son statut précédent (ex: Active, EnSouffrance) ?
          // Pour l'instant, on la marque comme MiseEnJeuRefusee.
        } else {
          // Cas de EnCoursInstruction, le statut de la garantie ne change peut-être pas encore.
          nouveauStatutGarantie = miseEnJeu.garantie.statut; // Garder le statut actuel de la garantie
        }

        if (miseEnJeu.garantie.statut !== nouveauStatutGarantie) {
            await tx.garantie.update({
                where: { id: miseEnJeu.garantieId },
                data: {
                    statut: nouveauStatutGarantie,
                    utilisateurModificationId: processorId,
                },
            });
        }
        return updatedMiseEnJeu;
      });
      // --- Fin Transaction ---
      return NextResponse.json(result);

    } catch (error: any) {
      console.error(`Erreur PUT /api/mises-en-jeu/${miseEnJeuId}/decision:`, error);
      if (error instanceof BusinessRuleError) {
        return NextResponse.json({ message: error.message, code: error.code }, { status: 400 });
      }
      if (error.message.includes("non trouvée")) {
        return NextResponse.json({ message: error.message, code: "NOT_FOUND" }, { status: 400 });
      }
      if (error.code === 'P2025') return NextResponse.json({ message: "Entité non trouvée pour la mise à jour", code: "NOT_FOUND" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}