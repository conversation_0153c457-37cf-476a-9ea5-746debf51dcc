// Script de test pour vérifier le fonctionnement du bouton de mainlevée
// À exécuter dans la console du navigateur sur la page des garanties

console.log("=== TEST BOUTON MAINLEVÉE ===");

// 1. Vérifier que la page est bien chargée
if (!document.querySelector('[data-testid="data-table"]')) {
  console.error("❌ Tableau des garanties non trouvé. Assurez-vous d'être sur la page /garanties");
  return;
}

// 2. Simuler une garantie avec statut "Active" pour tester
const testGarantie = {
  id: 999,
  referenceGarantie: "TEST-MAINLEVEE",
  statut: "Active",
  montantCredit: 1000000,
  montantGarantie: 800000,
  allocation: {
    partenaire: { nom: "Test Partenaire" },
    ligneGarantie: { devise: "XOF" }
  },
  projet: {
    nom: "Test Projet",
    clientBeneficiaire: { nomOuRaisonSociale: "Test Client" }
  }
};

console.log("✅ Garantie de test créée:", testGarantie);

// 3. Tester la logique de détermination du bouton mainlevée
const statutsPermettantMainlevee = [
  'Active',
  'EnSouffrance', 
  'Echue',
  'MiseEnJeuAcceptee',
  'MiseEnJeuPayee',
  'Transferree'
];

const peutDemanderMainlevee = statutsPermettantMainlevee.includes(testGarantie.statut)
  && testGarantie.statut !== 'MainleveeDemandee'
  && testGarantie.statut !== 'MainleveeAccordee';

console.log("✅ Statuts permettant mainlevée:", statutsPermettantMainlevee);
console.log("✅ Statut de test:", testGarantie.statut);
console.log("✅ Peut demander mainlevée:", peutDemanderMainlevee);

// 4. Vérifier les garanties réelles dans le tableau
const rows = document.querySelectorAll('[data-testid="data-table"] tbody tr');
console.log(`✅ Nombre de garanties réelles: ${rows.length}`);

let garantiesAvecBoutonMainlevee = 0;
let garantiesDevantAvoirBouton = 0;

rows.forEach((row, index) => {
  const cells = row.querySelectorAll('td');
  if (cells.length >= 7) {
    const statutCell = cells[6]; // Colonne statut (index 6)
    const actionsCell = cells[cells.length - 1]; // Dernière colonne (actions)
    
    const statut = statutCell.textContent?.trim();
    const mainleveeButton = actionsCell.querySelector('[title="Demander Mainlevée"]');
    
    const devaitAvoirBouton = statutsPermettantMainlevee.includes(statut) 
      && statut !== 'MainleveeDemandee' 
      && statut !== 'MainleveeAccordee';
    
    if (devaitAvoirBouton) garantiesDevantAvoirBouton++;
    if (mainleveeButton) garantiesAvecBoutonMainlevee++;
    
    console.log(`Garantie ${index + 1}:`);
    console.log(`  - Statut: "${statut}"`);
    console.log(`  - Devrait avoir bouton: ${devaitAvoirBouton}`);
    console.log(`  - A le bouton: ${!!mainleveeButton}`);
    
    if (devaitAvoirBouton && !mainleveeButton) {
      console.warn(`  ⚠️ PROBLÈME: Garantie ${index + 1} devrait avoir un bouton mainlevée!`);
    }
  }
});

console.log(`\n=== RÉSUMÉ ===`);
console.log(`✅ Garanties devant avoir le bouton: ${garantiesDevantAvoirBouton}`);
console.log(`✅ Garanties ayant effectivement le bouton: ${garantiesAvecBoutonMainlevee}`);

if (garantiesDevantAvoirBouton === 0) {
  console.log("ℹ️ Aucune garantie n'a un statut permettant la mainlevée.");
  console.log("ℹ️ Créez une garantie avec le statut 'Active' pour tester le bouton.");
} else if (garantiesAvecBoutonMainlevee === garantiesDevantAvoirBouton) {
  console.log("🎉 SUCCÈS: Tous les boutons de mainlevée sont présents!");
} else {
  console.log("❌ PROBLÈME: Certains boutons de mainlevée sont manquants.");
}

// 5. Instructions pour l'utilisateur
console.log(`\n=== INSTRUCTIONS ===`);
console.log("1. Si aucune garantie n'a le statut 'Active', créez-en une pour tester");
console.log("2. Vérifiez que vous êtes connecté en tant qu'Administrateur");
console.log("3. Actualisez la page après avoir créé une garantie");
console.log("4. Le bouton mainlevée apparaît comme une icône 'poignée de main' verte");