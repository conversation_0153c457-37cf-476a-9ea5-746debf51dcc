import { ColumnDef } from "@tanstack/react-table"
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import PaginationControls from "@/components/shared/pagination-controls"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  pagination?: {
    page: number
    pageSize: number
    onPageChange: (page: number) => void
    totalCount?: number
  }
}

export function DataTable<TData, TValue>({
  columns,
  data,
  page,
  pageSize,
  onPageChange,
  totalCount,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  // Pagination
  let paginatedRows = table.getRowModel().rows;
  let totalPages = 1;
  if (page !== undefined && pageSize !== undefined) {
    totalPages = Math.ceil((totalCount ?? data.length) / pageSize);
    const start = (page - 1) * pageSize;
    paginatedRows = paginatedRows.slice(start, start + pageSize);
  }

  return (
    <div className="rounded-md border overflow-x-auto">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header, headerIdx) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {paginatedRows?.length ? (
            paginatedRows.map((row, rowIdx) => (
              <TableRow
                key={row.id || `row-${rowIdx}`}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell, cellIdx) => (
                  <TableCell key={`${rowIdx}-${cellIdx}-${cell.column.id}`}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                Aucun résultat.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {(page !== undefined && pageSize !== undefined && onPageChange) && (
        <PaginationControls
          currentPage={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          totalRecords={totalCount ?? data.length}
        />
      )}
    </div>
  )
}