// src/app/admin/audit-log/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { AuditLog, Utilisateur } from "@prisma/client";
import { ArrowUpDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"; // Pour afficher les JSON complets

// Type pour les données de log enrichies avec les infos utilisateur
export type AuditLogColumn = AuditLog & {
  utilisateur: Pick<Utilisateur, "id" | "nomUtilisateur" | "nom" | "prenom"> | null;
};

// Fonction pour formater les dates et heures
const formatDateTime = (dateString: Date | string | null | undefined) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString("fr-FR", {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    });
};

// Fonction pour afficher un aperçu du JSON et le JSON complet dans un tooltip
const JsonCell = ({ jsonData }: { jsonData: any }) => {
    if (!jsonData || Object.keys(jsonData).length === 0) return <span className="text-xs text-muted-foreground">Vide</span>;
    const preview = JSON.stringify(jsonData).substring(0, 50) + (JSON.stringify(jsonData).length > 50 ? "..." : "");

    return (
        <TooltipProvider delayDuration={100}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <span className="text-xs cursor-help">{preview}</span>
                </TooltipTrigger>
                <TooltipContent className="max-w-md max-h-80 overflow-auto bg-popover text-popover-foreground p-2 border rounded-md shadow-lg">
                    <pre className="text-xs">{JSON.stringify(jsonData, null, 2)}</pre>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
};


export const columns: ColumnDef<AuditLogColumn>[] = [
  {
    accessorKey: "timestamp",
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        Date & Heure <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => formatDateTime(row.getValue("timestamp")),
  },
  {
    accessorKey: "utilisateur",
    header: "Utilisateur",
    cell: ({ row }) => {
      const utilisateur = row.original.utilisateur;
      if (!utilisateur) return <span className="text-muted-foreground">Système/Inconnu</span>;
      return (
        <div className="font-medium">
          {utilisateur.prenom} {utilisateur.nom}
          <div className="text-xs text-muted-foreground">@{utilisateur.nomUtilisateur} (ID: {utilisateur.id})</div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    cell: ({ row }) => {
        const action = row.getValue("action") as string;
        let variant: "default" | "secondary" | "destructive" | "outline" = "secondary";
        if (action.includes("CREATE")) variant = "default"; // Vert (si configuré)
        else if (action.includes("UPDATE") || action.includes("DEACTIVATE")) variant = "default"; // Bleu/Jaune (si configuré)
        else if (action.includes("DELETE")) variant = "destructive";

        return <Badge variant={variant} className={
            action.includes("CREATE") ? "bg-green-100 text-green-700 border-green-300" :
            action.includes("UPDATE") || action.includes("DEACTIVATE") ? "bg-blue-100 text-blue-700 border-blue-300" :
            action.includes("DELETE") ? "bg-red-100 text-red-700 border-red-300" : ""
        }>{action}</Badge>;
    }
  },
  {
    accessorKey: "entite",
    header: "Entité",
  },
  {
    accessorKey: "entiteId",
    header: "ID Entité",
    cell: ({ row }) => row.getValue("entiteId") || <span className="text-muted-foreground">N/A</span>,
  },
  {
    accessorKey: "descriptionAction",
    header: "Description",
    cell: ({ row }) => <span className="text-sm">{row.getValue("descriptionAction") || "-"}</span>,
  },
  {
    accessorKey: "nouvellesValeurs",
    header: "Nouvelles Valeurs",
    cell: ({ row }) => <JsonCell jsonData={row.getValue("nouvellesValeurs")} />,
  },
  //   { // Décommenter si vous stockez et voulez afficher les anciennes valeurs
  //     accessorKey: "ancienValeurs",
  //     header: "Anciennes Valeurs",
  //     cell: ({ row }) => <JsonCell jsonData={row.getValue("ancienValeurs")} />,
  //   },
  {
    accessorKey: "adresseIp",
    header: "Adresse IP",
    cell: ({ row }) => row.getValue("adresseIp") || <span className="text-muted-foreground">N/A</span>,
  },
  {
    accessorKey: "userAgent",
    header: "User Agent",
    cell: ({ row }) => {
        const ua = row.getValue("userAgent") as string | null;
        if (!ua) return <span className="text-muted-foreground">N/A</span>;
        return (
            <TooltipProvider delayDuration={100}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <span className="text-xs cursor-help">{ua.substring(0,30) + (ua.length > 30 ? "..." : "")}</span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-sm">
                        <p className="text-xs">{ua}</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        )
    }
  },
];