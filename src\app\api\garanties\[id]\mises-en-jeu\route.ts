// src/app/api/garanties/[id]/mises-en-jeu/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutGarantie, StatutMiseEnJeu } from "@/types/enums";
import { DemandeMiseEnJeuSchema } from "@/lib/schemas/mise-en-jeu.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams {
  params: { id: string }; // id de la Garantie
}

// --- Ajout de la classe d'erreur métier personnalisée ---
class BusinessRuleError extends Error {
  code: string;
  constructor(message: string, code: string) {
    super(message);
    this.name = 'BusinessRuleError';
    this.code = code;
  }
}

// POST: Créer une nouvelle demande de mise en jeu pour une garantie
export async function POST(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  // Qui peut demander une mise en jeu ? Partenaire pour ses garanties, ou agent GesGar.
  if (!session || ![RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar, RoleUtilisateur.Partenaire].includes(session.user?.role as RoleUtilisateur)) {
    return NextResponse.json({ message: "Non autorisé à demander une mise en jeu" }, { status: 403 });
  }

  const garantieIdNum = parseInt(resolvedParams.id);
  if (isNaN(garantieIdNum)) {
    return NextResponse.json({ message: "ID de garantie invalide." }, { status: 400 });
  }

  const demandeurId = session.user?.id ? parseInt(session.user.id) : undefined;
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';

  return auditContext.run({ 
    userId: demandeurId, 
    ip: ipAddress, 
    userAgent 
  }, async () => {
    try {
      const body = await request.json();
      if (body.dateDemande) body.dateDemande = new Date(body.dateDemande);

      const validation = DemandeMiseEnJeuSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de demande de mise en jeu invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const { dateDemande, montantDemandeStr, motifDemande } = validation.data;
      let montantDemandeDecimal: Decimal;
      try {
        montantDemandeDecimal = new Decimal(montantDemandeStr.replace(',', '.'));
      } catch (e) {
        return NextResponse.json({ message: "Montant demandé invalide" }, { status: 400 });
      }

      // --- Début de la logique transactionnelle ---
      const result = await prisma.$transaction(async (tx: any) => {
        // 1. Vérifier la garantie parente
        const garantie = await tx.garantie.findUnique({
          where: { id: garantieIdNum },
        });
        if (!garantie) throw new BusinessRuleError("Garantie non trouvée.", "GARANTIE_NOT_FOUND");

        // Logique de vérification du statut de la garantie pour permettre une demande de mise en jeu
        // Ex: Doit être 'Active' ou 'EnSouffrance'. Ne peut pas être déjà 'MiseEnJeuPayee', 'Echue', 'MainleveeAccordee', 'Radiee'.
        const statutsPermettantMiseEnJeu: StatutGarantie[] = [StatutGarantie.Active, StatutGarantie.EnSouffrance];
        if (!statutsPermettantMiseEnJeu.includes(garantie.statut)) {
          throw new BusinessRuleError(
            `Impossible de demander une mise en jeu pour une garantie au statut '${garantie.statut}'.`,
            "STATUT_GARANTIE_INVALID"
          );
        }

        // 2. Vérifier si une demande de mise en jeu non traitée/refusée existe déjà
        const existingMiseEnJeu = await tx.miseEnJeu.findFirst({
            where: {
                garantieId: garantieIdNum,
                statut: { notIn: [StatutMiseEnJeu.Refusee, StatutMiseEnJeu.Payee] } // Ou autres statuts "fermés"
            }
        });
        if (existingMiseEnJeu) {
            throw new BusinessRuleError(
              `Une demande de mise en jeu (Statut: ${existingMiseEnJeu.statut}) est déjà en cours ou a été traitée pour cette garantie.`,
              "MISE_EN_JEU_EXISTANTE"
            );
        }

        // 3. Vérifier que le montant demandé ne dépasse pas le montant garanti
        if (montantDemandeDecimal.greaterThan(garantie.montantGarantie)) {
            throw new BusinessRuleError(
              `Le montant demandé (${montantDemandeDecimal.toFixed(2)}) ne peut pas dépasser le montant garanti (${garantie.montantGarantie.toFixed(2)}).`,
              "MONTANT_SUPERIEUR_A_GARANTIE"
            );
        }

        // 4. Créer la demande de mise en jeu
        const newMiseEnJeu = await tx.miseEnJeu.create({
          data: {
            garantieId: garantieIdNum,
            dateDemande,
            montantDemande: montantDemandeDecimal,
            motifDemande,
            statut: StatutMiseEnJeu.Demandee, // Statut initial
            utilisateurCreationId: demandeurId,
          },
        });

        // 5. Mettre à jour le statut de la garantie parente
        await tx.garantie.update({
          where: { id: garantieIdNum },
          data: {
            statut: StatutGarantie.MiseEnJeuDemandee,
            utilisateurModificationId: demandeurId,
          },
        });

        return newMiseEnJeu;
      });
      // --- Fin de la logique transactionnelle ---

      return NextResponse.json(result, { status: 201 });

    } catch (error: any) {
      console.error(`Erreur POST /api/garanties/${garantieIdNum}/mises-en-jeu:`, error);
      if (error instanceof BusinessRuleError) {
        return NextResponse.json({ message: error.message, code: error.code }, { status: 400 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur lors de la création de la demande de mise en jeu." }, { status: 500 });
    }
  });
}

// TODO: GET pour lister les mises en jeu d'une garantie (pour la page de détail garantie)

export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session) return NextResponse.json({ message: "Non autorisé" }, { status: 403 });

  const garantieIdNum = parseInt(resolvedParams.id);
  if (isNaN(garantieIdNum)) {
    return NextResponse.json({ message: "ID de garantie invalide." }, { status: 400 });
  }

  try {
    const misesEnJeu = await prisma.miseEnJeu.findMany({
      where: { garantieId: garantieIdNum },
      orderBy: { dateDemande: "desc" },
    });
    return NextResponse.json(misesEnJeu, { status: 200 });
  } catch (e) {
    return NextResponse.json({ message: "Erreur lors de la récupération des mises en jeu." }, { status: 500 });
  }
}