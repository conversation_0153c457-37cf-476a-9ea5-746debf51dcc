// src/lib/prisma.ts
import { PrismaClient } from '@prisma/client';
import { enhancedAuditLogMiddleware } from './enhanced-audit.middleware'; // Importer le middleware d'audit amélioré

declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

const createPrismaClient = () => {
  const client = new PrismaClient({
    // log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : [],
  });

  // Appliquer le middleware
  client.$use(enhancedAuditLogMiddleware());

  return client;
};

const prisma = global.prisma || createPrismaClient();

if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
}

export default prisma;