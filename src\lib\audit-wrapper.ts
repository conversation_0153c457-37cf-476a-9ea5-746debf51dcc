// src/lib/audit-wrapper.ts

if (typeof window !== 'undefined') {
  throw new Error("audit-wrapper ne doit jamais être importé côté client !");
}

import { headers } from 'next/headers';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { 
  enhancedAuditContext, 
  createAuditContext, 
  logCustomAuditEvent, 
  BusinessAction,
  type AuditContext 
} from './enhanced-audit.middleware';
import { randomUUID } from 'crypto';

// Interface pour les options d'audit
interface AuditOptions {
  module?: string;
  operation?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  skipAuth?: boolean; // Pour les opérations système
}

// Wrapper principal pour les opérations auditées
export async function withAudit<T>(
  operation: () => Promise<T>,
  options: AuditOptions = {}
): Promise<T> {
  // Récupérer les informations de session et de requête
  const session = await getServerSession(authOptions);
  const headersList = await headers();
  
  const ip = headersList.get('x-forwarded-for') ||
             headersList.get('x-real-ip') ||
             'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';
  const requestId = randomUUID();
  
  // Vérifier l'authentification si nécessaire
  if (!options.skipAuth && !session?.user?.id) {
    // Logger la tentative d'accès non autorisé
    await logCustomAuditEvent(BusinessAction.UNAUTHORIZED_ACCESS_ATTEMPT, {
      description: 'Tentative d\'accès sans authentification',
      metadata: {
        ip,
        userAgent,
        module: options.module,
        operation: options.operation
      },
      criticalityLevel: 'HIGH'
    });
    throw new Error('Non authentifié');
  }

  // Créer le contexte d'audit
  const userId = session?.user?.id ? parseInt(session.user.id as string) : 0;
  const auditContext = createAuditContext(
    userId,
    ip,
    userAgent,
    {
      sessionId: session?.user?.id ? `session_${session.user.id}_${Date.now()}` : undefined,
      requestId,
      module: options.module,
      operation: options.operation,
      resourceId: options.resourceId,
      metadata: options.metadata
    }
  );

  // Exécuter l'opération dans le contexte d'audit
  return enhancedAuditContext.run(auditContext, operation);
}

// Wrapper spécialisé pour les opérations d'authentification
export async function withAuthAudit<T>(
  operation: () => Promise<T>,
  action: BusinessAction,
  details: {
    username?: string;
    success?: boolean;
    reason?: string;
    metadata?: Record<string, any>;
  }
): Promise<T> {
  const headersList = await headers();
  const ip = headersList.get('x-forwarded-for') ||
             headersList.get('x-real-ip') ||
             'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';

  try {
    const result = await operation();
    
    // Logger le succès
    await logCustomAuditEvent(action, {
      description: `${action} - Succès${details.username ? ` pour ${details.username}` : ''}`,
      metadata: {
        ip,
        userAgent,
        username: details.username,
        success: true,
        ...details.metadata
      },
      criticalityLevel: 'HIGH'
    });

    return result;
  } catch (error) {
    // Logger l'échec
    await logCustomAuditEvent(
      action === BusinessAction.LOGIN ? BusinessAction.LOGIN_FAILED : action,
      {
        description: `${action} - Échec${details.username ? ` pour ${details.username}` : ''}: ${details.reason || (error as Error).message}`,
        metadata: {
          ip,
          userAgent,
          username: details.username,
          success: false,
          error: (error as Error).message,
          ...details.metadata
        },
        criticalityLevel: 'HIGH'
      }
    );
    throw error;
  }
}

// Wrapper pour les opérations de configuration système
export async function withSystemConfigAudit<T>(
  operation: () => Promise<T>,
  action: BusinessAction,
  details: {
    configKey?: string;
    oldValue?: any;
    newValue?: any;
    metadata?: Record<string, any>;
  }
): Promise<T> {
  return withAudit(
    async () => {
      const result = await operation();
      
      // Logger l'action de configuration
      await logCustomAuditEvent(action, {
        entite: 'ParametreSysteme',
        entiteId: details.configKey,
        description: `Configuration système modifiée: ${details.configKey}`,
        metadata: {
          configKey: details.configKey,
          oldValue: details.oldValue,
          newValue: details.newValue,
          ...details.metadata
        },
        criticalityLevel: 'CRITICAL'
      });

      return result;
    },
    {
      module: 'SYSTEM_CONFIG',
      operation: action,
      resourceId: details.configKey,
      metadata: details.metadata
    }
  );
}

// Wrapper pour les opérations de gestion des utilisateurs
export async function withUserManagementAudit<T>(
  operation: () => Promise<T>,
  action: BusinessAction,
  details: {
    targetUserId?: string;
    targetUsername?: string;
    changes?: Record<string, any>;
    metadata?: Record<string, any>;
  }
): Promise<T> {
  return withAudit(
    async () => {
      const result = await operation();
      
      await logCustomAuditEvent(action, {
        entite: 'Utilisateur',
        entiteId: details.targetUserId,
        description: `Gestion utilisateur: ${action}${details.targetUsername ? ` pour ${details.targetUsername}` : ''}`,
        metadata: {
          targetUserId: details.targetUserId,
          targetUsername: details.targetUsername,
          changes: details.changes,
          ...details.metadata
        },
        criticalityLevel: 'HIGH'
      });

      return result;
    },
    {
      module: 'USER_MANAGEMENT',
      operation: action,
      resourceId: details.targetUserId,
      metadata: details.metadata
    }
  );
}

// Wrapper pour les opérations financières critiques
export async function withFinancialAudit<T>(
  operation: () => Promise<T>,
  action: BusinessAction,
  details: {
    entityType: string;
    entityId?: string;
    amount?: number;
    currency?: string;
    reference?: string;
    metadata?: Record<string, any>;
  }
): Promise<T> {
  return withAudit(
    async () => {
      const result = await operation();
      
      await logCustomAuditEvent(action, {
        entite: details.entityType,
        entiteId: details.entityId,
        description: `Opération financière: ${action}${details.amount ? ` - Montant: ${details.amount} ${details.currency || ''}` : ''}`,
        metadata: {
          amount: details.amount,
          currency: details.currency,
          reference: details.reference,
          ...details.metadata
        },
        criticalityLevel: 'CRITICAL'
      });

      return result;
    },
    {
      module: 'FINANCIAL_OPERATIONS',
      operation: action,
      resourceId: details.entityId,
      metadata: details.metadata
    }
  );
}

// Wrapper pour les accès aux données sensibles
export async function withSensitiveDataAccess<T>(
  operation: () => Promise<T>,
  details: {
    dataType: string;
    accessReason?: string;
    resourceIds?: string[];
    metadata?: Record<string, any>;
  }
): Promise<T> {
  return withAudit(
    async () => {
      const result = await operation();
      
      await logCustomAuditEvent(BusinessAction.SENSITIVE_DATA_ACCESS, {
        entite: details.dataType,
        description: `Accès aux données sensibles: ${details.dataType}${details.accessReason ? ` - Raison: ${details.accessReason}` : ''}`,
        metadata: {
          dataType: details.dataType,
          accessReason: details.accessReason,
          resourceIds: details.resourceIds,
          recordCount: Array.isArray(result) ? result.length : 1,
          ...details.metadata
        },
        criticalityLevel: 'HIGH'
      });

      return result;
    },
    {
      module: 'DATA_ACCESS',
      operation: 'SENSITIVE_DATA_ACCESS',
      metadata: details.metadata
    }
  );
}

// Wrapper pour les imports/exports de données
export async function withDataTransferAudit<T>(
  operation: () => Promise<T>,
  action: BusinessAction.DATA_IMPORT | BusinessAction.DATA_EXPORT,
  details: {
    dataType: string;
    recordCount?: number;
    fileInfo?: {
      name: string;
      size: number;
      type: string;
    };
    metadata?: Record<string, any>;
  }
): Promise<T> {
  return withAudit(
    async () => {
      const result = await operation();
      
      await logCustomAuditEvent(action, {
        entite: details.dataType,
        description: `${action}: ${details.dataType}${details.recordCount ? ` - ${details.recordCount} enregistrements` : ''}`,
        metadata: {
          dataType: details.dataType,
          recordCount: details.recordCount,
          fileInfo: details.fileInfo,
          ...details.metadata
        },
        criticalityLevel: 'HIGH'
      });

      return result;
    },
    {
      module: 'DATA_TRANSFER',
      operation: action,
      metadata: details.metadata
    }
  );
}

// Fonction pour logger les erreurs système
export async function logSystemError(
  error: Error,
  context: {
    operation?: string;
    module?: string;
    userId?: number;
    metadata?: Record<string, any>;
  }
) {
  try {
    const headersList = await headers();
    const ip = headersList.get('x-forwarded-for') || 'unknown';
    const userAgent = headersList.get('user-agent') || 'unknown';

    const auditContext = createAuditContext(
      context.userId || 0,
      ip,
      userAgent,
      {
        module: context.module,
        operation: context.operation,
        metadata: context.metadata
      }
    );

    await enhancedAuditContext.run(auditContext, async () => {
      await logCustomAuditEvent(BusinessAction.SYSTEM_ERROR, {
        description: `Erreur système: ${error.message}`,
        metadata: {
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack,
          operation: context.operation,
          module: context.module,
          ...context.metadata
        },
        criticalityLevel: 'HIGH'
      });
    });
  } catch (auditError) {
    console.error('[AUDIT] Erreur lors de l\'audit d\'erreur système:', auditError);
  }
}

// Export des types pour utilisation dans d'autres modules
export type { AuditContext, AuditOptions };
export { BusinessAction };