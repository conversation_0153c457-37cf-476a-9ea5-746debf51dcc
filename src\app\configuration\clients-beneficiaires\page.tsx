// src/app/configuration/clients-beneficiaires/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, ClientBeneficiaire, TypeClient, GenreClient, SecteurActivite } from "@prisma/client";
import { getClientBeneficiaireColumns, ClientBeneficiaireColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { ClientBeneficiaireFormValues, ClientBeneficiaireSchema } from "@/lib/schemas/client-beneficiaire.schema";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, /* ... */ } from "@/components/ui/alert-dialog";
import { PlusCircle, Eye } from "lucide-react";

type SelectOption = { value: string; label: string };

const typeClientOptions = Object.values(TypeClient).map(val => ({ value: val, label: val.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim() }));
const genreClientOptions = Object.values(GenreClient).map(val => ({ value: val, label: val.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim() }));


export default function ClientsBeneficiairesPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [clients, setClients] = useState<ClientBeneficiaireColumn[]>([]);
  const [secteursActivite, setSecteursActivite] = useState<SelectOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<ClientBeneficiaireColumn | null>(null);
  const [clientToDelete, setClientToDelete] = useState<ClientBeneficiaireColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [readOnly, setReadOnly] = useState(false);

  const form = useForm<ClientBeneficiaireFormValues>({
    resolver: zodResolver(ClientBeneficiaireSchema),
    defaultValues: { /* ... valeurs par défaut pour tous les champs du schéma Zod ... */
        nomOuRaisonSociale: "", typeClient: undefined, identifiantUnique: "",
        contactAdressePhysique: "", contactEmail: "", contactTelephone1: "", contactTelephone2: "",
        secteurActivitePrincipalId: "", age: "", genre: undefined,
    },
  });

  // Watch typeClient pour afficher/masquer age et genre
  const typeClientSelected = form.watch("typeClient");

  const fetchSecteurs = useCallback(async () => {
    console.log("fetchSecteurs: start");
    try {
      const secteursRes = await fetch("/api/configuration/secteurs-activite");
      if (!secteursRes.ok) throw new Error("Échec chargement secteurs");
      const secteursData: SecteurActivite[] = await secteursRes.json();
      setSecteursActivite(secteursData.map(s => ({ value: s.id.toString(), label: s.nom })));
    } catch (error: any) { toast({ title: "Erreur chargement secteurs", variant: "destructive" }); }
    console.log("fetchSecteurs: end");
  }, [toast]);


  const fetchClientsData = useCallback(async (showLoading = true) => {
    console.log("fetchClientsData: start");
    try {
      const res = await fetch("/api/configuration/clients-beneficiaires");
      if (!res.ok) throw new Error("Erreur chargement clients");
      const data = await res.json();
      console.log("clients API:", data);
      // Si besoin, adapte ici le mapping pour correspondre à ClientBeneficiaireColumn
      setClients(Array.isArray(data) ? data : []);
    } catch (error: any) { toast({ title: "Erreur chargement clients", variant: "destructive" }); }
    console.log("fetchClientsData: end");
  }, [toast]);
  useEffect(() => {
    console.log("ClientsPage useEffect triggered. sessionStatus:", sessionStatus, "initialDataLoaded:", initialDataLoaded, "session:", !!session);
    if (sessionStatus === "loading") { setIsLoading(true); return; }
    if (
      !session ||
      !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
    ) {
      /* ... accès refusé ... */
      return;
    }
    if (session && !initialDataLoaded) {
      (async () => {
        try {
          await Promise.all([fetchSecteurs(), fetchClientsData()]);
          setInitialDataLoaded(true);
        } catch (err) {
          // Les erreurs sont déjà toastées dans les fetch, mais tu peux logger ici si tu veux
          console.error("Erreur lors du chargement initial des données", err);
        }
      })();
    } else if (session && initialDataLoaded) {
      setIsLoading(false);
    }
  }, [session, sessionStatus, initialDataLoaded, router, toast, fetchClientsData, fetchSecteurs]);

  const refreshData = useCallback(async () => { await fetchClientsData(false); }, [fetchClientsData]);

  const handleEdit = useCallback((client: ClientBeneficiaireColumn) => {
    setEditingClient(client);
    const contact = client.informationsContact as any;
    const autresInfos = client.autresInformations as any;
    form.reset({
      nomOuRaisonSociale: client.nomOuRaisonSociale,
      typeClient: client.typeClient,
      identifiantUnique: client.identifiantUnique || "",
      contactAdressePhysique: contact?.adressePhysique || "",
      contactEmail: contact?.email || "",
      contactTelephone1: contact?.telephone1 || "",
      contactTelephone2: contact?.telephone2 || "",
      secteurActivitePrincipalId: client.secteurActivitePrincipalId?.toString() || "",
      age: client.age?.toString() || "",
      genre: client.genre || undefined,
      dateCreationEntreprise: autresInfos?.dateCreationEntreprise || "",
      formeJuridique: autresInfos?.formeJuridique || "",
      numeroIdentificationFiscale: autresInfos?.numeroIdentificationFiscale || "",
    });
    setIsFormOpen(true);
  }, [form]);

  const handleDeleteConfirm = useCallback((client: ClientBeneficiaireColumn) => { /* ... */ }, []);
  const handleDelete = useCallback(async () => { /* ... */ }, [clientToDelete, toast, refreshData]);
  const onSubmit = useCallback(async (values: ClientBeneficiaireFormValues) => {
    const autresInformations = {
      dateCreationEntreprise: values.dateCreationEntreprise || undefined,
      formeJuridique: values.formeJuridique || undefined,
      numeroIdentificationFiscale: values.numeroIdentificationFiscale || undefined,
    };
    const contactAdressePhysique = values.contactAdressePhysique || "";
    const contactEmail = values.contactEmail || "";
    const contactTelephone1 = values.contactTelephone1 || "";
    const contactTelephone2 = values.contactTelephone2 || null;
    const payload = {
      ...values,
      autresInformations,
    };
    console.log("payload envoyé", payload);
    delete payload.dateCreationEntreprise;
    delete payload.formeJuridique;
    delete payload.numeroIdentificationFiscale;

    try {
      let res;
      if (editingClient) {
        // Edition
        res = await fetch(`/api/configuration/clients-beneficiaires/${editingClient.id}` , {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });
      } else {
        // Création
        res = await fetch("/api/configuration/clients-beneficiaires", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });
      }
      if (!res.ok) {
        const data = await res.json();
        toast({ title: "Erreur", description: data.message || "Erreur inconnue", variant: "destructive" });
        return;
      }
      toast({ title: "Succès", description: editingClient ? "Client modifié" : "Client créé" });
      setIsFormOpen(false);
      await refreshData();
    } catch (err: any) {
      toast({ title: "Erreur réseau", description: err.message, variant: "destructive" });
    }
  }, [editingClient, form, toast, refreshData]);

  const columns = useMemo(
    () => getClientBeneficiaireColumns({
      onEdit: (client, readOnlyMode = false) => {
        setReadOnly(!!readOnlyMode);
        handleEdit(client);
      },
      onDelete: handleDeleteConfirm,
    }),
    [handleEdit, handleDeleteConfirm]
  );

  if (isLoading) return <div className="p-6 text-center">Chargement des clients...</div>;
  // ... (vérification de session pour accès) ...

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestion des Clients Bénéficiaires</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => {
          if (!open) {
            setEditingClient(null);
            form.reset();
            setReadOnly(false);
          }
          setIsFormOpen(open);
        }}>
          <DialogTrigger asChild><Button onClick={() => { /* ... */ }}><PlusCircle className="mr-2 h-4 w-4" /> Ajouter un Client</Button></DialogTrigger>
          <DialogContent className="sm:max-w-2xl lg:max-w-3xl"> {/* Plus large */}
            <DialogHeader><DialogTitle>{editingClient ? "Modifier le Client" : "Ajouter un Client Bénéficiaire"}</DialogTitle></DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4 max-h-[80vh] overflow-y-auto pr-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="nomOuRaisonSociale" render={({ field }) => ( <FormItem><FormLabel>Nom / Raison Sociale <span className="text-red-500">*</span></FormLabel><FormControl><Input {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="typeClient" render={({ field }) => (
                        <FormItem><FormLabel>Type de Client <span className="text-red-500">*</span></FormLabel>
                            <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value} disabled={readOnly}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir un type..." /></SelectTrigger></FormControl>
                                <SelectContent>{typeClientOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                            </Select><FormMessage />
                        </FormItem>
                    )} />
                </div>
                <FormField control={form.control} name="identifiantUnique" render={({ field }) => ( <FormItem><FormLabel>Identifiant Unique (NINEA, RC)</FormLabel><FormControl><Input {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>

                {/* Champs conditionnels pour Personne Physique */}
                {typeClientSelected === TypeClient.PersonnePhysique && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-md bg-slate-50 dark:bg-slate-800">
                         <FormField control={form.control} name="age" render={({ field }) => (
                            <FormItem><FormLabel>Âge <span className="text-red-500">*</span></FormLabel><FormControl><Input type="number" {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem>
                        )} />
                        <FormField control={form.control} name="genre" render={({ field }) => (
                            <FormItem><FormLabel>Genre <span className="text-red-500">*</span></FormLabel>
                                <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value} disabled={readOnly}>
                                    <FormControl><SelectTrigger><SelectValue placeholder="Choisir un genre..." /></SelectTrigger></FormControl>
                                    <SelectContent>{genreClientOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                                </Select><FormMessage />
                            </FormItem>
                        )} />
                    </div>
                )}

                <h3 className="text-lg font-medium border-b pb-2 pt-3">Informations de Contact <span className="text-red-500">*</span></h3>
                <FormField control={form.control} name="contactAdressePhysique" render={({ field }) => ( <FormItem><FormLabel>Adresse Physique <span className="text-red-500">*</span></FormLabel><FormControl><Textarea {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="contactEmail" render={({ field }) => ( <FormItem><FormLabel>Email <span className="text-red-500">*</span></FormLabel><FormControl><Input type="email" {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="contactTelephone1" render={({ field }) => ( <FormItem><FormLabel>Téléphone 1 <span className="text-red-500">*</span></FormLabel><FormControl><Input {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>
                </div>
                <FormField control={form.control} name="contactTelephone2" render={({ field }) => ( <FormItem><FormLabel>Téléphone 2</FormLabel><FormControl><Input {...field} disabled={readOnly} /></FormControl><FormMessage /></FormItem> )}/>

                <FormField control={form.control} name="secteurActivitePrincipalId" render={({ field }) => (
                    <FormItem><FormLabel>Secteur d'Activité Principal (Optionnel)</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value} disabled={readOnly}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir un secteur..." /></SelectTrigger></FormControl>
                            <SelectContent>{secteursActivite.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                {typeClientSelected !== TypeClient.PersonnePhysique && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField control={form.control} name="dateCreationEntreprise" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date de création</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} disabled={readOnly} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="formeJuridique" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Forme Juridique</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={readOnly} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="numeroIdentificationFiscale" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Numéro Identification Fiscale</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={readOnly} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                )}
                <DialogFooter className="pt-4">
                  <DialogClose asChild>
                    <Button variant="outline">Annuler</Button>
                  </DialogClose>
                  {!readOnly && (
                    <Button type="submit" disabled={form.formState.isSubmitting}>Sauvegarder</Button>
                  )}
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      <DataTable columns={columns} data={clients} />
      {/* TODO: Add AlertDialog here if needed */}
      {clientToDelete && null}
    </div>
  );
}