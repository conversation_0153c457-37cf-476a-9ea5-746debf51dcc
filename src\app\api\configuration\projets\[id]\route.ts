// src/app/api/configuration/projets/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur } from "@prisma/client";
import { ProjetSchema } from "@/lib/schemas/projet.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams { params: { id: string } }

// GET: Récupérer un projet spécifique
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  try {
    const projet = await prisma.projet.findUnique({
        where: { id },
        include: { // Pour pré-remplir les selects du formulaire
            secteurActivite: { select: { id: true, nom: true } },
            clientBeneficiaire: { select: { id: true, nomOuRaisonSociale: true } },
        }
    });
    if (!projet) return NextResponse.json({ message: "Projet non trouvé" }, { status: 404 });
    return NextResponse.json(projet);
  } catch (error) {
    console.error(`Erreur GET /api/configuration/projets/${id}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// PUT: Mettre à jour un projet
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  return auditContext.run(
    { userId: modifierId },
    async () => {
    try {
      const body = await request.json();
      const validation = ProjetSchema.safeParse(body);
      if (!validation.success) return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });

      const {
        nom, description, secteurActiviteId, clientBeneficiaireId,
        localisationRegion, localisationDepartement, localisationCommune, localisationAdressePrecise,
        coutTotalProjetStr, autresInformationsStr
      } = validation.data;

      const secteurId = parseInt(secteurActiviteId);
      const clientId = parseInt(clientBeneficiaireId);

      const secteurExists = await prisma.secteurActivite.findUnique({ where: { id: secteurId } });
      if (!secteurExists) return NextResponse.json({ message: "Secteur d'activité non valide." }, { status: 400 });
      const clientExists = await prisma.clientBeneficiaire.findUnique({ where: { id: clientId } });
      if (!clientExists) return NextResponse.json({ message: "Client bénéficiaire non valide." }, { status: 400 });

      const localisationJson = { /* ... */ }; // Comme dans POST
      let coutTotalProjetDecimal: Decimal | null = null; // Comme dans POST
      if (coutTotalProjetStr && coutTotalProjetStr.trim() !== "") {
        coutTotalProjetDecimal = new Decimal(coutTotalProjetStr.replace(',', '.'));
      }
      let autresInformationsJson = null; // Comme dans POST
      if (autresInformationsStr && autresInformationsStr.trim() !== "") {
        try { autresInformationsJson = JSON.parse(autresInformationsStr); }
        catch (e) { return NextResponse.json({ message: "Format JSON invalide pour 'Autres Informations'." }, { status: 400 }); }
      }


      const updatedProjet = await prisma.projet.update({
        where: { id },
        data: {
          nom, description, secteurActiviteId: secteurId, clientBeneficiaireId: clientId,
          localisation: Object.values(localisationJson).some(v => typeof v === "string" && v.trim() !== "") ? localisationJson : undefined,
          coutTotalProjet: coutTotalProjetDecimal,
          autresInformations: autresInformationsJson,
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedProjet);
    } catch (error: any) {
      console.error(`Erreur PUT /api/configuration/projets/${id}:`, error);
      return NextResponse.json(
        { message: "Erreur interne du serveur" },
        { status: 500 },
      );
     }
  });
}

// DELETE: Supprimer un projet
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      // Vérifier si le projet est lié à des Garanties
      const garantiesCount = await prisma.garantie.count({ where: { projetId: id } });
      if (garantiesCount > 0) {
        return NextResponse.json({ message: `Impossible de supprimer: lié à ${garantiesCount} garantie(s).` }, { status: 400 });
      }
      await prisma.projet.delete({ where: { id } });
      return NextResponse.json({ message: "Projet supprimé avec succès" }, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur DELETE /api/configuration/projets/${id}:`, error);
      if (error.code === 'P2025')
        return NextResponse.json({ message: "Projet non trouvé" }, { status: 404 });
      return NextResponse.json(
        { message: "Erreur interne du serveur" },
        { status: 500 },
      );
     }
  });
}