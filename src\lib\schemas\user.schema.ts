// src/lib/schemas/user.schema.ts
import { z } from "zod";
import { RoleUtilisateur } from "@prisma/client"; // Importer l'enum depuis Prisma

// Convertir l'enum Prisma en un tableau de chaînes pour Zod
const roleUtilisateurValues = Object.values(RoleUtilisateur) as [string, ...string[]];

export const CreateUserSchema = z.object({
  nomUtilisateur: z.string().min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères.").max(50),
  email: z.string().email("Adresse email invalide."),
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères.").max(50),
  prenom: z.string().min(2, "Le prénom doit contenir au moins 2 caractères.").max(50),
  role: z.enum(roleUtilisateurValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un rôle valide." }),
  }),
  motDePasse: z.string().min(8, "Le mot de passe doit contenir au moins 8 caractères."),
  // photoUrl: z.string().url("URL de la photo invalide.").optional().or(z.literal('')), // Optionnel
});

export type CreateUserFormValues = z.infer<typeof CreateUserSchema>;

// Nouveau schéma pour la mise à jour
export const UpdateUserSchema = z.object({
  nomUtilisateur: z.string().min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères.").max(50),
  email: z.string().email("Adresse email invalide."),
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères.").max(50),
  prenom: z.string().min(2, "Le prénom doit contenir au moins 2 caractères.").max(50),
  role: z.enum(roleUtilisateurValues, {
    errorMap: () => ({ message: "Veuillez sélectionner un rôle valide." }),
  }),
  // Le mot de passe est optionnel lors de la mise à jour
  // Si fourni, il doit respecter la contrainte de longueur.
  // S'il est vide, on ne le met pas à jour.
  motDePasse: z.string().min(8, "Le mot de passe doit contenir au moins 8 caractères.").optional().or(z.literal('')),
  // photoUrl: z.string().url("URL de la photo invalide.").optional().or(z.literal('')),
});
export type UpdateUserFormValues = z.infer<typeof UpdateUserSchema>;
