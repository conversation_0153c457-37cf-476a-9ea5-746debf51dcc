"use client";

import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, StatutMainlevee } from "@/types/enums";
import { getMainleveeDemandeColumns, MainleveeDemandeColumn } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { z } from "zod";
import { DecisionMainleveeSchema } from "@/lib/schemas/mainlevee.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import PaginationControls from "@/components/shared/pagination-controls";

const decisionStatutOptions = [
    { value: StatutMainlevee.Accordee, label: "Accordée" },
    { value: StatutMainlevee.Refusee, label: "Refusée" },
];

interface MainleveeApiResponse {
    data: MainleveeDemandeColumn[];
    totalPages: number;
    currentPage: number;
    totalRecords: number;
}

export default function MainleveesEnAttentePage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [demandes, setDemandes] = useState<MainleveeDemandeColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pageStatus, setPageStatus] = useState<"loading" | "loaded" | "error" | "unauthorized">("loading");
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);

  const [isDecisionFormOpen, setIsDecisionFormOpen] = useState(false);
  const [processingDemande, setProcessingDemande] = useState<MainleveeDemandeColumn | null>(null);

  const decisionForm = useForm({
    resolver: zodResolver(DecisionMainleveeSchema),
    defaultValues: {
      statut: "",
      dateDecision: new Date(),
      montantRecupereStr: "",
      commentairesDecision: "",
      raisonRefus: "",
    },
  });

  const fetchDemandes = useCallback(async (page = 1) => {
    setIsLoading(true);
    try {
      // ... rest of the logic
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
      if (pageStatus === "loading") setPageStatus("error");
    } finally {
        if (pageStatus === "loading" || isLoading) setIsLoading(false);
    }
  }, [toast, pageStatus, isLoading]);

  useEffect(() => {
    if (sessionStatus === "loading") { setPageStatus("loading"); return; }
    const allowedRoles = ["Administrateur", "GestionnaireGesGar"];
    if (!session || !allowedRoles.includes(session.user?.role)) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/"); setPageStatus("unauthorized"); return;
    }
    if (session && (pageStatus === "loading" || pageStatus === "error")) {
        fetchDemandes(currentPage);
    }
  }, [session, sessionStatus, pageStatus, router, toast, fetchDemandes, currentPage]);

  const handleProcessDemande = useCallback((demande: MainleveeDemandeColumn) => {
    setProcessingDemande(demande);
    decisionForm.reset({
      statut: "",
      dateDecision: new Date(),
      montantRecupereStr: demande.montantRecupere?.toString().replace('.',',') || "",
      commentairesDecision: "",
      raisonRefus: "",
    });
    setIsDecisionFormOpen(true);
  }, [decisionForm]);

  const onDecisionSubmit = useCallback(async (values: z.infer<typeof DecisionMainleveeSchema>) => {
    if (!processingDemande) return;
    try {
      const response = await fetch(`/api/mainlevees/${processingDemande.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData.message || "Échec du traitement de la demande");
      }
      toast({ title: "Décision Enregistrée", description: `La demande de mainlevée pour ${processingDemande.garantie.referenceGarantie} a été traitée.` });
      setIsDecisionFormOpen(false);
      setProcessingDemande(null);
      fetchDemandes(currentPage);
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [processingDemande, toast, fetchDemandes, decisionForm, currentPage]);

  const columns = useMemo(() => getMainleveeDemandeColumns({ onProcess: handleProcessDemande }), [handleProcessDemande]);

  const decisionStatutSelected = decisionForm.watch('statut');

  if (pageStatus === "loading" && isLoading) return <div className="p-6 text-center">Chargement des demandes...</div>;

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <h1 className="text-3xl font-bold mb-6">Demandes de Mainlevée en Attente</h1>

      <DataTable columns={columns} data={demandes} />
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={fetchDemandes}
        totalRecords={totalRecords}
      />

      {processingDemande && (
        <Dialog open={isDecisionFormOpen} onOpenChange={(open) => {
            if (!open) setProcessingDemande(null);
            setIsDecisionFormOpen(open);
        }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Traiter la Demande de Mainlevée</DialogTitle>
              <DialogDescription>
                Garantie: {processingDemande.garantie.referenceGarantie} <br/>
                Demandeur: {processingDemande.utilisateurCreation?.nomUtilisateur || "N/A"} -
                Type: {processingDemande.typeMainlevee}
              </DialogDescription>
            </DialogHeader>
            <Form {...decisionForm}>
              <form onSubmit={decisionForm.handleSubmit(onDecisionSubmit)} className="space-y-4 py-4">
                <FormField control={decisionForm.control} name="statut" render={({ field }) => (
                    <FormItem><FormLabel>Décision <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} value={field.value ?? ""}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir une décision..." /></SelectTrigger></FormControl>
                            <SelectContent>{decisionStatutOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={decisionForm.control} name="dateDecision" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Date de Décision <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value} onDateChange={field.onChange} /><FormMessage /></FormItem>
                )} />
                {decisionStatutSelected === StatutMainlevee.Refusee && (
                    <FormField control={decisionForm.control} name="raisonRefus" render={({ field }) => (
                        <FormItem><FormLabel>Raison du Refus <span className="text-red-500">*</span></FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>
                    )} />
                )}
                <FormField control={decisionForm.control} name="montantRecupereStr" render={({ field }) => (
                    <FormItem><FormLabel>Montant Récupéré par le Fonds (si applicable)</FormLabel><FormControl><Input type="text" placeholder="0" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={decisionForm.control} name="commentairesDecision" render={({ field }) => (
                    <FormItem><FormLabel>Commentaires sur la Décision</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <DialogFooter className="pt-4">
                    <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                    <Button type="submit" disabled={decisionForm.formState.isSubmitting}>
                        {decisionForm.formState.isSubmitting ? "Enregistrement..." : "Enregistrer la Décision"}
                    </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
} 