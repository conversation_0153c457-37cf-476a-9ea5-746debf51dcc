"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = __importStar(require("bcryptjs"));
const process = __importStar(require("process"));
const prisma = new client_1.PrismaClient();
function createAdminUser() {
    return __awaiter(this, void 0, void 0, function* () {
        const args = process.argv.slice(2);
        if (args.length < 5) {
            console.error("Usage: ts-node scripts/create-admin.ts <username> <email> <password> <firstname> <lastname>");
            process.exit(1);
        }
        const [nomUtilisateur, email, password, prenom, nom] = args;
        try {
            // Vérifier si l'utilisateur existe déjà
            const existingUser = yield prisma.utilisateur.findFirst({
                where: { OR: [{ email }, { nomUtilisateur }] }
            });
            if (existingUser) {
                console.error("Un utilisateur avec cet email ou nom d'utilisateur existe déjà");
                process.exit(1);
            }
            // Hacher le mot de passe
            const hashedPassword = yield bcrypt.hash(password, 10);
            // Créer l'utilisateur admin
            const adminUser = yield prisma.utilisateur.create({
                data: {
                    nomUtilisateur,
                    email,
                    motDePasse: hashedPassword,
                    nom,
                    prenom,
                    role: client_1.RoleUtilisateur.Administrateur,
                },
            });
            console.log("Admin créé avec succès:");
            console.log({
                id: adminUser.id,
                nomUtilisateur: adminUser.nomUtilisateur,
                email: adminUser.email,
                role: adminUser.role,
            });
        }
        catch (error) {
            console.error("Erreur lors de la création de l'admin:", error);
            process.exit(1);
        }
        finally {
            yield prisma.$disconnect();
        }
    });
}
createAdminUser();
