// src/lib/prisma-audit.middleware.ts

if (typeof window !== 'undefined') {
  throw new Error("prisma-audit.middleware ne doit jamais être importé côté client !");
}

import { Prisma } from '@prisma/client';
import { AsyncLocalStorage } from 'async_hooks';

// Ce store permettra de passer l'ID de l'utilisateur au middleware Prisma
// Il doit être configuré au niveau de chaque requête API.
export const auditContext = new AsyncLocalStorage<{ userId?: number; ip?: string; userAgent?: string }>();

// Liste des modèles que nous voulons auditer
// Vous pouvez l'étendre au fur et à mesure
const AUDITED_MODELS: Prisma.ModelName[] = [
  'Utilisateur', 'Bailleur', 'Partenaire', 'SecteurActivite', 'Projet', 'AvenantLigneGarantie', 'AvenantAllocation',
  // NE PAS inclure Account, Session, VerificationToken, User (NextAuth)
];

const WRITE_ACTIONS: Prisma.PrismaAction[] = [
  'create', 'createMany', 'update', 'updateMany', 'upsert', 'delete', 'deleteMany'
];

export function auditLogMiddleware(): Prisma.Middleware {
  return async (params, next) => {
    const isAuditedModel = AUDITED_MODELS.includes(params.model as Prisma.ModelName);
    const isWriteAction = WRITE_ACTIONS.includes(params.action);

    if (!isAuditedModel || !isWriteAction) {
      return next(params);
    }

    const context = auditContext.getStore();

    if (!context || context.userId === undefined) {
      if (params.model !== 'AuditLog') {
        console.warn(
          `[AUDIT MIDDLEWARE] Contexte d'audit (userId) manquant pour ${params.action} sur ${params.model}. ` +
          `L'opération Prisma va continuer, mais le log d'audit sera incomplet ou non créé.`
        );
      }
      return next(params);
    }

    const userId = context.userId;
    const ip = context.ip;
    const userAgent = context.userAgent;

    const { model, action, args } = params;
    let entiteId: string | undefined;
    let nouvellesValeurs: any = null;

    if (action === 'update' || action === 'updateMany' || action === 'upsert') {
      if (args.where?.id) entiteId = String(args.where.id);
      nouvellesValeurs = args.data;
    } else if (action === 'create' || action === 'createMany') {
      nouvellesValeurs = args.data;
    } else if (action === 'delete' || action === 'deleteMany') {
      if (args.where?.id) entiteId = String(args.where.id);
    }

    const result = await next(params);

    if (action === 'create' && result?.id) {
      entiteId = String(result.id);
    }

    if (userId !== undefined && model && params.model !== 'AuditLog') {
      let logAction = action.toUpperCase();
      if (action === 'update' && args.data?.estActif === false) {
        logAction = "DEACTIVATE";
      }

      try {
        const prismaGlobal = (await import('@/lib/prisma')).default;
        await prismaGlobal.auditLog.create({
          data: {
            utilisateurId: userId,
            action: logAction,
            entite: model,
            entiteId: entiteId,
            nouvellesValeurs: nouvellesValeurs ? JSON.parse(JSON.stringify(nouvellesValeurs)) : Prisma.JsonNull,
            adresseIp: ip,
            userAgent: userAgent,
            descriptionAction: `${logAction} on ${model} ${entiteId || ''}`.trim(),
          },
        });
      } catch (auditError) {
        console.error("[AUDIT MIDDLEWARE] Échec de l'écriture du log d'audit:", auditError);
      }
    }
    return result;
  };
}