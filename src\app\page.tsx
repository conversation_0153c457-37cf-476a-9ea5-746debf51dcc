// src/app/page.tsx
"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return; // Attendre que la session soit chargée
    
    if (!session) {
      // Rediriger vers l'écran de connexion si pas connecté
      router.push("/auth/connexion");
    } else {
      // Rediriger vers le dashboard si connecté
      router.push("/dashboard");
    }
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Chargement...</div>
      </div>
    );
  }

  // Cette page ne devrait jamais être affichée car on redirige toujours
  return null;
}