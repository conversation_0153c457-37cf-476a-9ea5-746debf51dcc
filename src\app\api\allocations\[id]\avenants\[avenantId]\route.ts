// src/app/api/allocations/[id]/avenants/[avenantId]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, TypeAvenant, StatutLigneGarantie, StatutAllocation, StatutGarantie } from "@prisma/client";
import { AvenantAllocationSchema } from "@/lib/schemas/avenant-allocation.schema";
import { auditContext } from "@/lib/prisma-audit.middleware";
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams {
  params: {
    id: string;
    avenantId: string;
  };
}

// GET: Récupérer un avenant d'allocation spécifique (pour pré-remplir le formulaire de modification)
export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const avenantIdNum = parseInt(resolvedParams.avenantId);
  const allocationIdNum = parseInt(resolvedParams.id);

  if (isNaN(avenantIdNum) || isNaN(allocationIdNum)) {
    return NextResponse.json({ message: "ID d'avenant ou d'allocation invalide" }, { status: 400 });
  }

  try {
    const avenant = await prisma.avenantAllocation.findUnique({
      where: { id: avenantIdNum, allocationId: allocationIdNum }, // S'assurer qu'il appartient à la bonne allocation
    });
    if (!avenant) return NextResponse.json({ message: "Avenant d'allocation non trouvé" }, { status: 404 });
    return NextResponse.json(avenant);
  } catch (error) {
    console.error(`Erreur GET /api/allocations/.../avenants/${resolvedParams.avenantId}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// PUT: Modifier un avenant d'allocation existant
export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const allocationIdNum = parseInt(resolvedParams.id);
  const avenantIdNum = parseInt(resolvedParams.avenantId);
  if (isNaN(allocationIdNum) || isNaN(avenantIdNum)) {
    return NextResponse.json({ message: "ID d'allocation ou d'avenant invalide." }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  
  // Headers pour audit
  const headersList = await headers();
  const ip = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';

  return auditContext.run({ userId: modifierId, ip, userAgent }, async () => {
    try {
      const body = await request.json();
      if (body.dateAvenant) body.dateAvenant = new Date(body.dateAvenant);
      if (body.nouvelleDateExpiration) body.nouvelleDateExpiration = new Date(body.nouvelleDateExpiration);

      const validation = AvenantAllocationSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données d'avenant invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        typeAvenant: newTypeAvenant,
        montantModificationStr: newMontantModificationStr,
        nouvelleDateExpiration: newNouvelleDateExpiration,
        dateAvenant: newDateAvenant,
        raison: newRaison,
        referenceDocument: newReferenceDocument,
      } = validation.data;

      // --- Début Transaction ---
      const updatedAvenant = await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'avenant existant, l'allocation parente, et la ligne grand-parente
        const avenantExistant = await tx.avenantAllocation.findUnique({ where: { id: avenantIdNum } });
        if (!avenantExistant || avenantExistant.allocationId !== allocationIdNum) {
          throw new Error("Avenant non trouvé ou n'appartient pas à cette allocation.");
        }

        const allocation = await tx.allocationLignePartenaire.findUnique({
          where: { id: allocationIdNum },
          include: {
            ligneGarantie: true,
            garanties: { select: { montantGarantie: true, statut: true } }
          }
        });
        if (!allocation || !allocation.ligneGarantie) throw new Error("Allocation ou Ligne de garantie parente non trouvée.");

        // 2. "Annuler" l'effet de l'avenant existant sur l'allocation et la ligne
        let montantAlloueAllocActuel = new Decimal(allocation.montantAlloue);
        let montantDispoLigneActuel = new Decimal(allocation.ligneGarantie.montantDisponible);

        if (avenantExistant.typeAvenant === TypeAvenant.AUGMENTATION_MONTANT && avenantExistant.montantModification) {
          montantAlloueAllocActuel = montantAlloueAllocActuel.sub(avenantExistant.montantModification);
          montantDispoLigneActuel = montantDispoLigneActuel.add(avenantExistant.montantModification); // Restituer à la ligne
        } else if (avenantExistant.typeAvenant === TypeAvenant.REDUCTION_MONTANT && avenantExistant.montantModification) {
          montantAlloueAllocActuel = montantAlloueAllocActuel.add(avenantExistant.montantModification.abs());
          montantDispoLigneActuel = montantDispoLigneActuel.sub(avenantExistant.montantModification.abs()); // Reprendre de la ligne
        }
        // Annulation de l'effet sur dateExpiration de l'allocation est complexe, géré par la nouvelle valeur.

        // 3. Préparer les données pour la mise à jour avec le NOUVEL avenant
        const allocationUpdateData: any = {};
        const ligneGarantieUpdateData: any = {};
        let newMontantModificationDecimal: Decimal | null = null;

        if (newTypeAvenant === TypeAvenant.AUGMENTATION_MONTANT || newTypeAvenant === TypeAvenant.REDUCTION_MONTANT) {
          if (!newMontantModificationStr) throw new Error("Montant de modification requis.");
          newMontantModificationDecimal = new Decimal(newMontantModificationStr.replace(',', '.'));

          montantAlloueAllocActuel = montantAlloueAllocActuel.add(
            newTypeAvenant === TypeAvenant.AUGMENTATION_MONTANT ? newMontantModificationDecimal : newMontantModificationDecimal.negated()
          );
          if (montantAlloueAllocActuel.isNegative()) throw new Error("Le montant alloué de l'allocation ne peut pas devenir négatif.");
          allocationUpdateData.montantAlloue = montantAlloueAllocActuel;

          // Impact sur la ligne de garantie
          if (newTypeAvenant === TypeAvenant.AUGMENTATION_MONTANT) {
            if (newMontantModificationDecimal.greaterThan(montantDispoLigneActuel)) { // Vérifier sur le dispo ligne *après* annulation effet ancien avenant
              throw new Error(`Augmentation (${newMontantModificationDecimal.toFixed(2)}) dépasse le disponible (${montantDispoLigneActuel.toFixed(2)}) sur la ligne de garantie.`);
            }
            ligneGarantieUpdateData.montantDisponible = montantDispoLigneActuel.sub(newMontantModificationDecimal);
          } else { // REDUCTION_MONTANT sur l'allocation restitue à la ligne
            ligneGarantieUpdateData.montantDisponible = montantDispoLigneActuel.add(newMontantModificationDecimal.abs());
          }
        }

        if (newTypeAvenant === TypeAvenant.PROLONGATION_DUREE && newNouvelleDateExpiration) {
          // Validations de date similaires à la création d'avenant d'allocation
          if (allocation.dateExpiration && newNouvelleDateExpiration <= allocation.dateExpiration && avenantExistant.typeAvenant !== TypeAvenant.PROLONGATION_DUREE) {
            throw new Error("La nouvelle date d'expiration doit être postérieure à la date actuelle.");
          }
          if (newNouvelleDateExpiration <= newDateAvenant) {
            throw new Error("La nouvelle date d'expiration doit être postérieure à la date de l'avenant.");
          }
          if (newNouvelleDateExpiration > allocation.ligneGarantie.dateExpiration) {
            throw new Error("La nouvelle date d'expiration de l'allocation ne peut pas dépasser celle de la ligne de garantie parente.");
          }
          allocationUpdateData.dateExpiration = newNouvelleDateExpiration;
        }

        // 4. Recalculer le montant disponible de l'allocation
        const totalGarantiesActivesSurAllocation = allocation.garanties
          .filter(g => ["Active", "EnSouffrance", "MiseEnJeuDemandee", "MiseEnJeuAcceptee"].includes(g.statut))
          .reduce((sum, g) => sum.add(g.montantGarantie), new Decimal(0));
        const montantAllouePourCalculDispo = allocationUpdateData.montantAlloue || montantAlloueAllocActuel;
        allocationUpdateData.montantDisponible = montantAllouePourCalculDispo.sub(totalGarantiesActivesSurAllocation);
        if (allocationUpdateData.montantDisponible && allocationUpdateData.montantDisponible.isNegative()) {
          console.warn(`Modification d'avenant sur alloc ${allocation.id} résulte en un montant disponible d'allocation négatif: ${allocationUpdateData.montantDisponible}`);
        }

        // 5. Mettre à jour l'avenant d'allocation
        const avenantModifie = await tx.avenantAllocation.update({
          where: { id: avenantIdNum },
          data: {
            typeAvenant: newTypeAvenant as TypeAvenant,
            montantModification: newMontantModificationDecimal,
            nouvelleDateExpiration: newNouvelleDateExpiration,
            dateAvenant: newDateAvenant,
            raison: newRaison,
            referenceDocument: newReferenceDocument,
            utilisateurModificationId: modifierId,
          },
        });

        // 6. Mettre à jour l'allocation parente
        if (Object.keys(allocationUpdateData).length > 0) {
            await tx.allocationLignePartenaire.update({
                where: { id: allocationIdNum },
                data: { ...allocationUpdateData, utilisateurModificationId: modifierId },
            });
        }

        // 7. Mettre à jour la ligne de garantie grand-parente
        if (Object.keys(ligneGarantieUpdateData).length > 0) {
            await tx.ligneGarantie.update({
                where: { id: allocation.ligneGarantieId },
                data: { ...ligneGarantieUpdateData, utilisateurModificationId: modifierId },
            });
        }
        return avenantModifie;
      });
      // --- Fin Transaction ---
      return NextResponse.json(updatedAvenant);

    } catch (error: any) {
      console.error(`Erreur PUT /api/allocations/.../avenants/${avenantIdNum}:`, error);
      if (error.message.includes("non trouvé") || error.message.includes("dépasse") || error.message.includes("négatif") || error.message.includes("postérieure")) {
        return NextResponse.json({ message: error.message }, { status: error.message.includes("non trouvé") ? 404 : 400 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// DELETE: Supprimer un avenant d'allocation
export async function DELETE(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const allocationIdNum = parseInt(resolvedParams.id);
  const avenantIdNum = parseInt(resolvedParams.avenantId);
  
  if (isNaN(allocationIdNum) || isNaN(avenantIdNum)) {
    return NextResponse.json({ message: "ID d'allocation ou d'avenant invalide." }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  
  // Headers pour audit
  const headersList = await headers();
  const ip = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';

  return auditContext.run({ userId: modifierId, ip, userAgent }, async () => {
    try {
      // --- Début Transaction ---
      await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'avenant à supprimer, l'allocation parente, et la ligne grand-parente
        const avenantASupprimer = await tx.avenantAllocation.findUnique({ where: { id: avenantIdNum } });
        if (!avenantASupprimer || avenantASupprimer.allocationId !== allocationIdNum) throw new Error("Avenant non trouvé.");

        const allocation = await tx.allocationLignePartenaire.findUnique({
          where: { id: allocationIdNum },
          include: { ligneGarantie: true, garanties: { select: { montantGarantie: true, statut: true } } }
        });
        if (!allocation || !allocation.ligneGarantie) throw new Error("Allocation ou Ligne parente non trouvée.");

        // 2. "Annuler" l'effet de l'avenant sur l'allocation et la ligne
        const allocationUpdateData: any = {};
        const ligneGarantieUpdateData: any = {};
        let montantAlloueAllocApresAnnulation = new Decimal(allocation.montantAlloue);

        if (avenantASupprimer.typeAvenant === TypeAvenant.AUGMENTATION_MONTANT && avenantASupprimer.montantModification) {
          montantAlloueAllocApresAnnulation = montantAlloueAllocApresAnnulation.sub(avenantASupprimer.montantModification);
          ligneGarantieUpdateData.montantDisponible = allocation.ligneGarantie.montantDisponible.add(avenantASupprimer.montantModification);
        } else if (avenantASupprimer.typeAvenant === TypeAvenant.REDUCTION_MONTANT && avenantASupprimer.montantModification) {
          montantAlloueAllocApresAnnulation = montantAlloueAllocApresAnnulation.add(avenantASupprimer.montantModification.abs());
          ligneGarantieUpdateData.montantDisponible = allocation.ligneGarantie.montantDisponible.sub(avenantASupprimer.montantModification.abs());
          if (ligneGarantieUpdateData.montantDisponible.isNegative()) { // Ne devrait pas arriver si les validations étaient bonnes
              throw new Error("L'annulation de l'avenant rendrait le disponible de la ligne négatif.");
          }
        }
        allocationUpdateData.montantAlloue = montantAlloueAllocApresAnnulation;
        // Gérer l'annulation de l'effet sur dateExpiration de l'allocation est complexe, similaire à la modif.

        // 3. Recalculer le montant disponible de l'allocation
        const totalGarantiesActivesSurAllocation = allocation.garanties
          .filter(g => ["Active", "EnSouffrance", "MiseEnJeuDemandee", "MiseEnJeuAcceptee"].includes(g.statut))
          .reduce((sum, g) => sum.add(g.montantGarantie), new Decimal(0));
        allocationUpdateData.montantDisponible = montantAlloueAllocApresAnnulation.sub(totalGarantiesActivesSurAllocation);
        if (allocationUpdateData.montantDisponible.isNegative()) {
           console.warn(`Suppression d'avenant sur alloc ${allocation.id} résulte en un montant disponible d'allocation négatif: ${allocationUpdateData.montantDisponible}`);
        }

        // 4. Mettre à jour l'allocation parente
        await tx.allocationLignePartenaire.update({
            where: { id: allocationIdNum },
            data: { ...allocationUpdateData, utilisateurModificationId: modifierId },
        });

        // 5. Mettre à jour la ligne de garantie grand-parente
        if (Object.keys(ligneGarantieUpdateData).length > 0) {
            await tx.ligneGarantie.update({
                where: { id: allocation.ligneGarantieId },
                data: { ...ligneGarantieUpdateData, utilisateurModificationId: modifierId },
            });
        }

        // 6. Supprimer l'avenant
        await tx.avenantAllocation.delete({ where: { id: avenantIdNum } });
      });
      // --- Fin Transaction ---
      return NextResponse.json({ message: "Avenant d'allocation supprimé avec succès." }, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur DELETE /api/allocations/.../avenants/${avenantIdNum}:`, error);
      if (error.message.includes("non trouvé") || error.message.includes("négatif")) {
        return NextResponse.json({ message: error.message }, { status: error.message.includes("non trouvé") ? 404 : 400 });
      }
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}